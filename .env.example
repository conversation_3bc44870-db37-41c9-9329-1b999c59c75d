# Development settings
DEVELOPMENT=True
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,smarch-platform.herokuapp.com

# Database settings
# For local development with SQLite, leave this empty
# For PostgreSQL: postgres://user:password@localhost:5432/smarch
DATABASE_URL=

# Secret key (keep this secret in production)
SECRET_KEY=your-secret-key-here

# Stripe settings
STRIPE_PUBLISHABLE_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# AWS settings for static and media files
USE_AWS=False
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name

# Email settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Site URL (used in email templates)
SITE_URL=http://localhost:8000