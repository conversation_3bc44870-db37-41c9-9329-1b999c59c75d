name: Smarch CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: github_actions
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install selenium webdriver-manager

    - name: Set up Chrome
      uses: browser-actions/setup-chrome@latest

    - name: Run unit tests
      env:
        DATABASE_URL: postgres://postgres:postgres@localhost:5432/github_actions
        SECRET_KEY: github_actions_secret_key
        STRIPE_PUBLISHABLE_KEY: pk_test_sample
        STRIPE_SECRET_KEY: sk_test_sample
        STRIPE_WEBHOOK_SECRET: whsec_sample
      run: |
        python manage.py test services.tests

    - name: Run integration tests
      env:
        DATABASE_URL: postgres://postgres:postgres@localhost:5432/github_actions
        SECRET_KEY: github_actions_secret_key
        STRIPE_PUBLISHABLE_KEY: pk_test_sample
        STRIPE_SECRET_KEY: sk_test_sample
        STRIPE_WEBHOOK_SECRET: whsec_sample
      run: |
        python run_integration_tests.py

    - name: Upload test coverage reports
      uses: actions/upload-artifact@v2
      with:
        name: test-coverage
        path: htmlcov/

  deploy:
    needs: test
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Deploy to Heroku
      uses: akhileshns/heroku-deploy@v3.12.12
      with:
        heroku_api_key: ${{ secrets.HEROKU_API_KEY }}
        heroku_app_name: ${{ secrets.HEROKU_APP_NAME }}
        heroku_email: ${{ secrets.HEROKU_EMAIL }}

    - name: Run database migrations
      env:
        HEROKU_API_KEY: ${{ secrets.HEROKU_API_KEY }}
      run: |
        heroku run python manage.py migrate --app ${{ secrets.HEROKU_APP_NAME }}

    - name: Collect static files
      env:
        HEROKU_API_KEY: ${{ secrets.HEROKU_API_KEY }}
      run: |
        heroku run python manage.py collectstatic --noinput --app ${{ secrets.HEROKU_APP_NAME }}

    - name: Send deployment notification
      uses: rtCamp/action-slack-notify@v2
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        SLACK_CHANNEL: deployments
        SLACK_COLOR: good
        SLACK_ICON: https://github.com/rtCamp.png?size=48
        SLACK_MESSAGE: 'Smarch platform deployed to production :rocket:'
        SLACK_TITLE: Deployment Successful
        SLACK_USERNAME: GitHub Actions
