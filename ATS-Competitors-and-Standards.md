# ATS Competitors, Standards, and Platform Comparison

## 1. Major ATS Competitors & How They Operate

**Greenhouse**
- Focus: Advanced ATS and onboarding
- Features: Job posting, structured interviews, DEI tools, reporting, onboarding workflows
- Target: Fast-growing/enterprise
- Pricing: Custom, often $600–$2000+/mo

**Lever**
- Focus: ATS + CRM
- Features: Job posting, candidate management, interview scheduling, analytics, pipeline automation, integrations
- Target: Mid-size/enterprise
- Pricing: Custom, $300–$1000+/mo

**Workable**
- Focus: ATS with sourcing and AI
- Features: Job posting to 200+ boards, sourcing, pipeline, reporting, video interviews
- Target: SMB/mid-market
- Pricing: From $149/mo

**BambooHR**
- Focus: HRIS with lightweight ATS
- Features: Employee records, onboarding, e-signatures, basic ATS
- Target: SMB/mid-market
- Pricing: From ~$100–$200/mo

**SmartRecruiters, iCIMS, Jobvite, SAP SuccessFactors**
- All offer robust enterprise ATS/HR suites with deep integrations, analytics, and compliance tools.

### Key Success Factors Across Competitors
- Deep integrations (HR/payroll, calendars, comms)
- Automation (scheduling, reminders, pipeline)
- Analytics (customizable reports)
- Collaboration (team notes, scorecards)
- Branding (career pages, employer tools)
- Compliance (GDPR, EEOC, DEI)

---

## 2. Platform Comparison & Unique Value

**Your Platform:**
- Core: Connecting applicants with service providers (marketplace angle)
- Features: User profiles, application forms, subscriptions, contact requests, blog, events, insights, Stripe payments
- Strengths:
  - Modular Django design
  - Stripe integration
  - Clear user type separation
  - Early analytics focus
  - Community and content (blog/events)
  - Transparent pricing
- Gaps (vs. competitors):
  - Integrations (HRIS, Slack, calendars) not yet present
  - Automation/collaboration features in progress
  - Branding tools, compliance tools to be built

**Recommendations:**
- Lean into marketplace/community angle
- Focus on easy analytics for SMBs
- Build integrations over time (start with calendar/email)
- Keep pricing simple and transparent
- Use customer feedback to shape roadmap

---

## 3. ATS Standards for Job Offers (Job Postings)

### Schema.org JobPosting
- The most widely adopted standard for job offers.
- Used by Google for Jobs, LinkedIn, and job boards/ATSs.
- **Key Fields:**
  - `title`, `description`, `datePosted`, `employmentType`, `hiringOrganization`, `jobLocation`, `baseSalary`, `qualifications`, `responsibilities`, `validThrough`
- **Format:** JSON-LD or HTML microdata
- **Docs:** [Schema.org/JobPosting](https://schema.org/JobPosting)

### HR-XML/HR-JSON
- Used for enterprise data exchange
- More complex, supports full job/applicant data
- **Docs:** [HR Open Standards](https://www.hropenstandards.org/)

---

## 4. Job Application Standards
- **No universal standard.**
- Each ATS uses custom forms and fields
- Some use HR-XML/HR-JSON for integrations, but inconsistently
- You can design your own application process

---

## 5. Best Practices for Job Offer Design
- Follow Schema.org JobPosting for compatibility & SEO
- Use clear, structured fields for job data
- Include all relevant info (title, location, salary, description, qualifications)
- Make job offers machine-readable (JSON-LD markup)
- If integrating with enterprise clients, consider HR-XML/HR-JSON as optional export/import

---

## 6. Integration Considerations
- Most major ATSs offer APIs for job/candidate data (REST, webhooks)
- Data mapping may be required; not all fields match 1:1
- Some ATSs require partnership or paid API access
- Focus on the most popular ATSs first (Greenhouse, Lever, Workable, SmartRecruiters)

---

## 7. Useful Resources
- [Schema.org JobPosting Documentation](https://schema.org/JobPosting)
- [HR Open Standards](https://www.hropenstandards.org/)
- [Google for Jobs Guidelines](https://developers.google.com/search/docs/appearance/structured-data/job-posting)

---

**Summary:**
- Use Schema.org JobPosting for job offers
- Design your own job application process
- Integrate with popular ATSs via their APIs
- Reference competitors for feature inspiration and integration priorities

## 2. ATS Standards for Job Offers (Job Postings)

### **Schema.org JobPosting**
- The most widely adopted standard for job offers.
- Used by Google for Jobs, LinkedIn, and many job boards/ATSs.
- **Key Fields:**
  - `title`
  - `description`
  - `datePosted`
  - `employmentType`
  - `hiringOrganization`
  - `jobLocation`
  - `baseSalary`
  - `qualifications`
  - `responsibilities`
  - `validThrough`
- **Format:** JSON-LD or HTML microdata.
- **Documentation:** [Schema.org/JobPosting](https://schema.org/JobPosting)

### **HR-XML/HR-JSON**
- Used for enterprise data exchange.
- More complex, but supports full job posting and application data.
- **Documentation:** [HR Open Standards](https://www.hropenstandards.org/)

## 3. Job Application Standards
- **No universal standard.**
- Each ATS collects different data and uses custom forms.
- Some use HR-XML/HR-JSON for integrations, but this is not consistent.
- **You can design your own application process.**

## 4. Best Practices for Job Offer Design
- **Follow Schema.org JobPosting** for maximum compatibility and SEO.
- Use clear, structured fields for job data.
- Include all relevant information (title, location, salary, description, qualifications, etc.).
- Make job offers machine-readable (JSON-LD markup on web pages).
- If integrating with enterprise clients, consider supporting HR-XML/HR-JSON as an optional export/import.

## 5. Integration Considerations
- Most major ATSs offer APIs for job and candidate data (REST, webhooks, etc.).
- Data mapping may be required; not all fields match 1:1.
- Some ATSs require partnership or paid access for API use.
- Focus on the most popular ATSs first (Greenhouse, Lever, Workable, SmartRecruiters).

## 6. Useful Resources
- [Schema.org JobPosting Documentation](https://schema.org/JobPosting)
- [HR Open Standards](https://www.hropenstandards.org/)
- [Google for Jobs Guidelines](https://developers.google.com/search/docs/appearance/structured-data/job-posting)

---

**Summary:**
- Use Schema.org JobPosting for job offers.
- Design your own job application process.
- Integrate with popular ATSs via their APIs for maximum reach.
- Reference competitors for feature inspiration and integration priorities.
