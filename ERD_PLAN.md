# Smarch Application - Entity-Relationship Diagram (ERD) Plan

## Overview
This document outlines the database schema design for the Smarch application, including models, fields, and relationships between them.

## Django Apps and Models

### 1. Users App
Handles user management for all types of users in the system.

#### 1.1 `CustomUser` Model
Extends Django's AbstractUser to handle all user types.
- `id` (PK): Auto-incrementing ID
- `email` (CharField): User's email (unique)
- `username` (CharField): Username (unique)
- `first_name` (CharField): User's first name
- `last_name` (CharField): User's last name
- `user_type` (CharField): Choices - 'applicant', 'service', 'staff'
- `date_joined` (DateTimeField): When the user joined
- `is_active` (BooleanField): Whether the user is active
- `is_staff` (BooleanField): Whether the user is staff

#### 1.2 `ApplicantProfile` Model
Profile for applicant users.
- `user` (FK → CustomUser): One-to-one relationship with CustomUser
- `image` (ImageField): Profile image
- `bio` (TextField): Short biography
- `case_number` (CharField): Unique case number (automatically assigned)
- `queue_number` (IntegerField): Queue position number (automatically assigned)
- `status` (CharField): Choices - 'pending', 'in_progress', 'completed'
- `assignation` (CharField): Assignment information
- `created_at` (DateTimeField): When the profile was created
- `updated_at` (DateTimeField): When the profile was last updated

#### 1.3 `ServiceUserProfile` Model
Profile for service users (companies).
- `user` (FK → CustomUser): One-to-one relationship with CustomUser
- `company_name` (CharField): Name of the company
- `company_size` (CharField): Size of the company
- `company_location` (CharField): Main location of the company
- `other_locations` (TextField): Additional locations, stored as JSON
- `company_branch` (CharField): Industry branch
- `industry` (CharField): Specific industry type
- `company_needs` (TextField): Types of applicants needed
- `average_wages` (CharField): Average salary ranges
- `contract_types` (CharField): Available contract types
- `working_hours` (CharField): Working hours information
- `work_location` (CharField): Remote or office-based
- `created_at` (DateTimeField): When the profile was created
- `updated_at` (DateTimeField): When the profile was last updated

### 2. Application App
Handles the application form and related data.

#### 2.1 `ApplicationForm` Model
Represents the application form submitted by applicants.
- `id` (PK): Auto-incrementing ID
- `applicant` (FK → ApplicantProfile): Foreign key to the applicant
- `form_data` (JSONField): Form answers in JSON format
- `submitted_at` (DateTimeField): When the form was submitted
- `is_complete` (BooleanField): Whether the form is complete

#### 2.2 `ApplicationQuestion` Model
Represents questions on the application form.
- `id` (PK): Auto-incrementing ID
- `question_text` (TextField): The text of the question
- `question_type` (CharField): Choices - 'open', 'closed', 'multichoice'
- `options` (JSONField): Options for multichoice questions
- `required` (BooleanField): Whether the question is required
- `order` (IntegerField): The order in which the question appears
- `category` (CharField): The category of the question

#### 2.3 `RefinedInfo` Model
Information extracted and refined from raw application form data.
- `application` (FK → ApplicationForm): Foreign key to the application form
- `branch` (CharField): Industry branch
- `age` (IntegerField): Applicant's age
- `location` (CharField): Applicant's location
- `industry` (CharField): Industry preference
- `skills` (JSONField): List of skills
- `role` (CharField): Preferred role/position
- `experience_years` (IntegerField): Years of experience
- `gender` (CharField): Applicant's gender
- `work_permit` (BooleanField): Whether the applicant has a work permit
- `languages` (JSONField): List of languages spoken

### 3. Services App
Handles service subscriptions and related data.

#### 3.1 `ServiceOffer` Model
Represents different service offerings.
- `id` (PK): Auto-incrementing ID
- `name` (CharField): Name of the offer
- `description` (TextField): Description of the offer
- `price` (DecimalField): Monthly price
- `features` (JSONField): List of features
- `is_premium` (BooleanField): Whether this is a premium offering

#### 3.2 `Subscription` Model
Represents a user's subscription to a service.
- `id` (PK): Auto-incrementing ID
- `service_user` (FK → ServiceUserProfile): Foreign key to the service user
- `service_offer` (FK → ServiceOffer): Foreign key to the service offer
- `start_date` (DateField): Start date of subscription
- `end_date` (DateField): End date of subscription
- `stripe_subscription_id` (CharField): Stripe subscription ID
- `is_active` (BooleanField): Whether the subscription is active

#### 3.3 `ServiceUserSearch` Model
Tracks service user search activity.
- `service_user` (FK → ServiceUserProfile): Foreign key to the service user
- `search_query` (JSONField): Search parameters used
- `timestamp` (DateTimeField): When the search was performed
- `search_results` (ManyToManyField → ApplicantProfile): Applicants found

#### 3.4 `SavedResult` Model
Saved search results by service users.
- `service_user` (FK → ServiceUserProfile): Foreign key to the service user
- `applicant` (FK → ApplicantProfile): Foreign key to the applicant
- `notes` (TextField): Notes about the saved applicant
- `saved_at` (DateTimeField): When the result was saved

### 4. Contact App
Handles contact requests and related data.

#### 4.1 `ContactRequest` Model
Represents contact requests between service users and applicants.
- `id` (PK): Auto-incrementing ID
- `sender` (FK → ServiceUserProfile): Foreign key to the service user
- `recipient` (FK → ApplicantProfile): Foreign key to the applicant
- `message` (TextField): Message content
- `calendly_link` (URLField): Link to Calendly or similar service
- `status` (CharField): Choices - 'pending', 'accepted', 'declined'
- `sent_at` (DateTimeField): When the request was sent
- `responded_at` (DateTimeField): When the request was responded to

#### 4.2 `Offer` Model
Represents job offers from service users to applicants.
- `id` (PK): Auto-incrementing ID
- `contact_request` (FK → ContactRequest): Foreign key to the contact request
- `position` (CharField): Job position offered
- `details` (TextField): Details of the offer
- `status` (CharField): Choices - 'pending', 'accepted', 'declined'
- `created_at` (DateTimeField): When the offer was created
- `responded_at` (DateTimeField): When the offer was responded to

### 5. Blog App
Handles blog posts and related data.

#### 5.1 `BlogPost` Model
Represents blog posts.
- `id` (PK): Auto-incrementing ID
- `title` (CharField): Title of the post
- `content` (TextField): Content of the post
- `image` (ImageField): Featured image
- `author` (FK → CustomUser): Foreign key to the author
- `published_at` (DateTimeField): When the post was published
- `is_published` (BooleanField): Whether the post is published
- `slug` (SlugField): URL slug
- `categories` (ManyToManyField → Category): Categories the post belongs to

#### 5.2 `Category` Model
Represents blog post categories.
- `id` (PK): Auto-incrementing ID
- `name` (CharField): Name of the category
- `slug` (SlugField): URL slug

#### 5.3 `Comment` Model
Represents comments on blog posts.
- `id` (PK): Auto-incrementing ID
- `post` (FK → BlogPost): Foreign key to the blog post
- `author` (FK → CustomUser): Foreign key to the author
- `content` (TextField): Content of the comment
- `created_at` (DateTimeField): When the comment was created
- `is_approved` (BooleanField): Whether the comment is approved

### 6. Events App
Handles event scheduling and tracking.

#### 6.1 `Event` Model
Represents calendar events.
- `id` (PK): Auto-incrementing ID
- `title` (CharField): Title of the event
- `description` (TextField): Description of the event
- `start_time` (DateTimeField): Start time of the event
- `end_time` (DateTimeField): End time of the event
- `participants` (ManyToManyField → CustomUser): Users participating in the event
- `location` (CharField): Location of the event
- `related_contact_request` (FK → ContactRequest): Optional link to a contact request
- `created_by` (FK → CustomUser): User who created the event
- `created_at` (DateTimeField): When the event was created

### 7. Insights App
Handles analytics and insights for premium service users.

#### 7.1 `ServiceUserInsight` Model
Analytics data for service users.
- `id` (PK): Auto-incrementing ID
- `service_user` (FK → ServiceUserProfile): Foreign key to the service user
- `data` (JSONField): Insight data in JSON format
- `generated_at` (DateTimeField): When the insights were generated

#### 7.2 `SearchAnalytics` Model
Analytics about search behavior.
- `id` (PK): Auto-incrementing ID
- `service_user` (FK → ServiceUserProfile): Foreign key to the service user
- `total_searches` (IntegerField): Total number of searches
- `successful_matches` (IntegerField): Number of successful matches
- `no_match_phrases` (JSONField): Search phrases with no matches
- `popular_categories` (JSONField): Most popular search categories
- `time_period` (CharField): Time period for analytics

### 8. Staff App
Handles staff-specific functionality.

#### 8.1 `StaffAssignment` Model
Tracks staff assignments to applicants.
- `id` (PK): Auto-incrementing ID
- `staff_user` (FK → CustomUser): Foreign key to the staff user
- `applicant` (FK → ApplicantProfile): Foreign key to the applicant
- `status` (CharField): Choices - 'open', 'in_progress', 'completed'
- `notes` (TextField): Staff notes
- `assigned_at` (DateTimeField): When the assignment was created
- `updated_at` (DateTimeField): When the assignment was last updated

## Relationships Summary

1. **One-to-One Relationships**:
   - CustomUser → ApplicantProfile
   - CustomUser → ServiceUserProfile

2. **One-to-Many Relationships**:
   - ApplicantProfile → ApplicationForm
   - ApplicationForm → RefinedInfo
   - ServiceUserProfile → Subscription
   - ServiceUserProfile → ServiceUserSearch
   - ServiceUserProfile → ContactRequest
   - CustomUser → BlogPost
   - BlogPost → Comment
   - ServiceUserProfile → ServiceUserInsight
   - ServiceUserProfile → SearchAnalytics
   - CustomUser → StaffAssignment

3. **Many-to-Many Relationships**:
   - BlogPost ↔ Category
   - ServiceUserSearch ↔ ApplicantProfile
   - Event ↔ CustomUser

## Database Decisions and Considerations

1. User Management:
   - Using Django's built-in authentication with a custom user model
   - Separate profiles for different user types rather than inheritance
   - Clear separation between raw form data and refined information

2. Form Handling:
   - Storing form data as JSON for flexibility
   - Separation of concerns between raw data collection and processed info

3. Search and Matching:
   - Tracking search queries to enable analytics
   - Saving search results to enable quick access
   - Using JSONField for flexible search parameters

4. Contact and Communications:
   - Clear workflow for contact requests → meetings → offers
   - Status tracking for all communication stages

5. Analytics:
   - Separate models for different types of analytics
   - Flexible JSON storage for various metrics

This ERD plan provides a comprehensive starting point for building the Smarch application. As development progresses, additional models or modifications may be necessary. 