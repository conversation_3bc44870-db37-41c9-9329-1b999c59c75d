Here’s a concise overview of how Lever, Greenhouse, BambooHR, and Workable operate, plus an honest assessment of how your platform compares and where it could stand out:

1. How the Competitors Operate
Lever
Focus: Talent acquisition suite (ATS + CRM).
Core Features: Job posting, candidate management, interview scheduling, analytics, integrations with HRIS/Slack/Zoom, pipeline automation, collaborative hiring.
Target Market: Mid-size to enterprise; tech-forward companies.
Pricing: Not public—custom quotes, typically $300–$1000+/month.
Greenhouse
Focus: Advanced ATS and onboarding.
Core Features: Job posting, structured interview kits, scorecards, DEI tools, reporting, integrations, onboarding workflows.
Target Market: Fast-growing companies, enterprise.
Pricing: Not public—custom quotes, often $600–$2000+/month.
BambooHR
Focus: HRIS with lightweight ATS.
Core Features: Employee records, PTO, onboarding, e-signatures, basic applicant tracking, performance management.
Target Market: SMBs and mid-market.
Pricing: Not public—starts ~$100–$200/month, scales with features and employee count.
Workable
Focus: ATS with sourcing and AI features.
Core Features: Job posting to 200+ boards, candidate sourcing, pipeline management, interview scheduling, reporting, video interviews.
Target Market: SMB to mid-market.
Pricing: Starts at ~$149/month, higher tiers for more jobs/users.
2. What Makes Them Successful?
Deep Integrations: With HR/payroll, calendars, comms tools.
Automation: Scheduling, reminders, pipeline movement.
Analytics: Powerful, customizable reports.
Collaboration: Team notes, scorecards, feedback.
Branding: Customizable career pages, employer branding tools.
Compliance: GDPR, EEOC, DEI features.
3. How Does Your Platform Compare? (Current State)
Your Platform:
Core: Connecting applicants with service providers (hiring partners).
Features: User profiles, application forms, subscriptions, contact requests, blog, events, insights, Stripe payments.
Current Strengths:
Modular Django design (easy to expand).
Stripe integration for monetization.
Clear separation of user types (applicant, service, staff).
Early focus on business insights and analytics (planned).
Potential Spark of Difference:
B2B Marketplace Angle: If you’re enabling direct connections between applicants and service providers (not just internal hiring), this is more “marketplace” than traditional ATS.
Insights/Analytics for SMBs: If you deliver actionable insights to smaller businesses (not just big companies), that’s a gap in the market.
Integrated Blog/Content: If your blog/events drive community and education, this adds value beyond pure hiring.
Simple, Transparent Pricing: Many competitors are opaque—your clear plans could appeal to SMBs/startups.
Customization: If your platform is more customizable for niche industries or workflows, that’s a differentiator.
Current Gaps (vs. Competitors):
Integrations: Not yet present (e.g., with HRIS, Slack, calendars).
Automation: Scheduling, reminders, pipeline movement not yet built.
Collaboration: Team feedback, scorecards, etc., not yet implemented.
Branding/Customization: Career pages, branding tools not yet present.
Compliance Tools: DEI, GDPR, reporting not yet built.
4. Recommendations for Standing Out
Lean into the Marketplace/Community Angle: If you connect multiple businesses and applicants, highlight this.
Focus on Insights: Deliver easy-to-understand analytics for SMBs.
Transparent Pricing and Simplicity: Make onboarding easy.
Build Integrations Over Time: Start with calendar/email, then HR/payroll.
Listen to Early Customers: Let their feedback shape your roadmap.
Summary:
Your platform has a solid foundation and a few promising differentiators, especially if you focus on the marketplace/community and insights for smaller businesses. To truly stand out, keep building unique features and listen to user feedback. If you want a more detailed competitor matrix or feature roadmap, let me know!

Feedback submitted
Generating.
