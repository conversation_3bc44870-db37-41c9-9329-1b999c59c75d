# Smarch Project Canvas

## Project Overview
Smarch is a platform connecting applicants with service providers. It features robust user profiles, application management, service subscriptions, contact requests, and analytics.

## Django Apps Structure

### 1. Users App
- **Purpose**: User authentication and profiles management
- **Models**: CustomUser, ApplicantProfile, ServiceUserProfile
- **Views**: Registration, login, profile management
- **Pages**: Profile pages for both applicant and service users

### 2. Application App
- **Purpose**: Handle application forms and processing
- **Models**: ApplicationForm, ApplicationQuestion, RefinedInfo
- **Views**: Form submission, form builder, application management
- **Pages**: Apply page, form view page

### 3. Services App
- **Purpose**: Service subscriptions and search functionality
- **Models**: ServiceOffer, Subscription, ServiceUserSearch, SavedResult
- **Views**: Services overview, subscription management, search functionality
- **Pages**: Services page, browser page, search results page

### 4. Contact App
- **Purpose**: Contact requests and offer management
- **Models**: ContactRequest, Offer
- **Views**: Contact request management, offer handling
- **Pages**: Contact requests page (sent/received)

### 5. Blog App
- **Purpose**: Content management for the blog
- **Models**: BlogPost, Category, Comment
- **Views**: Blog management, post creation/editing, commenting
- **Pages**: Blog list page, blog post detail page

### 6. Events App
- **Purpose**: Calendar and event management
- **Models**: Event
- **Views**: Event creation, event listing
- **Pages**: Calendar/events page

### 7. Insights App
- **Purpose**: Analytics for premium service users
- **Models**: ServiceUserInsight, SearchAnalytics
- **Views**: Insights dashboard, analytics reports
- **Pages**: Smarch Insights page

### 8. Staff App
- **Purpose**: Staff-specific functionality
- **Models**: StaffAssignment
- **Views**: Staff dashboard, applicant management
- **Pages**: Staff dashboard, "Smarch them" panel

## Static Pages
1. **Home Page**: 
   - Carousel with information
   - "What is Smarch" section
   - Latest news cards

2. **About Page**:
   - Information about Smarch
   - Mission and goals
   - Values
   - Collaboration form

## Deployment Plan
- Backend: Django with PostgreSQL (Heroku)
- Frontend: Bootstrap for responsive design
- Storage: AWS S3 for media files
- Payment processing: Stripe
- Authentication: django-allauth

## Development Phases
1. **Phase 1**: Basic site structure, user authentication, profiles
2. **Phase 2**: Application form, services section, contact system
3. **Phase 3**: Blog, events, basic search functionality
4. **Phase 4**: Insights, staff functionality, advanced search
5. **Phase 5**: "Smarch them" AI functionality

## Project Summary
The Smarch application consists of 8 Django apps with a total of 19 models. The database design focuses on flexibility and scalability, with clear separation between different user types and functionalities. The application will feature both dynamic content (managed through Django admin) and static pages. 