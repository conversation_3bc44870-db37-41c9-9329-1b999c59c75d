# Project Overview

## What is Smarch?
Smarch is a platform connecting applicants with service providers. It features robust user profiles, application management, service subscriptions, contact requests, and analytics.

## Django Apps Structure
- Users
- Application
- Services
- Contact
- Blog
- Events
- Insights
- Staff

## Current Status
- Project structure and core models implemented
- Basic UI and authentication in place
- Ongoing work on forms, business logic, and integrations

## Development Phases
1. Phase 1: Basic site structure, user authentication, profiles
2. Phase 2: Application form, services section, contact system
3. Phase 3: Blog, events, basic search functionality
4. Phase 4: Insights, staff functionality, advanced search
5. Phase 5: "Smarch them" AI functionality

## Deployment Plan
- Backend: Django with PostgreSQL (Heroku)
- Frontend: Bootstrap for responsive design
- Storage: AWS S3 for media files
- Payment processing: Stripe
- Authentication: django-allauth
