# Smarch

Smarch is a platform connecting applicants with service providers. It features application management, service subscriptions, and insights for businesses.

---

## 📑 Index

1. [Project Overview](./Project-Overview.md)
2. [Development Roadmap](./Development-Roadmap.md)
3. [ATS Competitors & Standards](./ATS-Competitors-and-Standards.md)
4. [Database Design (ERD)](./ERD_PLAN.md)
5. [Subscription Plans](./SUBSCRIPTION_PLANS.md)
6. [Installation & Setup](#installation)
7. [Deployment](#deployment)

---

## Features

- User profiles for applicants and service providers
- Application forms with 30+ questions
- Service subscriptions with Stripe integration
- Contact request system
- Blog functionality
- Staff dashboard for application management
- Premium insights for businesses

## Technology Stack

- Django 4.2
- Bootstrap 5
- PostgreSQL (Heroku)
- AWS S3 for storage
- Stripe for payments

## Installation

1. Clone the repository
2. Create a virtual environment: `python -m venv venv`
3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Unix/MacOS: `source venv/bin/activate`
4. Install dependencies: `pip install -r requirements.txt`
5. Create a `.env` file based on `.env.example` with your configuration
6. Run migrations: `python manage.py migrate`
7. Create a superuser: `python manage.py createsuperuser`
8. Start the development server: `python manage.py runserver`

## Deployment

The application is configured for deployment to Heroku with PostgreSQL database:

1. Create a new Heroku app
   ```
   heroku create smarch-platform
   ```

2. Add PostgreSQL add-on
   ```
   heroku addons:create heroku-postgresql:mini
   ```

3. Configure environment variables in Heroku settings:
   - SECRET_KEY: Your Django secret key
   - DEBUG: False
   - ALLOWED_HOSTS: smarch-platform.herokuapp.com
   - DATABASE_URL: (automatically set by Heroku PostgreSQL)
   - STRIPE_PUBLIC_KEY: Your Stripe public key
   - STRIPE_SECRET_KEY: Your Stripe secret key
   - STRIPE_WEBHOOK_SECRET: Your Stripe webhook secret
   - USE_AWS: True
   - AWS_ACCESS_KEY_ID: Your AWS access key
   - AWS_SECRET_ACCESS_KEY: Your AWS secret key
   - AWS_STORAGE_BUCKET_NAME: Your S3 bucket name
   - EMAIL_HOST: Your email host (e.g., smtp.gmail.com)
   - EMAIL_PORT: Your email port (e.g., 587)
   - EMAIL_HOST_USER: Your email address
   - EMAIL_HOST_PASSWORD: Your email password or app password
   - DEFAULT_FROM_EMAIL: Your default sender email

4. Connect your GitHub repository for continuous deployment:
   - In Heroku dashboard, go to the "Deploy" tab
   - Choose "GitHub" as the deployment method
   - Connect to your GitHub repository
   - Enable automatic deploys from your main branch

5. Deploy the application:
   ```
   git push heroku main
   ```

6. Run migrations on Heroku:
   ```
   heroku run python manage.py migrate
   ```

7. Create a superuser on Heroku:
   ```
   heroku run python manage.py createsuperuser
   ```

8. Set up Stripe webhooks for the production environment:
   - Go to the Stripe Dashboard
   - Navigate to Developers > Webhooks
   - Add a new endpoint: https://smarch-platform.herokuapp.com/services/webhook/
   - Select events to listen for (checkout.session.completed, etc.)
   - Save the webhook secret in your Heroku environment variables