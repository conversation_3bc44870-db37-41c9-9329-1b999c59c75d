# SUBSCRIPTION_PLANS.md

## Subscription Tiers & Features

| Feature / Limit                  | Basic        | Standard     | Premium      | Enterprise         |
|----------------------------------|--------------|--------------|--------------|--------------------|
| Monthly Price                    | $99          | $199         | $299         | Contact Us         |
| Number of Active Job Posts       | 3            | 10           | 25           | Unlimited / Custom |
| Number of Applicant Views        | 100/mo       | 500/mo       | 2000/mo      | Unlimited / Custom |
| Team Members                     | 1            | 5            | 20           | Unlimited          |
| Advanced Analytics               | No           | Yes          | Yes          | Yes                |
| API Access                       | No           | No           | Yes          | Yes                |
| Priority Support                 | No           | No           | Yes          | Dedicated Manager  |
| Custom Integrations              | No           | No           | No           | Yes                |
| White-labeling                   | No           | No           | No           | Yes                |
| Contact-Requests                 | 3            | 10           | 25           | Unlimited / Custom |
| Blog Posts                       | 3            | 10           | 25           | Unlimited / Custom |
| Events                           | 3            | 10           | 25           | Unlimited / Custom |

---

## Enforcement Plan (Code Suggestions)

- Store the user’s subscription plan in the `Subscription` model (`plan` field).
- On key actions (e.g., posting a job, viewing applicants, inviting team members), check the user’s plan and enforce limits.
- For limits (e.g., job posts), count current usage and compare to plan cap.
- For feature access (e.g., analytics, API), use decorators or permission checks in views.

**Example (Pseudo-code):**
```python
def can_post_job(user):
    max_posts = PLAN_LIMITS[user.subscription.plan]['job_posts']
    current_posts = Job.objects.filter(owner=user).count()
    return current_posts < max_posts
```

---

## Implementation Notes

- “Enterprise” plan pricing and features should be handled manually or with custom logic.
- Stripe integration: Store plan IDs/prices in Stripe dashboard, reference them in code for billing.
- Consider adding grace periods, trial periods, and upgrade/downgrade logic.
- Adjust features/limits as your product and user feedback evolve.

---

## Future Adjustments

- Update this file as plans, features, or pricing change.
- Document any business rules or exceptions for each plan here for future developers and product managers.
