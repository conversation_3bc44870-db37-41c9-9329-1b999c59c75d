from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import ApplicantProfile, ApplicationFormResponse, RefinedInfo


@admin.register(ApplicantProfile)
class ApplicantProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'case_number', 'queue_number', 'location', 'industry', 'created_at')
    list_filter = ('industry', 'work_permit')
    search_fields = ('user__username', 'user__email', 'case_number', 'industry', 'location')
    readonly_fields = ('case_number', 'queue_number', 'created_at', 'updated_at')
    fieldsets = (
        ('User Information', {
            'fields': ('user', 'image', 'bio')
        }),
        ('Personal Details', {
            'fields': ('date_of_birth', 'age', 'gender', 'phone_number')
        }),
        ('Location', {
            'fields': ('address', 'city', 'state', 'zip_code', 'location')
        }),
        ('Professional Information', {
            'fields': ('branch', 'industry', 'role', 'position_experience_years',
                      'work_permit', 'languages')
        }),
        ('System Information', {
            'fields': ('case_number', 'queue_number', 'created_at', 'updated_at')
        }),
    )


class RefinedInfoInline(admin.StackedInline):
    model = RefinedInfo
    can_delete = False
    verbose_name_plural = 'Refined Information'
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('full_name', 'location', 'age')
        }),
        ('Professional Information', {
            'fields': ('industry', 'branch', 'role', 'experience_years')
        }),
        ('Skills and Languages', {
            'fields': ('skills', 'languages')
        }),
        ('Work Preferences', {
            'fields': ('work_preferences', 'work_permit')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(ApplicationFormResponse)
class ApplicationFormResponseAdmin(admin.ModelAdmin):
    list_display = ('applicant', 'status', 'submission_date', 'review_date', 'submitted_at', 'has_refined_info')
    list_filter = ('status', 'submission_date', 'review_date')
    search_fields = ('applicant__user__username', 'applicant__user__email',
                    'applicant__case_number')
    readonly_fields = ('submitted_at', 'updated_at', 'applicant_link')
    inlines = [RefinedInfoInline]
    actions = ['process_applications', 'mark_as_in_review', 'mark_as_approved', 'mark_as_rejected']

    fieldsets = (
        ('Applicant Information', {
            'fields': ('applicant', 'applicant_link')
        }),
        ('Form Data', {
            'fields': ('raw_data',)
        }),
        ('Status Information', {
            'fields': ('status', 'submission_date', 'review_date', 'reviewed_by', 'notes')
        }),
        ('System Information', {
            'fields': ('submitted_at', 'updated_at')
        }),
    )

    def has_refined_info(self, obj):
        """Check if the application has refined info."""
        try:
            return bool(obj.refinedinfo)
        except RefinedInfo.DoesNotExist:
            return False
    has_refined_info.boolean = True
    has_refined_info.short_description = 'Processed'

    def applicant_link(self, obj):
        """Link to the applicant profile."""
        if obj.applicant:
            url = reverse('admin:users_applicantprofile_change', args=[obj.applicant.id])
            return format_html('<a href="{}">View Applicant Profile</a>', url)
        return '-'
    applicant_link.short_description = 'Applicant Profile'

    def process_applications(self, request, queryset):
        """Process selected applications to create RefinedInfo instances."""
        count = 0
        for application in queryset:
            if application.status == 'submitted':
                refined_info = application.process_application()
                if refined_info:
                    count += 1
        self.message_user(request, f'{count} applications processed successfully.')
    process_applications.short_description = 'Process selected applications'

    def mark_as_in_review(self, request, queryset):
        """Mark selected applications as in review."""
        queryset.update(status='in_review', review_date=timezone.now(), reviewed_by=request.user)
        self.message_user(request, f'{queryset.count()} applications marked as in review.')
    mark_as_in_review.short_description = 'Mark selected applications as in review'

    def mark_as_approved(self, request, queryset):
        """Mark selected applications as approved."""
        queryset.update(status='approved', review_date=timezone.now(), reviewed_by=request.user)
        self.message_user(request, f'{queryset.count()} applications marked as approved.')
    mark_as_approved.short_description = 'Mark selected applications as approved'

    def mark_as_rejected(self, request, queryset):
        """Mark selected applications as rejected."""
        queryset.update(status='rejected', review_date=timezone.now(), reviewed_by=request.user)
        self.message_user(request, f'{queryset.count()} applications marked as rejected.')
    mark_as_rejected.short_description = 'Mark selected applications as rejected'


@admin.register(RefinedInfo)
class RefinedInfoAdmin(admin.ModelAdmin):
    list_display = ('application', 'full_name', 'industry', 'role', 'experience_years', 'created_at')
    list_filter = ('industry', 'work_permit')
    search_fields = ('full_name', 'industry', 'role')
    readonly_fields = ('application', 'created_at', 'updated_at')
    fieldsets = (
        ('Application', {
            'fields': ('application',)
        }),
        ('Basic Information', {
            'fields': ('full_name', 'location', 'age')
        }),
        ('Professional Information', {
            'fields': ('industry', 'branch', 'role', 'experience_years')
        }),
        ('Skills and Languages', {
            'fields': ('skills', 'languages')
        }),
        ('Work Preferences', {
            'fields': ('work_preferences', 'work_permit')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at')
        }),
    )
