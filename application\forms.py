from django import forms
from .models import ApplicantProfile, ApplicationFormResponse


class ApplicantProfileForm(forms.ModelForm):
    """Form for creating or updating an applicant profile."""
    class Meta:
        model = ApplicantProfile
        fields = [
            'image', 'bio', 'branch', 'age', 'date_of_birth', 'location',
            'industry', 'role', 'position_experience_years', 'gender',
            'work_permit', 'languages', 'phone_number', 'address', 'city',
            'state', 'zip_code'
        ]
        widgets = {
            'date_of_birth': forms.DateInput(attrs={'type': 'date'}),
            'bio': forms.Textarea(attrs={'rows': 4}),
            'languages': forms.TextInput(attrs={'placeholder': 'e.g., English, Spanish, French'}),
        }


class DreamJobApplicationForm(forms.Form):
    """SMARCH Dream Job & Career Aspirations Survey form."""
    # Section 1: Basic Information
    full_name = forms.CharField(
        max_length=100,
        label="Full Name",
        help_text="Please provide your full name."
    )
    date_of_birth = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        label="Date of Birth",
        help_text="Please provide your birth date."
    )
    current_location = forms.ChoiceField(
        choices=[
            ('stockholm', 'Stockholm'),
            ('remote', 'Remote (working from home or another location in Sweden)'),
            ('other', 'Other (please specify)')
        ],
        label="Where are you currently based?",
        help_text="Select your current location."
    )
    other_location = forms.CharField(
        max_length=200,
        required=False,
        label="Other Location",
        help_text="If you selected 'Other', please specify your location."
    )
    registered_with_arbetsformedlingen = forms.ChoiceField(
        choices=[
            ('yes', 'Yes'),
            ('no', 'No')
        ],
        label="Are you currently registered with Arbetsförmedlingen?",
        help_text="Please indicate if you are registered with Arbetsförmedlingen."
    )

    employment_status = forms.ChoiceField(
        choices=[
            ('employed_seeking', 'I am employed, but searching for a job because my current job does not use my skills'),
            ('employed_other', 'I am employed, but searching for a job for another reason'),
            ('unemployed_registered', 'I am unemployed and registered with Arbetsförmedlingen'),
            ('unemployed_not_registered', 'I am unemployed and not registered with Arbetsförmedlingen')
        ],
        label="What is your current employment status?",
        help_text="Please select the option that best describes your current situation."
    )

    unemployed_since = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        label="If unemployed, since when?",
        help_text="Please provide the date when you became unemployed.",
        required=False
    )

    registered_unemployed_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        label="If registered with Arbetsförmedlingen, since when?",
        help_text="Please provide the specific date when you registered as unemployed with Arbetsförmedlingen. This helps us prioritize applicants who have been registered longer.",
        required=False
    )

    work_permit = forms.ChoiceField(
        choices=[
            ('yes', 'Yes'),
            ('no', 'No'),
            ('in_process', 'In Process')
        ],
        label="Do you have a valid work permit for Sweden?",
        help_text="Please indicate if you have a valid work permit to work in Sweden."
    )

    job_search_duration = forms.ChoiceField(
        choices=[
            ('less_than_1', 'Less than 1 month'),
            ('1_3', '1-3 months'),
            ('3_6', '3-6 months'),
            ('6_12', '6-12 months'),
            ('1_2_years', '1-2 years'),
            ('2_plus_years', 'More than 2 years')
        ],
        label="How long have you been searching for a job?",
        help_text="Please select the option that best describes how long you've been actively looking for a job."
    )

    # Section 2: Dream Job Details
    dream_job = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 5}),
        label="What is your dream job?",
        help_text="Please describe a job that aligns with your current skills, interests, and experience, or a field where you are willing to put in the effort to acquire the necessary skills. Think about a role you could realistically aim for within the next 3-5 years."
    )
    industry = forms.ChoiceField(
        choices=[
            ('it_software', 'IT & Software Development'),
            ('healthcare', 'Healthcare'),
            ('education', 'Education'),
            ('finance', 'Finance'),
            ('arts', 'Arts & Creative'),
            ('media_production', 'Media Production'),
            ('video_game_production', 'Video Game Production'),
            ('other', 'Other')
        ],
        label="What industry does your dream job fall under?",
        help_text="Choose the industry closest to your dream job."
    )
    category = forms.ChoiceField(
        choices=[
            ('application_development', 'Application Development'),
            ('software_engineering', 'Software Engineering'),
            ('web_development', 'Web Development'),
            ('mobile_development', 'Mobile Development'),
            ('ux_ui_design', 'UX/UI Design'),
            ('qa_testing', 'Quality Assurance & Testing'),
            ('product_management', 'Product Management'),
            ('data_analysis', 'Data Analysis & Machine Learning'),
            ('devops_cloud', 'DevOps & Cloud Infrastructure'),
            ('fullstack_development', 'Fullstack Development'),
            ('frontend_development', 'Frontend Development'),
            ('backend_development', 'Backend Development'),
            ('other', 'Other (please specify)')
        ],
        label="Which category within the industry best describes your profession or interest?",
        help_text="Select the category that best aligns with your skills or the role you aspire to."
    )
    other_category = forms.CharField(
        max_length=200,
        required=False,
        label="Other Category",
        help_text="If you selected 'Other', please specify your category."
    )

    has_experience = forms.ChoiceField(
        choices=[
            ('yes', 'Yes'),
            ('no', 'No')
        ],
        label="Do you have any experience in this field?",
        help_text="Indicate if you have any relevant experience in this field."
    )

    # Section 3: Experience and Growth
    years_of_experience = forms.ChoiceField(
        choices=[
            ('less_than_1', 'Less than 1 year'),
            ('1_3', '1-3 years'),
            ('3_5', '3-5 years'),
            ('5_plus', '5+ years')
        ],
        label="How many years of experience do you have in this field?",
        help_text="Select the range that best describes your experience."
    )
    current_skills = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 5}),
        label="What skills do you already have relevant to this job?",
        help_text="List the skills you have that are relevant to your dream job."
    )
    additional_skills = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 5}),
        label="What additional skills or education do you need?",
        help_text="Indicate any skills or education you believe you need for your dream job."
    )

    acquiring_skills = forms.ChoiceField(
        choices=[
            ('yes', 'Yes'),
            ('no', 'No'),
            ('planning', 'Planning to')
        ],
        label="Have you taken steps to acquire these skills?",
        help_text="Have you begun any training, education, or steps towards gaining these skills?"
    )

    achievable_in_5_years = forms.ChoiceField(
        choices=[
            ('yes', 'Yes'),
            ('no', 'No'),
            ('not_sure', 'Not sure')
        ],
        label="Do you believe your dream job is achievable in 5 years?",
        help_text="Realistically assess if your dream job is attainable within the next 5 years."
    )

    challenges = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 5}),
        label="What challenges might you face in achieving your dream job?",
        help_text="Describe any challenges or obstacles you foresee in reaching your dream job."
    )

    # Section 4: Work Preferences
    remote_work = forms.ChoiceField(
        choices=[
            ('yes', 'Yes'),
            ('no', 'No'),
            ('maybe', 'Maybe')
        ],
        label="Are you open to remote work?",
        help_text="Would you consider remote positions?"
    )

    willing_to_relocate = forms.ChoiceField(
        choices=[
            ('yes', 'Yes'),
            ('no', 'No'),
            ('maybe', 'Maybe')
        ],
        label="Are you willing to relocate for your dream job?",
        help_text="Would you be open to relocating for the right position?"
    )

    work_type = forms.MultipleChoiceField(
        widget=forms.CheckboxSelectMultiple,
        choices=[
            ('full_time', 'Full-time'),
            ('part_time', 'Part-time'),
            ('freelance', 'Freelance')
        ],
        label="What type of work are you looking for?",
        help_text="Select the type(s) of employment you're interested in."
    )

    support_needed = forms.MultipleChoiceField(
        widget=forms.CheckboxSelectMultiple,
        choices=[
            ('mentorship', 'Mentorship'),
            ('training', 'Training'),
            ('networking', 'Networking'),
            ('financial', 'Financial Support'),
            ('other', 'Other')
        ],
        label="What kind of support do you think you need?",
        help_text="Choose the types of support that would help you achieve your dream job."
    )

    other_support = forms.CharField(
        max_length=200,
        required=False,
        label="Other Support Needed",
        help_text="If you selected 'Other' for support needed, please specify."
    )

    # Section 5: Additional Information
    additional_comments = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 5}),
        required=False,
        label="Additional Comments or Ideas",
        help_text="If you have any further comments or ideas about how SMARCH can support your career aspirations, please share them here."
    )
    interested_in_pilot = forms.ChoiceField(
        choices=[
            ('yes', 'Yes'),
            ('no', 'No')
        ],
        label="Are you interested in joining the SMARCH Pilot Program?",
        help_text="Please indicate if you are interested in joining our pilot program."
    )

    # Section 6: Non-Disclosure Agreement (NDA)
    nda_agreement = forms.ChoiceField(
        choices=[
            ('agree', 'I agree'),
            ('disagree', 'I do not agree')
        ],
        label="NDA Agreement",
        help_text="To protect the business ideas and discussions during our program, please review our Non-Disclosure Agreement below. By selecting 'I agree', you confirm that you will keep all proprietary information shared during the SMARCH sessions confidential and will not use or share this information outside the program without permission."
    )

    def clean(self):
        """Custom validation for the form."""
        cleaned_data = super().clean()

        # If 'other' is selected for location, ensure other_location is provided
        current_location = cleaned_data.get('current_location')
        other_location = cleaned_data.get('other_location')
        if current_location == 'other' and not other_location:
            self.add_error('other_location', "Please specify your location.")

        # If 'other' is selected for category, ensure other_category is provided
        category = cleaned_data.get('category')
        other_category = cleaned_data.get('other_category')
        if category == 'other' and not other_category:
            self.add_error('other_category', "Please specify your category.")

        # If 'other' is selected for support_needed, ensure other_support is provided
        support_needed = cleaned_data.get('support_needed', [])
        other_support = cleaned_data.get('other_support')
        if 'other' in support_needed and not other_support:
            self.add_error('other_support', "Please specify what other support you need.")

        return cleaned_data