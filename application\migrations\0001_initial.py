# Generated by Django 4.2.7 on 2025-04-17 10:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0003_alter_applicantprofile_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ApplicantProfile',
            fields=[
            ],
            options={
                'verbose_name': 'Applicant Profile',
                'verbose_name_plural': 'Applicant Profiles',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('users.applicantprofile',),
        ),
        migrations.CreateModel(
            name='ApplicationFormResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('raw_data', models.JSONField()),
                ('status', models.Char<PERSON>ield(choices=[('draft', 'Draft'), ('submitted', 'Submitted'), ('in_review', 'In Review'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='draft', max_length=50)),
                ('submission_date', models.DateTimeField(blank=True, null=True)),
                ('review_date', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('applicant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='form_responses', to='users.applicantprofile')),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_applications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Application Form Response',
                'verbose_name_plural': 'Application Form Responses',
                'ordering': ['-submitted_at'],
            },
        ),
    ]
