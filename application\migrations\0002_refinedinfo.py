# Generated by Django 4.2.7 on 2025-04-17 12:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('application', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RefinedInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('location', models.Char<PERSON>ield(blank=True, max_length=200)),
                ('age', models.IntegerField(default=0)),
                ('industry', models.CharField(blank=True, max_length=100)),
                ('branch', models.CharField(blank=True, max_length=100)),
                ('role', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('experience_years', models.IntegerField(default=0)),
                ('skills', models.J<PERSON><PERSON>ield(default=list)),
                ('languages', models.J<PERSON><PERSON>ield(default=list)),
                ('work_preferences', models.JSO<PERSON>ield(default=list)),
                ('work_permit', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('application', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='refinedinfo', to='application.applicationformresponse')),
            ],
            options={
                'verbose_name': 'Refined Application Info',
                'verbose_name_plural': 'Refined Application Info',
            },
        ),
    ]
