from django.db import models
from django.contrib.auth import get_user_model
from users.models import ApplicantProfile as UserApplicantProfile
from django.utils import timezone

User = get_user_model()


class ApplicantProfile(UserApplicantProfile):
    """
    Proxy model for ApplicantProfile from users app.
    This maintains backward compatibility while we transition to the consolidated model.
    """
    class Meta:
        proxy = True
        verbose_name = 'Applicant Profile'
        verbose_name_plural = 'Applicant Profiles'


class ApplicationFormResponse(models.Model):
    """Model to store applicant form submissions."""
    applicant = models.ForeignKey(
        UserApplicantProfile, on_delete=models.CASCADE, related_name="form_responses"
    )
    raw_data = models.JSONField()
    status = models.CharField(
        max_length=50,
        choices=[
            ('draft', 'Draft'),
            ('submitted', 'Submitted'),
            ('in_review', 'In Review'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected')
        ],
        default='draft'
    )
    submission_date = models.DateTimeField(blank=True, null=True)
    review_date = models.DateTimeField(blank=True, null=True)
    reviewed_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name="reviewed_applications"
    )
    notes = models.TextField(blank=True, null=True)
    submitted_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Application Form Response'
        verbose_name_plural = 'Application Form Responses'
        ordering = ['-submitted_at']

    def __str__(self):
        return f"Application from {self.applicant.user.username} - {self.status}"

    def has_refined_info(self):
        """Check if this application has a RefinedInfo instance."""
        try:
            return hasattr(self, 'refinedinfo') and self.refinedinfo is not None
        except RefinedInfo.DoesNotExist:
            return False
    has_refined_info.boolean = True  # For admin display

    def process_application(self):
        """Process the application data and create a RefinedInfo instance."""
        if self.status != 'submitted':
            return None

        # Check if a RefinedInfo already exists for this application
        try:
            refined_info = self.refinedinfo
            # Update existing RefinedInfo
        except RefinedInfo.DoesNotExist:
            # Create new RefinedInfo
            refined_info = RefinedInfo(application=self)

        try:
            # Extract data from raw_data
            data = self.raw_data

            # Basic information
            refined_info.full_name = data.get('full_name', '')
            refined_info.location = data.get('current_location', '')
            if refined_info.location == 'other':
                refined_info.location = data.get('other_location', '')

            # Professional information
            refined_info.industry = data.get('industry', '')
            refined_info.branch = data.get('category', '')
            if refined_info.branch == 'other':
                refined_info.branch = data.get('other_category', '')

            # Experience
            years_exp = data.get('years_of_experience', '')
            if years_exp == 'less_than_1':
                refined_info.experience_years = 0
            elif years_exp == '1_3':
                refined_info.experience_years = 2
            elif years_exp == '3_5':
                refined_info.experience_years = 4
            elif years_exp == '5_plus':
                refined_info.experience_years = 5
            else:
                refined_info.experience_years = 0

            # Role
            refined_info.role = data.get('dream_job', '')[:100]  # Truncate to fit field

            # Skills
            current_skills = data.get('current_skills', '')
            if current_skills:
                skills = [s.strip() for s in current_skills.split(',')]
                refined_info.skills = skills[:10]  # Limit to 10 skills

            # Languages - Extract from dedicated languages field if available
            languages_data = data.get('languages', '')
            if languages_data:
                languages = [lang.strip() for lang in languages_data.split(',')]
                refined_info.languages = languages
            # Fallback to extracting from skills if no languages field
            elif current_skills and 'language' in current_skills.lower():
                import re
                language_matches = re.findall(r'language[s]?[:\s]+(.*?)(?:\.|$)', current_skills.lower())
                if language_matches:
                    refined_info.languages = [lang.strip().title() for lang in language_matches[0].split(',')]

            # Work preferences
            work_type = data.get('work_type', [])
            refined_info.work_preferences = work_type

            # Personal information
            if 'date_of_birth' in data:
                try:
                    from datetime import datetime
                    dob = data['date_of_birth']
                    if isinstance(dob, str):
                        dob = datetime.fromisoformat(dob.replace('Z', '+00:00')).date()
                    # Calculate age
                    today = timezone.now().date()
                    refined_info.age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
                except (ValueError, TypeError):
                    refined_info.age = 0

            # Work permit
            refined_info.work_permit = data.get('work_permit', False)

            # Save the refined info
            refined_info.save()
            return refined_info

        except Exception as e:
            # Log the error but don't crash the application process
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error processing application {self.id}: {str(e)}")

            # Create a minimal RefinedInfo with just the basic information
            if not hasattr(refined_info, 'id'):  # If it wasn't saved yet
                refined_info.full_name = data.get('full_name', '')
                refined_info.save()

            return refined_info


class RefinedInfo(models.Model):
    """Model to store processed application data in a structured format."""
    application = models.OneToOneField(
        ApplicationFormResponse, on_delete=models.CASCADE, related_name="refinedinfo"
    )
    # Basic information
    full_name = models.CharField(max_length=100, blank=True)
    location = models.CharField(max_length=200, blank=True)
    age = models.IntegerField(default=0)

    # Professional information
    industry = models.CharField(max_length=100, blank=True)
    branch = models.CharField(max_length=100, blank=True)
    role = models.CharField(max_length=100, blank=True)
    experience_years = models.IntegerField(default=0)

    # Skills and languages
    skills = models.JSONField(default=list)
    languages = models.JSONField(default=list)

    # Work preferences
    work_preferences = models.JSONField(default=list)
    work_permit = models.BooleanField(default=False)

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Refined Application Info'
        verbose_name_plural = 'Refined Application Info'

    def __str__(self):
        return f"Refined info for {self.application.applicant.user.username}"
