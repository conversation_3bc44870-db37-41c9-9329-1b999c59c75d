from django.urls import path
from django.views.generic.base import RedirectView
from . import views

app_name = 'application'

urlpatterns = [
    path('', views.index, name='index'),
    path('apply/', views.application_form, name='apply'),
    path('status/', views.application_status, name='status'),
    # Redirect from old URL to new URL
    path('application-status/', RedirectView.as_view(pattern_name='application:status', permanent=True)),
    path('application-success/', views.application_success, name='success'),
    path('update-application/', views.update_application, name='update'),
    path('process/<int:application_id>/', views.process_application, name='process'),

    # Contact request URLs
    path('contact-requests/', views.contact_requests, name='contact_requests'),
    path('contact-request/<int:request_id>/', views.contact_request_detail, name='contact_request_detail'),
    path('contact-request/<int:request_id>/<str:action>/', views.contact_request_respond, name='contact_request_respond'),
]
