from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponseForbidden
from django.core.paginator import Paginator
from .models import Applicant<PERSON>rofile, ApplicationFormResponse
from .forms import DreamJobA<PERSON>licationForm
from django.utils import timezone
import json
from datetime import date
from services.models import ContactRequest


class DateEncoder(json.JSONEncoder):
    """Custom JSON encoder that can handle date objects."""
    def default(self, obj):
        if isinstance(obj, date):
            return obj.isoformat()
        return super().default(obj)


def index(request):
    """View for the application landing page."""
    # If the user is logged in and is a superuser, create an applicant profile if it doesn't exist
    if request.user.is_authenticated and request.user.is_superuser:
        if not hasattr(request.user, 'applicant_profile'):
            from users.models import ApplicantProfile as UserApplicantProfile
            UserApplicantProfile.objects.create(user=request.user)
            messages.success(request, "An applicant profile has been created for your superuser account.")

    return render(request, 'application/index.html')


@login_required
def application_form(request):
    """View for the application form submission."""
    # Check if the user already has an applicant profile
    if hasattr(request.user, 'applicant_profile'):
        applicant_profile = request.user.applicant_profile
    else:
        if request.user.is_superuser:
            # Create an applicant profile for superuser
            applicant_profile = ApplicantProfile.objects.create(
                user=request.user
            )
        else:
            messages.error(request, "You need to create an applicant profile first.")
            return redirect('users:applicant_signup')

    # Check if the user has already submitted an application
    existing_application = ApplicationFormResponse.objects.filter(
        applicant=applicant_profile,
        status__in=['submitted', 'in_review', 'approved']
    ).first()

    if existing_application:
        messages.info(request, "You have already submitted an application.")
        return redirect('application:status')

    # Handle form submission
    if request.method == 'POST':
        form = DreamJobApplicationForm(request.POST)
        if form.is_valid():
            # Check if this is a draft save or a final submission
            is_draft = 'save_draft' in request.POST

            # Create or update the application form response
            status = 'draft' if is_draft else 'submitted'
            submission_date = None if is_draft else timezone.now()

            # Check if there's an existing draft
            existing_draft = ApplicationFormResponse.objects.filter(
                applicant=applicant_profile,
                status='draft'
            ).first()

            # Convert form.cleaned_data to a JSON-serializable format
            form_data = {}
            for key, value in form.cleaned_data.items():
                if isinstance(value, date):
                    form_data[key] = value.isoformat()
                else:
                    form_data[key] = value

            if existing_draft:
                # Update the existing draft
                existing_draft.raw_data = form_data
                existing_draft.status = status
                existing_draft.submission_date = submission_date
                existing_draft.save()
                application = existing_draft
            else:
                # Create a new application form response
                application = ApplicationFormResponse(
                    applicant=applicant_profile,
                    raw_data=form_data,
                    status=status,
                    submission_date=submission_date
                )
                application.save()

            if is_draft:
                messages.success(request, "Your application has been saved as a draft. You can continue later.")
                return redirect('application:apply')
            else:
                # Update the queue position based on unemployment data
                unemployed_since = form_data.get('unemployed_since')
                registered_unemployed_date = form_data.get('registered_unemployed_date')
                job_search_duration = form_data.get('job_search_duration')
                applicant_profile.update_queue_position(
                    unemployed_since=unemployed_since,
                    registered_unemployed_date=registered_unemployed_date,
                    job_search_duration=job_search_duration
                )

                # Update the applicant profile with the form data
                applicant_profile.industry = form_data.get('industry')
                applicant_profile.role = form_data.get('category')

                # Convert years of experience to integer
                years_exp = form_data.get('years_of_experience')
                if years_exp == 'less_than_1':
                    applicant_profile.position_experience_years = 0
                elif years_exp == '1_3':
                    applicant_profile.position_experience_years = 2
                elif years_exp == '3_5':
                    applicant_profile.position_experience_years = 4
                elif years_exp == '5_plus':
                    applicant_profile.position_experience_years = 5

                # Update languages if provided in current_skills
                current_skills = form_data.get('current_skills')
                if current_skills and 'language' in current_skills.lower():
                    # Try to extract languages from the skills text
                    import re
                    languages = re.findall(r'language[s]?[:\s]+(.*?)(?:\.|$)', current_skills.lower())
                    if languages:
                        applicant_profile.languages = languages[0].strip().title()

                applicant_profile.save()

                # Process the application to create a RefinedInfo instance
                application.process_application()

                messages.success(request, "Your application has been submitted successfully.")
                return redirect('application:success')
    else:
        # Check if there's an existing draft to pre-fill the form
        existing_draft = ApplicationFormResponse.objects.filter(
            applicant=applicant_profile,
            status='draft'
        ).first()

        if existing_draft:
            # Pre-fill the form with the draft data
            # Convert ISO date strings back to date objects
            initial_data = {}
            for key, value in existing_draft.raw_data.items():
                if key == 'date_of_birth' and isinstance(value, str):
                    try:
                        initial_data[key] = date.fromisoformat(value)
                    except ValueError:
                        # If the date can't be parsed, just use the string value
                        initial_data[key] = value
                else:
                    initial_data[key] = value

            form = DreamJobApplicationForm(initial=initial_data)
        else:
            form = DreamJobApplicationForm()

    return render(request, 'application/apply.html', {'form': form})


@login_required
def application_status(request):
    """View for checking application status."""
    if hasattr(request.user, 'applicant_profile'):
        applicant_profile = request.user.applicant_profile
        applications = ApplicationFormResponse.objects.filter(
            applicant=applicant_profile
        ).order_by('-submitted_at')

        if not applications:
            messages.info(request, "You haven't submitted any applications yet.")
            return redirect('application:apply')

        return render(request, 'application/status.html', {'applications': applications})
    else:
        messages.error(request, "You need to create an applicant profile first.")
        return redirect('users:applicant_signup')


@login_required
def application_success(request):
    """View for application submission success page."""
    return render(request, 'application/success.html')


@login_required
def process_application(request, application_id):
    """View for manually processing an application."""
    if request.method != 'POST':
        messages.error(request, "Invalid request method.")
        return redirect('application:status')

    try:
        # Get the application
        application = ApplicationFormResponse.objects.get(
            id=application_id,
            applicant__user=request.user
        )

        # Process the application
        refined_info = application.process_application()

        if refined_info:
            messages.success(request, "Application processed successfully.")
        else:
            messages.warning(request, "Application could not be processed. Please try again later.")
    except ApplicationFormResponse.DoesNotExist:
        messages.error(request, "Application not found.")
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")

    return redirect('application:status')


@login_required
def update_application(request):
    """View for updating an existing application."""
    if hasattr(request.user, 'applicant_profile'):
        applicant_profile = request.user.applicant_profile

        # Check if the user has an existing application
        existing_application = ApplicationFormResponse.objects.filter(
            applicant=applicant_profile
        ).order_by('-submitted_at').first()

        if not existing_application:
            messages.error(request, "You don't have an application to update.")
            return redirect('application:apply')

        # Handle form submission
        if request.method == 'POST':
            form = DreamJobApplicationForm(request.POST)
            if form.is_valid():
                # Convert form.cleaned_data to a JSON-serializable format
                form_data = {}
                for key, value in form.cleaned_data.items():
                    if isinstance(value, date):
                        form_data[key] = value.isoformat()
                    else:
                        form_data[key] = value

                # Update the existing application
                existing_application.raw_data = form_data
                existing_application.save()

                # Update the queue position based on unemployment data
                unemployed_since = form_data.get('unemployed_since')
                registered_unemployed_date = form_data.get('registered_unemployed_date')
                job_search_duration = form_data.get('job_search_duration')
                applicant_profile.update_queue_position(
                    unemployed_since=unemployed_since,
                    registered_unemployed_date=registered_unemployed_date,
                    job_search_duration=job_search_duration
                )

                # Update the applicant profile with the form data
                applicant_profile.industry = form_data.get('industry')
                applicant_profile.role = form_data.get('category')

                # Convert years of experience to integer
                years_exp = form_data.get('years_of_experience')
                if years_exp == 'less_than_1':
                    applicant_profile.position_experience_years = 0
                elif years_exp == '1_3':
                    applicant_profile.position_experience_years = 2
                elif years_exp == '3_5':
                    applicant_profile.position_experience_years = 4
                elif years_exp == '5_plus':
                    applicant_profile.position_experience_years = 5

                # Update languages if provided in current_skills
                current_skills = form_data.get('current_skills')
                if current_skills and 'language' in current_skills.lower():
                    # Try to extract languages from the skills text
                    import re
                    languages = re.findall(r'language[s]?[:\s]+(.*?)(?:\.|$)', current_skills.lower())
                    if languages:
                        applicant_profile.languages = languages[0].strip().title()

                applicant_profile.save()

                # Process the application to create or update a RefinedInfo instance
                existing_application.process_application()

                messages.success(request, "Your application has been updated successfully.")
                return redirect('application:status')
        else:
            # Pre-fill the form with the existing application data
            initial_data = {}
            for key, value in existing_application.raw_data.items():
                if key in ['date_of_birth', 'unemployed_since', 'registered_unemployed_date'] and isinstance(value, str):
                    try:
                        initial_data[key] = date.fromisoformat(value)
                    except ValueError:
                        initial_data[key] = value
                else:
                    initial_data[key] = value

            form = DreamJobApplicationForm(initial=initial_data)

        return render(request, 'application/update.html', {
            'form': form,
            'application': existing_application
        })
    else:
        messages.error(request, "You need to create an applicant profile first.")
        return redirect('users:applicant_signup')


@login_required
def contact_requests(request):
    """View for applicants to see their received contact requests."""
    if not hasattr(request.user, 'applicant_profile'):
        messages.error(request, "You need to create an applicant profile first.")
        return redirect('users:applicant_signup')

    applicant_profile = request.user.applicant_profile

    # Get all contact requests for this applicant
    contact_requests = ContactRequest.objects.filter(
        recipient=applicant_profile
    ).order_by('-sent_at')

    # Paginate the results
    paginator = Paginator(contact_requests, 10)  # Show 10 requests per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'application/contact_requests.html', {
        'page_obj': page_obj,
        'total_requests': contact_requests.count(),
    })


@login_required
def contact_request_detail(request, request_id):
    """View for applicants to see details of a specific contact request."""
    if not hasattr(request.user, 'applicant_profile'):
        messages.error(request, "You need to create an applicant profile first.")
        return redirect('users:applicant_signup')

    applicant_profile = request.user.applicant_profile

    # Get the contact request
    contact_req = get_object_or_404(
        ContactRequest,
        id=request_id,
        recipient=applicant_profile
    )

    return render(request, 'application/contact_request_detail.html', {
        'contact_request': contact_req
    })


@login_required
def contact_request_respond(request, request_id, action):
    """View for applicants to respond to a contact request."""
    if not hasattr(request.user, 'applicant_profile'):
        messages.error(request, "You need to create an applicant profile first.")
        return redirect('users:applicant_signup')

    applicant_profile = request.user.applicant_profile

    # Get the contact request
    contact_req = get_object_or_404(
        ContactRequest,
        id=request_id,
        recipient=applicant_profile,
        status='pending'  # Only allow responding to pending requests
    )

    # Process the response
    if action == 'accept':
        contact_req.status = 'accepted'
        messages.success(request, "Contact request accepted successfully.")
    elif action == 'decline':
        contact_req.status = 'declined'
        messages.success(request, "Contact request declined successfully.")
    else:
        messages.error(request, "Invalid action.")
        return redirect('application:contact_request_detail', request_id=request_id)

    # Update the responded_at timestamp
    contact_req.responded_at = timezone.now()
    contact_req.save()

    return redirect('application:contact_requests')
