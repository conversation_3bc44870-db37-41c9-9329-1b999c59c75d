from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

from .models import BlogPost, Category, Comment
from .forms import CommentForm


def blog_list(request):
    """View for listing blog posts."""
    query = request.GET.get('query', '')
    category_slug = request.GET.get('category', '')
    
    posts = BlogPost.objects.filter(is_published=True)
    
    # Apply search query if provided
    if query:
        posts = posts.filter(
            Q(title__icontains=query) | 
            Q(content__icontains=query) |
            Q(tags__icontains=query)
        )
    
    # Filter by category if provided
    if category_slug:
        category = get_object_or_404(Category, slug=category_slug)
        posts = posts.filter(category=category)
    
    # Get all categories for the sidebar
    categories = Category.objects.all()
    
    # Pagination
    paginator = Paginator(posts, 10)  # Show 10 posts per page
    page = request.GET.get('page')
    
    try:
        posts = paginator.page(page)
    except PageNotAnInteger:
        # If page is not an integer, deliver first page
        posts = paginator.page(1)
    except EmptyPage:
        # If page is out of range, deliver last page of results
        posts = paginator.page(paginator.num_pages)
    
    context = {
        'posts': posts,
        'categories': categories,
        'query': query,
        'category_slug': category_slug
    }
    
    return render(request, 'blog/list.html', context)


def blog_by_category(request, category_slug):
    """View for listing blog posts by category."""
    category = get_object_or_404(Category, slug=category_slug)
    posts = BlogPost.objects.filter(category=category, is_published=True)
    
    # Pagination
    paginator = Paginator(posts, 10)
    page = request.GET.get('page')
    
    try:
        posts = paginator.page(page)
    except PageNotAnInteger:
        posts = paginator.page(1)
    except EmptyPage:
        posts = paginator.page(paginator.num_pages)
    
    categories = Category.objects.all()
    
    context = {
        'category': category,
        'posts': posts,
        'categories': categories
    }
    
    return render(request, 'blog/by_category.html', context)


def blog_detail(request, post_slug):
    """View for displaying a blog post detail."""
    post = get_object_or_404(BlogPost, slug=post_slug, is_published=True)
    
    # Increment view count
    post.view_count += 1
    post.save()
    
    # Get comments
    comments = post.comments.filter(is_approved=True, parent=None)
    
    # Comment form
    if request.method == 'POST' and request.user.is_authenticated:
        comment_form = CommentForm(request.POST)
        if comment_form.is_valid():
            new_comment = comment_form.save(commit=False)
            new_comment.post = post
            new_comment.user = request.user
            
            # Check if it's a reply
            parent_id = request.POST.get('parent_id')
            if parent_id:
                parent_comment = get_object_or_404(Comment, id=parent_id)
                new_comment.parent = parent_comment
            
            new_comment.save()
            messages.success(request, "Your comment has been added!")
            return redirect('blog:detail', post_slug=post.slug)
    else:
        comment_form = CommentForm()
    
    # Related posts
    related_posts = BlogPost.objects.filter(
        category=post.category,
        is_published=True
    ).exclude(id=post.id)[:3]
    
    context = {
        'post': post,
        'comments': comments,
        'comment_form': comment_form,
        'related_posts': related_posts
    }
    
    return render(request, 'blog/detail.html', context)


@login_required
def add_comment(request, post_slug):
    """View for adding a comment to a blog post."""
    post = get_object_or_404(BlogPost, slug=post_slug, is_published=True)
    
    if request.method == 'POST':
        form = CommentForm(request.POST)
        if form.is_valid():
            comment = form.save(commit=False)
            comment.post = post
            comment.user = request.user
            
            # Check if it's a reply
            parent_id = request.POST.get('parent_id')
            if parent_id:
                parent_comment = get_object_or_404(Comment, id=parent_id)
                comment.parent = parent_comment
            
            comment.save()
            messages.success(request, "Your comment has been added!")
    
    return redirect('blog:detail', post_slug=post.slug)
