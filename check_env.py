"""
<PERSON><PERSON><PERSON> to check if environment variables are properly loaded.
Run this script to verify that Django can access the environment variables.
"""
import os
import django
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smarch.settings')
django.setup()

# Check environment variables
print("Checking environment variables...")
print(f"SECRET_KEY: {'✅ Set' if settings.SECRET_KEY else '❌ Not set'}")
print(f"DEBUG: {'✅ Set to ' + str(settings.DEBUG)}")
print(f"ALLOWED_HOSTS: {'✅ Set to ' + str(settings.ALLOWED_HOSTS)}")

# Check Stripe settings
print("\nStripe Settings:")
print(f"STRIPE_PUBLISHABLE_KEY: {'✅ Set' if hasattr(settings, 'STRIPE_PUBLISHABLE_KEY') and settings.STRIPE_PUBLISHABLE_KEY else '❌ Not set'}")
print(f"STRIPE_SECRET_KEY: {'✅ Set' if hasattr(settings, 'STRIPE_SECRET_KEY') and settings.STRIPE_SECRET_KEY else '❌ Not set'}")
print(f"STRIPE_WEBHOOK_SECRET: {'✅ Set' if hasattr(settings, 'STRIPE_WEBHOOK_SECRET') and settings.STRIPE_WEBHOOK_SECRET else '❌ Not set'}")

# Check AWS settings (only if DEBUG is False)
if not settings.DEBUG:
    print("\nAWS Settings:")
    print(f"AWS_ACCESS_KEY_ID: {'✅ Set' if hasattr(settings, 'AWS_ACCESS_KEY_ID') and settings.AWS_ACCESS_KEY_ID else '❌ Not set'}")
    print(f"AWS_SECRET_ACCESS_KEY: {'✅ Set' if hasattr(settings, 'AWS_SECRET_ACCESS_KEY') and settings.AWS_SECRET_ACCESS_KEY else '❌ Not set'}")
    print(f"AWS_STORAGE_BUCKET_NAME: {'✅ Set' if hasattr(settings, 'AWS_STORAGE_BUCKET_NAME') and settings.AWS_STORAGE_BUCKET_NAME else '❌ Not set'}")

# Check Email settings
print("\nEmail Settings:")
print(f"EMAIL_BACKEND: {'✅ Set to ' + settings.EMAIL_BACKEND}")

print("\nEnvironment check complete.")
