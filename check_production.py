"""
Sc<PERSON>t to check if the application works with DEBUG=False.
This simulates a production environment to catch any issues.
"""
import os
import sys
import django
from django.core.management import call_command
from django.conf import settings

# Set DEBUG to False before loading Django settings
os.environ['DEBUG'] = 'False'
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smarch.settings')
django.setup()

print("Checking production configuration...")
print(f"DEBUG: {'✅ Set to ' + str(settings.DEBUG)}")
print(f"ALLOWED_HOSTS: {'✅ Set to ' + str(settings.ALLOWED_HOSTS)}")

# Check if static files are configured correctly
print("\nStatic Files Configuration:")
if hasattr(settings, 'STATICFILES_STORAGE'):
    print(f"STATICFILES_STORAGE: {'✅ Set to ' + settings.STATICFILES_STORAGE}")
else:
    print("STATICFILES_STORAGE: ❌ Not set")

# Check if AWS settings are configured correctly (should be loaded when DEBUG is False)
print("\nAWS Settings:")
if hasattr(settings, 'AWS_ACCESS_KEY_ID'):
    print(f"AWS_ACCESS_KEY_ID: {'✅ Set' if settings.AWS_ACCESS_KEY_ID else '❌ Not set'}")
else:
    print("AWS_ACCESS_KEY_ID: ❌ Not set")

if hasattr(settings, 'AWS_SECRET_ACCESS_KEY'):
    print(f"AWS_SECRET_ACCESS_KEY: {'✅ Set' if settings.AWS_SECRET_ACCESS_KEY else '❌ Not set'}")
else:
    print("AWS_SECRET_ACCESS_KEY: ❌ Not set")

if hasattr(settings, 'AWS_STORAGE_BUCKET_NAME'):
    print(f"AWS_STORAGE_BUCKET_NAME: {'✅ Set' if settings.AWS_STORAGE_BUCKET_NAME else '❌ Not set'}")
else:
    print("AWS_STORAGE_BUCKET_NAME: ❌ Not set")

# Check if email settings are configured correctly
print("\nEmail Settings:")
print(f"EMAIL_BACKEND: {'✅ Set to ' + settings.EMAIL_BACKEND}")
if hasattr(settings, 'EMAIL_HOST'):
    print(f"EMAIL_HOST: {'✅ Set' if settings.EMAIL_HOST else '❌ Not set'}")
else:
    print("EMAIL_HOST: ❌ Not set")

# Run collectstatic to check for any issues (with --dry-run to avoid actual file operations)
print("\nRunning collectstatic (dry run)...")
try:
    call_command('collectstatic', '--dry-run', '--noinput', verbosity=0)
    print("✅ collectstatic successful")
except Exception as e:
    print(f"❌ collectstatic failed: {str(e)}")

# Run check command to verify settings
print("\nRunning Django check command...")
try:
    call_command('check', '--deploy')
    print("✅ Django check successful")
except Exception as e:
    print(f"❌ Django check failed: {str(e)}")

print("\nProduction configuration check complete.")
