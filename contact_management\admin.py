from django.contrib import admin
from .models import ContactMessage


@admin.register(ContactMessage)
class ContactMessageAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'subject', 'is_read', 'is_archived', 'created_at')
    list_filter = ('is_read', 'is_archived', 'created_at')
    search_fields = ('name', 'email', 'subject', 'message')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'

    actions = ['mark_as_read', 'mark_as_unread', 'archive_messages', 'unarchive_messages']

    def mark_as_read(self, request, queryset):
        """Mark selected messages as read."""
        updated = queryset.update(is_read=True)
        self.message_user(request, f"{updated} messages marked as read.")
    mark_as_read.short_description = "Mark selected messages as read"

    def mark_as_unread(self, request, queryset):
        """Mark selected messages as unread."""
        updated = queryset.update(is_read=False)
        self.message_user(request, f"{updated} messages marked as unread.")
    mark_as_unread.short_description = "Mark selected messages as unread"

    def archive_messages(self, request, queryset):
        """Archive selected messages."""
        updated = queryset.update(is_archived=True)
        self.message_user(request, f"{updated} messages archived.")
    archive_messages.short_description = "Archive selected messages"

    def unarchive_messages(self, request, queryset):
        """Unarchive selected messages."""
        updated = queryset.update(is_archived=False)
        self.message_user(request, f"{updated} messages unarchived.")
    unarchive_messages.short_description = "Unarchive selected messages"
