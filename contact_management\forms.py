from django import forms
from .models import ContactMessage


class ContactForm(forms.ModelForm):
    """Form for the contact page."""
    
    class Meta:
        model = ContactMessage
        fields = ['name', 'email', 'subject', 'message']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Your Name'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Your Email'}),
            'subject': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Subject'}),
            'message': forms.Textarea(attrs={'class': 'form-control', 'rows': 5, 'placeholder': 'Your Message'}),
        }
        
    def clean_email(self):
        """Validate email format."""
        email = self.cleaned_data.get('email')
        if email and not email.strip():
            raise forms.ValidationError("Email cannot be empty.")
        return email
        
    def clean_message(self):
        """Validate message content."""
        message = self.cleaned_data.get('message')
        if message and len(message.strip()) < 10:
            raise forms.ValidationError("Message must be at least 10 characters long.")
        return message
