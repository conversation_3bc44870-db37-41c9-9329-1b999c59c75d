from django.db import models
from django.utils import timezone


class ContactMessage(models.Model):
    """Model for storing contact form submissions."""

    name = models.CharField(max_length=255)
    email = models.EmailField()
    subject = models.CharField(max_length=255)
    message = models.TextField()

    # Status tracking
    is_read = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Contact Message'
        verbose_name_plural = 'Contact Messages'

    def __str__(self):
        return f"Message from {self.name} - {self.subject}"

    def mark_as_read(self):
        """Mark the message as read."""
        if not self.is_read:
            self.is_read = True
            self.save(update_fields=['is_read', 'updated_at'])

    def archive(self):
        """Archive the message."""
        if not self.is_archived:
            self.is_archived = True
            self.save(update_fields=['is_archived', 'updated_at'])

    def unarchive(self):
        """Unarchive the message."""
        if self.is_archived:
            self.is_archived = False
            self.save(update_fields=['is_archived', 'updated_at'])
