from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.conf import settings

from .forms import ContactForm
from .models import ContactMessage

# Import the email notification service
try:
    from notifications.utils import EmailNotificationService
    email_service_available = True
except ImportError:
    email_service_available = False


def contact_form(request):
    """
    View for displaying and processing the contact form.
    """
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            # Save the form data
            contact_message = form.save()

            # Send email notification if the service is available
            if email_service_available:
                try:
                    EmailNotificationService.send_contact_form_notification(contact_message)
                except Exception as e:
                    # Log the error but don't prevent the form from being submitted
                    print(f"Error sending contact form notification: {str(e)}")

            # Redirect to success page
            return redirect('contact:contact_success')
    else:
        form = ContactForm()

    return render(request, 'contact_management/contact_form.html', {'form': form})


def contact_success(request):
    """
    View for displaying the success message after form submission.
    """
    return render(request, 'contact_management/contact_success.html')


@login_required
def contact_requests(request):
    """
    View for displaying contact requests for hiring partners.
    """
    try:
        hiring_partner_profile = request.user.hiring_partner_profile
        contact_requests = hiring_partner_profile.contact_requests.all()
        return render(request, 'contact_management/requests.html', {'requests': contact_requests})
    except:
        messages.error(request, "You need to be a hiring partner to view contact requests.")
        return redirect('services:index')


@login_required
def contact_messages(request):
    """
    View for displaying contact messages for staff.
    """
    if not request.user.is_staff:
        messages.error(request, "You need to be a staff member to view contact messages.")
        return redirect('core:home')

    contact_messages = ContactMessage.objects.all()
    return render(request, 'contact_management/messages.html', {'messages': contact_messages})
