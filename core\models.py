from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class CarouselItem(models.Model):
    """Model for storing carousel items on the website's homepage."""
    image = models.ImageField(upload_to='carousel/')
    title = models.CharField(max_length=200)
    description = models.TextField()
    order = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    url = models.URLField(blank=True, null=True, help_text="Optional URL for the carousel item")

    class Meta:
        ordering = ['order', '-created_at']
        verbose_name = 'Carousel Item'
        verbose_name_plural = 'Carousel Items'

    def __str__(self):
        return self.title


class Event(models.Model):
    """Model for storing events shown on the website."""
    title = models.CharField(max_length=200)
    description = models.TextField()
    event_date = models.DateTimeField()
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='created_events'
    )
    link = models.URLField(blank=True, null=True)
    location = models.CharField(max_length=255, blank=True, null=True)
    image = models.ImageField(upload_to='events/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-event_date']
        verbose_name = 'Event'
        verbose_name_plural = 'Events'

    def __str__(self):
        return self.title 