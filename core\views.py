from django.shortcuts import render, redirect
from django.contrib import messages
from .models import CarouselItem, Event


def home(request):
    """View for the home page with carousel items."""
    carousel_items = CarouselItem.objects.filter(is_active=True)
    upcoming_events = Event.objects.filter(is_active=True).order_by('event_date')[:3]

    context = {
        'carousel_items': carousel_items,
        'upcoming_events': upcoming_events,
    }

    return render(request, 'common/home.html', context)


def about(request):
    """View for the about page with collaboration form."""
    if request.method == 'POST':
        # Process the form data
        name = request.POST.get('name')
        email = request.POST.get('email')  # Would be used in a real implementation
        company = request.POST.get('company')  # Would be used in a real implementation
        inquiry_type = request.POST.get('inquiry_type')  # Would be used in a real implementation
        message = request.POST.get('message')  # Would be used in a real implementation

        # Here you would typically save this to the database or send an email
        # For now, we'll just show a success message
        # In a real implementation, you would save this data or send an email
        messages.success(
            request,
            f"Thank you, {name}! Your inquiry has been received. We'll get back to you soon."
        )

        # Redirect to avoid form resubmission
        return redirect('core:about')

    return render(request, 'common/about.html')
