#!/usr/bin/env python
"""
Demo script showing how to use the AI matching service.
This creates sample data so you can see the AI matching in action.
"""

import os
import django
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smarch.settings')
django.setup()

from django.contrib.auth import get_user_model
from users.models import ApplicantProfile, HiringPartnerProfile
from services.models import Offer, JobRequirement

User = get_user_model()

def create_demo_data():
    """Create realistic demo data for AI matching demonstration."""
    print("🎬 Creating Demo Data for AI Matching...")
    
    # Create hiring partner
    recruiter_user, created = User.objects.get_or_create(
        username='demo_recruiter',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Sarah',
            'last_name': 'Wilson',
            'user_type': 'service',
            'password': 'pbkdf2_sha256$600000$demo$hash'  # password: demo123
        }
    )
    
    if created:
        recruiter_user.set_password('demo123')
        recruiter_user.save()
    
    hiring_partner, created = HiringPartnerProfile.objects.get_or_create(
        user=recruiter_user,
        defaults={
            'company_name': 'TechCorp Solutions',
            'company_size': '100-500',
            'primary_location': 'San Francisco, CA',
            'branch': 'Technology',
            'industry': 'Software Development',
            'company_description': 'Leading software development company specializing in web applications',
            'work_location': 'hybrid'
        }
    )
    
    # Create realistic job offers
    offers_data = [
        {
            'title': 'Senior Python Developer',
            'description': '''We are seeking a Senior Python Developer to join our growing team. 
            
Required Skills:
- Python (3+ years)
- Django or Flask
- JavaScript and React
- SQL and PostgreSQL
- Git version control

Preferred Skills:
- AWS or cloud experience
- Docker containerization
- Redis caching
- API development
- Agile methodologies

Experience: 3-5 years in web development
Location: San Francisco or Remote
Salary: $120,000 - $150,000''',
            'price': 135000,
        },
        {
            'title': 'Frontend React Developer',
            'description': '''Looking for a talented Frontend Developer with React expertise.
            
Required Skills:
- JavaScript (ES6+)
- React and Redux
- HTML5 and CSS3
- Responsive design
- Git

Preferred Skills:
- TypeScript
- Next.js
- Tailwind CSS
- Jest testing
- Webpack

Experience: 2-4 years
Location: Hybrid (SF Bay Area)
Salary: $90,000 - $120,000''',
            'price': 105000,
        },
        {
            'title': 'DevOps Engineer',
            'description': '''DevOps Engineer needed for infrastructure automation.
            
Required Skills:
- AWS or Azure
- Docker and Kubernetes
- CI/CD pipelines
- Linux administration
- Infrastructure as Code

Preferred Skills:
- Terraform
- Jenkins
- Monitoring tools
- Python scripting
- Security best practices

Experience: 3+ years
Location: Remote OK
Salary: $130,000 - $160,000''',
            'price': 145000,
        }
    ]
    
    for offer_data in offers_data:
        offer, created = Offer.objects.get_or_create(
            title=offer_data['title'],
            hiring_partner=hiring_partner,
            defaults={
                'description': offer_data['description'],
                'price': offer_data['price'],
                'is_premium': False,
                'is_active': True
            }
        )
        if created:
            print(f"✅ Created offer: {offer.title}")
    
    # Create realistic candidate profiles
    candidates_data = [
        {
            'username': 'alex_python',
            'email': '<EMAIL>',
            'first_name': 'Alex',
            'last_name': 'Rodriguez',
            'profile_data': {
                'role': 'Senior Python Developer',
                'industry': 'Software Development',
                'location': 'San Francisco, CA',
                'position_experience_years': 5,
                'bio': '''Experienced Python developer with 5 years in web development. 
                Proficient in Django, React, PostgreSQL, and AWS. Strong background in 
                building scalable web applications and API development.''',
                'languages': 'English, Spanish',
                'branch': 'Technology'
            }
        },
        {
            'username': 'emma_frontend',
            'email': '<EMAIL>',
            'first_name': 'Emma',
            'last_name': 'Chen',
            'profile_data': {
                'role': 'Frontend Developer',
                'industry': 'Technology',
                'location': 'San Jose, CA',
                'position_experience_years': 3,
                'bio': '''Frontend specialist with expertise in React, TypeScript, and modern 
                JavaScript. Experience with responsive design, state management, and testing.
                Passionate about user experience and clean code.''',
                'languages': 'English, Mandarin',
                'branch': 'Technology'
            }
        },
        {
            'username': 'mike_devops',
            'email': '<EMAIL>',
            'first_name': 'Mike',
            'last_name': 'Johnson',
            'profile_data': {
                'role': 'DevOps Engineer',
                'industry': 'Cloud Computing',
                'location': 'Remote',
                'position_experience_years': 4,
                'bio': '''DevOps engineer with strong AWS and Kubernetes experience. 
                Specialized in CI/CD automation, infrastructure as code, and monitoring.
                Python scripting and security-focused approach.''',
                'languages': 'English',
                'branch': 'Technology'
            }
        },
        {
            'username': 'lisa_junior',
            'email': '<EMAIL>',
            'first_name': 'Lisa',
            'last_name': 'Park',
            'profile_data': {
                'role': 'Junior Developer',
                'industry': 'Software Development',
                'location': 'Oakland, CA',
                'position_experience_years': 1,
                'bio': '''Recent bootcamp graduate with strong foundation in Python and JavaScript.
                Eager to learn and grow in a collaborative environment. Experience with 
                basic web development and version control.''',
                'languages': 'English, Korean',
                'branch': 'Technology'
            }
        },
        {
            'username': 'david_senior',
            'email': '<EMAIL>',
            'first_name': 'David',
            'last_name': 'Thompson',
            'profile_data': {
                'role': 'Senior Software Engineer',
                'industry': 'Software Development',
                'location': 'New York, NY',
                'position_experience_years': 8,
                'bio': '''Senior engineer with extensive full-stack experience. Expert in 
                Python, Django, React, and cloud technologies. Led multiple teams and 
                architected large-scale applications.''',
                'languages': 'English, French',
                'branch': 'Technology'
            }
        }
    ]
    
    for candidate_data in candidates_data:
        user, created = User.objects.get_or_create(
            username=candidate_data['username'],
            defaults={
                'email': candidate_data['email'],
                'first_name': candidate_data['first_name'],
                'last_name': candidate_data['last_name'],
                'user_type': 'applicant'
            }
        )
        
        if created:
            user.set_password('demo123')
            user.save()
        
        profile, created = ApplicantProfile.objects.get_or_create(
            user=user,
            defaults=candidate_data['profile_data']
        )
        if created:
            print(f"✅ Created candidate: {user.get_full_name()}")
    
    print(f"\n🎉 Demo data created successfully!")
    print(f"📋 Login credentials:")
    print(f"   Recruiter: demo_recruiter / demo123")
    print(f"   URL: http://127.0.0.1:8000/services/offers/")
    print(f"\n🚀 How to test AI matching:")
    print(f"   1. Login as demo_recruiter")
    print(f"   2. Go to 'My Offers'")
    print(f"   3. Click the 🤖 robot icon next to any offer")
    print(f"   4. See AI-powered candidate matches!")

if __name__ == '__main__':
    create_demo_data()
