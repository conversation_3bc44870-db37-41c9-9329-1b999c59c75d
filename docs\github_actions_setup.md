# Setting Up GitHub Actions Secrets for CI/CD

This document explains how to set up the necessary secrets for the GitHub Actions CI/CD pipeline.

## Required Secrets

The following secrets need to be added to your GitHub repository:

1. `HEROKU_API_KEY`: Your Heroku API key
2. `HEROKU_APP_NAME`: The name of your Heroku application
3. `<PERSON>EROKU_EMAIL`: The email address associated with your Heroku account
4. `SLACK_WEBHOOK`: (Optional) A Slack webhook URL for deployment notifications

## How to Add Secrets to GitHub

1. Go to your GitHub repository
2. Click on "Settings"
3. In the left sidebar, click on "Secrets and variables" > "Actions"
4. Click on "New repository secret"
5. Add each of the secrets listed above

## Getting Your Heroku API Key

1. Log in to your Heroku account
2. Go to your [Account Settings](https://dashboard.heroku.com/account)
3. Scroll down to the "API Key" section
4. Click "Reveal" to see your API key
5. Copy this key and add it as the `HEROKU_API_KEY` secret in GitHub

## Setting Up Slack Notifications (Optional)

1. Create a new Slack app in your workspace
2. Enable Incoming Webhooks
3. Create a new webhook URL for your desired channel
4. Copy this URL and add it as the `SLACK_WEBHOOK` secret in GitHub

## Testing the CI/CD Pipeline

After setting up the secrets, push a commit to the main branch to trigger the CI/CD pipeline. The pipeline will:

1. Run unit tests
2. Run integration tests
3. Generate test coverage reports
4. Deploy to Heroku (if tests pass)
5. Run database migrations on Heroku
6. Collect static files on Heroku
7. Send a deployment notification to Slack (if configured)

## Troubleshooting

If the CI/CD pipeline fails, check the GitHub Actions logs for error messages. Common issues include:

- Missing or incorrect secrets
- Failed tests
- Heroku deployment issues
- Database migration errors

For Heroku-specific issues, check the Heroku logs:

```
heroku logs --tail --app YOUR_APP_NAME
```
