from django.contrib import admin
from .models import Event, CalendarEvent, MeetingType, EventParticipant, EventReminder


@admin.register(MeetingType)
class MeetingTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'default_duration', 'color', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    ordering = ('name',)


class EventParticipantInline(admin.TabularInline):
    model = EventParticipant
    extra = 1
    fields = ('user', 'response', 'is_organizer', 'notes')


class EventReminderInline(admin.TabularInline):
    model = EventReminder
    extra = 1
    fields = ('participant', 'reminder_type', 'minutes_before', 'is_sent')


@admin.register(CalendarEvent)
class CalendarEventAdmin(admin.ModelAdmin):
    list_display = (
        'title', 'start_datetime', 'end_datetime', 'meeting_type',
        'location_type', 'status', 'organizer', 'related_candidate'
    )
    list_filter = (
        'status', 'meeting_type', 'location_type', 'start_datetime',
        'is_private', 'created_at'
    )
    search_fields = (
        'title', 'description', 'location', 'organizer__username',
        'organizer__first_name', 'organizer__last_name',
        'related_candidate__user__first_name', 'related_candidate__user__last_name'
    )
    date_hierarchy = 'start_datetime'
    inlines = [EventParticipantInline, EventReminderInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'organizer')
        }),
        ('Date & Time', {
            'fields': ('start_datetime', 'end_datetime', 'all_day')
        }),
        ('Meeting Details', {
            'fields': ('meeting_type', 'location_type', 'location')
        }),
        ('Related Records', {
            'fields': ('related_candidate', 'related_job_offer')
        }),
        ('Status & Privacy', {
            'fields': ('status', 'is_private')
        }),
        ('External Integration', {
            'fields': ('google_calendar_id', 'outlook_calendar_id'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'organizer', 'meeting_type', 'related_candidate', 'related_job_offer'
        )


@admin.register(EventParticipant)
class EventParticipantAdmin(admin.ModelAdmin):
    list_display = ('event', 'user', 'response', 'is_organizer', 'notified_at', 'responded_at')
    list_filter = ('response', 'is_organizer', 'created_at')
    search_fields = (
        'event__title', 'user__username', 'user__first_name', 'user__last_name'
    )
    date_hierarchy = 'created_at'


@admin.register(EventReminder)
class EventReminderAdmin(admin.ModelAdmin):
    list_display = (
        'event', 'participant', 'reminder_type', 'minutes_before',
        'is_sent', 'sent_at'
    )
    list_filter = ('reminder_type', 'is_sent', 'created_at')
    search_fields = (
        'event__title', 'participant__username',
        'participant__first_name', 'participant__last_name'
    )
    date_hierarchy = 'created_at'


# Keep the legacy Event admin
@admin.register(Event)
class LegacyEventAdmin(admin.ModelAdmin):
    list_display = ('title', 'date', 'location', 'registration_required')
    list_filter = ('date', 'registration_required')
    search_fields = ('title', 'description', 'location')
    date_hierarchy = 'date'
