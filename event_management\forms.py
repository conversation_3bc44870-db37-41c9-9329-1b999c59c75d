from django import forms
from django.contrib.auth import get_user_model
from django.utils import timezone
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, HTML
from .models import CalendarEvent, MeetingType, EventParticipant

User = get_user_model()


class CalendarEventForm(forms.ModelForm):
    """Form for creating and editing calendar events."""
    
    participants_list = forms.ModelMultipleChoiceField(
        queryset=User.objects.all(),
        widget=forms.CheckboxSelectMultiple,
        required=False,
        help_text="Select additional participants for this event"
    )

    class Meta:
        model = CalendarEvent
        fields = [
            'title', 'description', 'start_datetime', 'end_datetime', 
            'all_day', 'meeting_type', 'location_type', 'location',
            'related_candidate', 'related_job_offer', 'is_private'
        ]
        widgets = {
            'start_datetime': forms.DateTimeInput(
                attrs={'type': 'datetime-local', 'class': 'form-control'}
            ),
            'end_datetime': forms.DateTimeInput(
                attrs={'type': 'datetime-local', 'class': 'form-control'}
            ),
            'description': forms.Textarea(attrs={'rows': 3}),
            'location': forms.TextInput(
                attrs={'placeholder': 'Meeting room, address, or video call link'}
            ),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Set up crispy forms
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('title', css_class='form-group col-md-8 mb-3'),
                Column('meeting_type', css_class='form-group col-md-4 mb-3'),
            ),
            'description',
            Row(
                Column('start_datetime', css_class='form-group col-md-6 mb-3'),
                Column('end_datetime', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('location_type', css_class='form-group col-md-4 mb-3'),
                Column('location', css_class='form-group col-md-8 mb-3'),
            ),
            Row(
                Column('related_candidate', css_class='form-group col-md-6 mb-3'),
                Column('related_job_offer', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('all_day', css_class='form-group col-md-4 mb-3'),
                Column('is_private', css_class='form-group col-md-4 mb-3'),
            ),
            HTML('<h6 class="mt-3 mb-2">Additional Participants</h6>'),
            'participants_list',
            Submit('submit', 'Schedule Event', css_class='btn btn-primary mt-3')
        )
        
        # Filter candidates and job offers for current user if hiring partner
        if self.user and hasattr(self.user, 'hiring_partner_profile'):
            from application.models import ApplicantProfile
            from services.models import Offer
            
            # Filter candidates that have applied to this hiring partner's offers
            self.fields['related_candidate'].queryset = ApplicantProfile.objects.filter(
                applications__offer__hiring_partner=self.user.hiring_partner_profile
            ).distinct()
            
            # Filter job offers for this hiring partner
            self.fields['related_job_offer'].queryset = Offer.objects.filter(
                hiring_partner=self.user.hiring_partner_profile,
                is_active=True
            )
        
        # Set default end time to 1 hour after start time
        if not self.instance.pk and 'start_datetime' in self.initial:
            start_time = self.initial['start_datetime']
            if isinstance(start_time, str):
                start_time = timezone.datetime.fromisoformat(start_time)
            self.initial['end_datetime'] = start_time + timezone.timedelta(hours=1)

    def clean(self):
        cleaned_data = super().clean()
        start_datetime = cleaned_data.get('start_datetime')
        end_datetime = cleaned_data.get('end_datetime')
        
        if start_datetime and end_datetime:
            if end_datetime <= start_datetime:
                raise forms.ValidationError("End time must be after start time.")
            
            # Check if start time is in the past
            if start_datetime < timezone.now():
                raise forms.ValidationError("Cannot schedule events in the past.")
        
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Set organizer to current user
        if self.user:
            instance.organizer = self.user
        
        if commit:
            instance.save()
            
            # Handle participants
            participants = self.cleaned_data.get('participants_list', [])
            
            # Add organizer as participant
            EventParticipant.objects.get_or_create(
                event=instance,
                user=instance.organizer,
                defaults={'is_organizer': True, 'response': 'accepted'}
            )
            
            # Add other participants
            for participant in participants:
                EventParticipant.objects.get_or_create(
                    event=instance,
                    user=participant,
                    defaults={'response': 'pending'}
                )
        
        return instance


class QuickScheduleForm(forms.ModelForm):
    """Simplified form for quick interview scheduling from candidate pipeline."""
    
    class Meta:
        model = CalendarEvent
        fields = [
            'title', 'start_datetime', 'meeting_type', 'location_type', 'location'
        ]
        widgets = {
            'start_datetime': forms.DateTimeInput(
                attrs={'type': 'datetime-local', 'class': 'form-control'}
            ),
            'location': forms.TextInput(
                attrs={'placeholder': 'Meeting room or video call link'}
            ),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.candidate = kwargs.pop('candidate', None)
        super().__init__(*args, **kwargs)
        
        # Set up crispy forms
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'title',
            Row(
                Column('start_datetime', css_class='form-group col-md-6 mb-3'),
                Column('meeting_type', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('location_type', css_class='form-group col-md-4 mb-3'),
                Column('location', css_class='form-group col-md-8 mb-3'),
            ),
            Submit('submit', 'Schedule Interview', css_class='btn btn-primary')
        )
        
        # Set default title if candidate is provided
        if self.candidate:
            candidate_name = self.candidate.user.get_full_name() or self.candidate.user.username
            self.fields['title'].initial = f"Interview with {candidate_name}"

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Set organizer and related candidate
        if self.user:
            instance.organizer = self.user
        if self.candidate:
            instance.related_candidate = self.candidate
        
        # Set default end time (1 hour after start)
        if instance.start_datetime and not instance.end_datetime:
            instance.end_datetime = instance.start_datetime + timezone.timedelta(hours=1)
        
        # Set default description
        if not instance.description and self.candidate:
            candidate_name = self.candidate.user.get_full_name() or self.candidate.user.username
            instance.description = f"Interview scheduled with candidate {candidate_name}"
        
        if commit:
            instance.save()
            
            # Add organizer and candidate as participants
            EventParticipant.objects.get_or_create(
                event=instance,
                user=instance.organizer,
                defaults={'is_organizer': True, 'response': 'accepted'}
            )
            
            if self.candidate and self.candidate.user:
                EventParticipant.objects.get_or_create(
                    event=instance,
                    user=self.candidate.user,
                    defaults={'response': 'pending'}
                )
        
        return instance


class EventResponseForm(forms.ModelForm):
    """Form for participants to respond to event invitations."""
    
    class Meta:
        model = EventParticipant
        fields = ['response', 'notes']
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3, 'placeholder': 'Optional notes...'})
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'response',
            'notes',
            Submit('submit', 'Update Response', css_class='btn btn-primary')
        )
