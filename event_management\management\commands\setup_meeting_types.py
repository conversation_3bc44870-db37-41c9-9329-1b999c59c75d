from django.core.management.base import BaseCommand
from event_management.models import MeetingType


class Command(BaseCommand):
    help = 'Create default meeting types for recruitment calendar'

    def handle(self, *args, **options):
        meeting_types = [
            {
                'name': 'Phone Screening',
                'description': 'Initial phone screening with candidate',
                'default_duration': 30,
                'color': '#28a745'
            },
            {
                'name': 'Video Interview',
                'description': 'Video interview via Zoom, Teams, or similar',
                'default_duration': 60,
                'color': '#007bff'
            },
            {
                'name': 'In-Person Interview',
                'description': 'Face-to-face interview at office location',
                'default_duration': 90,
                'color': '#6f42c1'
            },
            {
                'name': 'Technical Interview',
                'description': 'Technical assessment and coding interview',
                'default_duration': 120,
                'color': '#fd7e14'
            },
            {
                'name': 'Panel Interview',
                'description': 'Interview with multiple team members',
                'default_duration': 90,
                'color': '#dc3545'
            },
            {
                'name': 'Final Interview',
                'description': 'Final interview with decision makers',
                'default_duration': 60,
                'color': '#20c997'
            },
            {
                'name': 'Reference Check',
                'description': 'Call to check candidate references',
                'default_duration': 20,
                'color': '#6c757d'
            },
            {
                'name': 'Offer Discussion',
                'description': 'Discussion about job offer details',
                'default_duration': 30,
                'color': '#ffc107'
            },
        ]

        created_count = 0
        for meeting_type_data in meeting_types:
            meeting_type, created = MeetingType.objects.get_or_create(
                name=meeting_type_data['name'],
                defaults=meeting_type_data
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created meeting type: {meeting_type.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Meeting type already exists: {meeting_type.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} meeting types')
        )
