# Generated by Django 4.2.7 on 2025-05-26 17:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('application', '0002_refinedinfo'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('services', '0010_passivecandidate_outreachsequence_outreachattempt'),
        ('event_management', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CalendarEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('start_datetime', models.DateTimeField()),
                ('end_datetime', models.DateTimeField()),
                ('all_day', models.BooleanField(default=False)),
                ('location_type', models.Char<PERSON>ield(choices=[('in_person', 'In Person'), ('video_call', 'Video Call'), ('phone_call', 'Phone Call'), ('online', 'Online Meeting')], default='video_call', max_length=20)),
                ('location', models.CharField(blank=True, help_text='Physical address or meeting link', max_length=500)),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('confirmed', 'Confirmed'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('rescheduled', 'Rescheduled')], default='scheduled', max_length=20)),
                ('is_private', models.BooleanField(default=False)),
                ('google_calendar_id', models.CharField(blank=True, max_length=255, null=True)),
                ('outlook_calendar_id', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Calendar Event',
                'verbose_name_plural': 'Calendar Events',
                'ordering': ['start_datetime'],
            },
        ),
        migrations.CreateModel(
            name='MeetingType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('default_duration', models.PositiveIntegerField(default=60, help_text='Default duration in minutes')),
                ('color', models.CharField(default='#007bff', help_text='Hex color code for calendar display', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.AlterModelOptions(
            name='event',
            options={'ordering': ['date'], 'verbose_name': 'Legacy Event', 'verbose_name_plural': 'Legacy Events'},
        ),
        migrations.CreateModel(
            name='EventParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('response', models.CharField(choices=[('pending', 'Pending'), ('accepted', 'Accepted'), ('declined', 'Declined'), ('tentative', 'Tentative')], default='pending', max_length=20)),
                ('is_organizer', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True)),
                ('notified_at', models.DateTimeField(blank=True, null=True)),
                ('responded_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='event_management.calendarevent')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Event Participant',
                'verbose_name_plural': 'Event Participants',
                'unique_together': {('event', 'user')},
            },
        ),
        migrations.AddField(
            model_name='calendarevent',
            name='meeting_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='event_management.meetingtype'),
        ),
        migrations.AddField(
            model_name='calendarevent',
            name='organizer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='organized_events', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='calendarevent',
            name='participants',
            field=models.ManyToManyField(related_name='calendar_events', through='event_management.EventParticipant', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='calendarevent',
            name='related_candidate',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='scheduled_events', to='application.applicantprofile'),
        ),
        migrations.AddField(
            model_name='calendarevent',
            name='related_job_offer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='scheduled_events', to='services.offer'),
        ),
        migrations.CreateModel(
            name='EventReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reminder_type', models.CharField(choices=[('email', 'Email'), ('sms', 'SMS'), ('notification', 'In-App Notification')], default='email', max_length=20)),
                ('minutes_before', models.PositiveIntegerField(default=60, help_text='Minutes before event to send reminder')),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('is_sent', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='event_management.calendarevent')),
                ('participant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Event Reminder',
                'verbose_name_plural': 'Event Reminders',
                'unique_together': {('event', 'participant', 'reminder_type', 'minutes_before')},
            },
        ),
    ]
