from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

User = get_user_model()


class MeetingType(models.Model):
    """Types of meetings/interviews that can be scheduled."""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    default_duration = models.PositiveIntegerField(default=60, help_text="Default duration in minutes")
    color = models.CharField(max_length=7, default="#007bff", help_text="Hex color code for calendar display")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class CalendarEvent(models.Model):
    """Enhanced calendar event model for recruitment scheduling."""

    EVENT_STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('confirmed', 'Confirmed'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('rescheduled', 'Rescheduled'),
    ]

    MEETING_LOCATION_TYPES = [
        ('in_person', 'In Person'),
        ('video_call', 'Video Call'),
        ('phone_call', 'Phone Call'),
        ('online', 'Online Meeting'),
    ]

    # Basic event information
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # Date and time
    start_datetime = models.DateTimeField()
    end_datetime = models.DateTimeField()
    all_day = models.BooleanField(default=False)

    # Meeting details
    meeting_type = models.ForeignKey(MeetingType, on_delete=models.SET_NULL, null=True, blank=True)
    location_type = models.CharField(max_length=20, choices=MEETING_LOCATION_TYPES, default='video_call')
    location = models.CharField(max_length=500, blank=True, help_text="Physical address or meeting link")

    # Participants
    organizer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='organized_events')
    participants = models.ManyToManyField(User, through='EventParticipant', related_name='calendar_events')

    # Status and metadata
    status = models.CharField(max_length=20, choices=EVENT_STATUS_CHOICES, default='scheduled')
    is_private = models.BooleanField(default=False)

    # Integration fields
    google_calendar_id = models.CharField(max_length=255, blank=True, null=True)
    outlook_calendar_id = models.CharField(max_length=255, blank=True, null=True)

    # Candidate relationship
    related_candidate = models.ForeignKey(
        'application.ApplicantProfile',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='scheduled_events'
    )
    related_job_offer = models.ForeignKey(
        'services.Offer',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='scheduled_events'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['start_datetime']
        verbose_name = 'Calendar Event'
        verbose_name_plural = 'Calendar Events'

    def __str__(self):
        return f"{self.title} - {self.start_datetime.strftime('%Y-%m-%d %H:%M')}"

    def clean(self):
        """Validate that end_datetime is after start_datetime."""
        if self.end_datetime and self.start_datetime and self.end_datetime <= self.start_datetime:
            raise ValidationError("End time must be after start time.")

    @property
    def duration_minutes(self):
        """Calculate duration in minutes."""
        if self.end_datetime and self.start_datetime:
            delta = self.end_datetime - self.start_datetime
            return int(delta.total_seconds() / 60)
        return 0

    @property
    def is_upcoming(self):
        """Check if event is in the future."""
        return self.start_datetime > timezone.now()

    @property
    def is_today(self):
        """Check if event is today."""
        return self.start_datetime.date() == timezone.now().date()

    @property
    def can_be_cancelled(self):
        """Check if event can still be cancelled."""
        return self.status in ['scheduled', 'confirmed'] and self.is_upcoming


class EventParticipant(models.Model):
    """Through model for event participants with additional metadata."""

    RESPONSE_CHOICES = [
        ('pending', 'Pending'),
        ('accepted', 'Accepted'),
        ('declined', 'Declined'),
        ('tentative', 'Tentative'),
    ]

    event = models.ForeignKey(CalendarEvent, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    response = models.CharField(max_length=20, choices=RESPONSE_CHOICES, default='pending')
    is_organizer = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    notified_at = models.DateTimeField(null=True, blank=True)
    responded_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['event', 'user']
        verbose_name = 'Event Participant'
        verbose_name_plural = 'Event Participants'

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - {self.event.title}"


class EventReminder(models.Model):
    """Reminders for calendar events."""

    REMINDER_TYPES = [
        ('email', 'Email'),
        ('sms', 'SMS'),
        ('notification', 'In-App Notification'),
    ]

    event = models.ForeignKey(CalendarEvent, on_delete=models.CASCADE, related_name='reminders')
    participant = models.ForeignKey(User, on_delete=models.CASCADE)
    reminder_type = models.CharField(max_length=20, choices=REMINDER_TYPES, default='email')
    minutes_before = models.PositiveIntegerField(default=60, help_text="Minutes before event to send reminder")
    sent_at = models.DateTimeField(null=True, blank=True)
    is_sent = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['event', 'participant', 'reminder_type', 'minutes_before']
        verbose_name = 'Event Reminder'
        verbose_name_plural = 'Event Reminders'

    def __str__(self):
        return f"Reminder for {self.participant.get_full_name()} - {self.event.title}"

    @property
    def reminder_datetime(self):
        """Calculate when the reminder should be sent."""
        return self.event.start_datetime - timezone.timedelta(minutes=self.minutes_before)


# Keep the original Event model for backward compatibility
class Event(models.Model):
    """Legacy event model - kept for backward compatibility."""
    title = models.CharField(max_length=200)
    description = models.TextField()
    date = models.DateField()
    time = models.TimeField(null=True, blank=True)
    location = models.CharField(max_length=200, null=True, blank=True)
    image = models.ImageField(upload_to='events/', null=True, blank=True)
    capacity = models.PositiveIntegerField(null=True, blank=True)
    registration_required = models.BooleanField(default=False)
    registration_info = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['date']
        verbose_name = 'Legacy Event'
        verbose_name_plural = 'Legacy Events'

    def __str__(self):
        return self.title

    @property
    def is_upcoming(self):
        return self.date >= timezone.now().date()
