from django.urls import path
from . import views

app_name = 'event_management'

urlpatterns = [
    # Legacy event URLs
    path('legacy/', views.event_list, name='event_list'),
    path('legacy/<int:event_id>/', views.event_detail, name='event_detail'),

    # Calendar URLs
    path('calendar/', views.calendar_view, name='calendar'),
    path('calendar/create/', views.create_event, name='create_event'),
    path('calendar/<int:event_id>/', views.event_detail_view, name='event_detail'),
    path('calendar/<int:event_id>/respond/', views.respond_to_event, name='respond_to_event'),
    path('calendar/<int:event_id>/cancel/', views.cancel_event, name='cancel_event'),

    # Quick scheduling URLs
    path('schedule/interview/<int:candidate_id>/', views.quick_schedule_interview, name='quick_schedule_interview'),

    # API URLs
    path('api/events.json', views.get_calendar_events_json, name='calendar_events_json'),
]