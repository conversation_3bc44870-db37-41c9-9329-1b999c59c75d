from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Q
from datetime import datetime, timedelta
import json

from .models import Event, CalendarEvent, MeetingType, EventParticipant
from .forms import CalendarEventForm, QuickScheduleForm, EventResponseForm
from application.models import ApplicantProfile


def event_list(request):
    """
    View for displaying a list of all legacy events.
    """
    events = Event.objects.filter(is_upcoming=True)
    return render(request, 'event_management/event_list.html', {'events': events})


def event_detail(request, event_id):
    """
    View for displaying details of a specific legacy event.
    """
    event = get_object_or_404(Event, id=event_id)
    return render(request, 'event_management/event_detail.html', {'event': event})


@login_required
def calendar_view(request):
    """
    Main calendar view showing user's events.
    """
    # Get current date or date from URL parameter
    date_str = request.GET.get('date')
    if date_str:
        try:
            current_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            current_date = timezone.now().date()
    else:
        current_date = timezone.now().date()

    # Calculate date ranges for different views
    week_start = current_date - timedelta(days=current_date.weekday())
    week_end = week_start + timedelta(days=6)
    month_start = current_date.replace(day=1)
    next_month = month_start.replace(month=month_start.month + 1) if month_start.month < 12 else month_start.replace(year=month_start.year + 1, month=1)
    month_end = next_month - timedelta(days=1)

    # Get view type (day, week, month)
    view_type = request.GET.get('view', 'week')

    # Filter events based on user's role
    if hasattr(request.user, 'hiring_partner_profile'):
        # Hiring partners see their organized events and events they're invited to
        events = CalendarEvent.objects.filter(
            Q(organizer=request.user) | Q(participants=request.user)
        ).distinct()
    else:
        # Applicants see events they're invited to
        events = CalendarEvent.objects.filter(participants=request.user)

    # Filter by date range based on view type
    if view_type == 'day':
        events = events.filter(
            start_datetime__date=current_date
        )
        date_range = f"{current_date.strftime('%B %d, %Y')}"
    elif view_type == 'week':
        events = events.filter(
            start_datetime__date__range=[week_start, week_end]
        )
        date_range = f"{week_start.strftime('%b %d')} - {week_end.strftime('%b %d, %Y')}"
    else:  # month
        events = events.filter(
            start_datetime__date__range=[month_start, month_end]
        )
        date_range = current_date.strftime('%B %Y')

    events = events.order_by('start_datetime')

    # Get upcoming events for sidebar
    upcoming_events = CalendarEvent.objects.filter(
        Q(organizer=request.user) | Q(participants=request.user),
        start_datetime__gte=timezone.now()
    ).distinct().order_by('start_datetime')[:5]

    context = {
        'events': events,
        'upcoming_events': upcoming_events,
        'current_date': current_date,
        'view_type': view_type,
        'date_range': date_range,
        'week_start': week_start,
        'week_end': week_end,
        'month_start': month_start,
        'month_end': month_end,
    }

    return render(request, 'event_management/calendar.html', context)


@login_required
def create_event(request):
    """
    Create a new calendar event.
    """
    if request.method == 'POST':
        form = CalendarEventForm(request.POST, user=request.user)
        if form.is_valid():
            event = form.save()
            messages.success(request, f'Event "{event.title}" has been scheduled successfully.')
            return redirect('event_management:calendar')
    else:
        # Pre-fill start time if provided
        initial_data = {}
        if 'start_time' in request.GET:
            try:
                start_time = datetime.fromisoformat(request.GET['start_time'])
                initial_data['start_datetime'] = start_time
                initial_data['end_datetime'] = start_time + timedelta(hours=1)
            except ValueError:
                pass

        form = CalendarEventForm(initial=initial_data, user=request.user)

    context = {
        'form': form,
        'title': 'Schedule New Event'
    }

    return render(request, 'event_management/create_event.html', context)


@login_required
def quick_schedule_interview(request, candidate_id):
    """
    Quick interview scheduling from candidate pipeline.
    """
    candidate = get_object_or_404(ApplicantProfile, id=candidate_id)

    # Check if user has permission to schedule interviews with this candidate
    if hasattr(request.user, 'hiring_partner_profile'):
        # Check if candidate has applied to any of this hiring partner's offers
        has_permission = candidate.applications.filter(
            offer__hiring_partner=request.user.hiring_partner_profile
        ).exists()
        if not has_permission:
            messages.error(request, "You don't have permission to schedule interviews with this candidate.")
            return redirect('services:pipeline')
    else:
        messages.error(request, "Only hiring partners can schedule interviews.")
        return redirect('core:home')

    if request.method == 'POST':
        form = QuickScheduleForm(request.POST, user=request.user, candidate=candidate)
        if form.is_valid():
            event = form.save()
            messages.success(request, f'Interview with {candidate.user.get_full_name() or candidate.user.username} has been scheduled.')
            return redirect('services:pipeline')
    else:
        form = QuickScheduleForm(user=request.user, candidate=candidate)

    context = {
        'form': form,
        'candidate': candidate,
        'title': f'Schedule Interview with {candidate.user.get_full_name() or candidate.user.username}'
    }

    return render(request, 'event_management/quick_schedule.html', context)


@login_required
def event_detail_view(request, event_id):
    """
    View calendar event details.
    """
    event = get_object_or_404(CalendarEvent, id=event_id)

    # Check if user has permission to view this event
    if not (event.organizer == request.user or
            event.participants.filter(id=request.user.id).exists()):
        messages.error(request, "You don't have permission to view this event.")
        return redirect('event_management:calendar')

    # Get user's participation record
    participation = None
    if event.participants.filter(id=request.user.id).exists():
        participation = EventParticipant.objects.get(event=event, user=request.user)

    context = {
        'event': event,
        'participation': participation,
        'is_organizer': event.organizer == request.user,
    }

    return render(request, 'event_management/event_detail_view.html', context)


@login_required
def respond_to_event(request, event_id):
    """
    Allow participants to respond to event invitations.
    """
    event = get_object_or_404(CalendarEvent, id=event_id)

    # Check if user is a participant
    try:
        participation = EventParticipant.objects.get(event=event, user=request.user)
    except EventParticipant.DoesNotExist:
        messages.error(request, "You are not invited to this event.")
        return redirect('event_management:calendar')

    if request.method == 'POST':
        form = EventResponseForm(request.POST, instance=participation)
        if form.is_valid():
            participation = form.save(commit=False)
            participation.responded_at = timezone.now()
            participation.save()
            messages.success(request, "Your response has been updated.")
            return redirect('event_management:event_detail', event_id=event.id)
    else:
        form = EventResponseForm(instance=participation)

    context = {
        'form': form,
        'event': event,
        'participation': participation,
    }

    return render(request, 'event_management/respond_to_event.html', context)


@login_required
def cancel_event(request, event_id):
    """
    Cancel a calendar event (organizer only).
    """
    event = get_object_or_404(CalendarEvent, id=event_id)

    # Check if user is the organizer
    if event.organizer != request.user:
        messages.error(request, "Only the event organizer can cancel events.")
        return redirect('event_management:calendar')

    if not event.can_be_cancelled:
        messages.error(request, "This event cannot be cancelled.")
        return redirect('event_management:calendar')

    if request.method == 'POST':
        event.status = 'cancelled'
        event.save()
        messages.success(request, f'Event "{event.title}" has been cancelled.')
        return redirect('event_management:calendar')

    context = {
        'event': event,
    }

    return render(request, 'event_management/cancel_event.html', context)


def get_calendar_events_json(request):
    """
    API endpoint to get calendar events in JSON format for calendar widgets.
    """
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    # Get date range from request
    start_date = request.GET.get('start')
    end_date = request.GET.get('end')

    # Filter events based on user's role
    if hasattr(request.user, 'hiring_partner_profile'):
        events = CalendarEvent.objects.filter(
            Q(organizer=request.user) | Q(participants=request.user)
        ).distinct()
    else:
        events = CalendarEvent.objects.filter(participants=request.user)

    # Filter by date range if provided
    if start_date and end_date:
        try:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            events = events.filter(
                start_datetime__gte=start_dt,
                start_datetime__lte=end_dt
            )
        except ValueError:
            pass

    # Convert to JSON format
    events_data = []
    for event in events:
        events_data.append({
            'id': event.id,
            'title': event.title,
            'start': event.start_datetime.isoformat(),
            'end': event.end_datetime.isoformat(),
            'allDay': event.all_day,
            'color': event.meeting_type.color if event.meeting_type else '#007bff',
            'url': f'/events/calendar/{event.id}/',
            'extendedProps': {
                'meeting_type': event.meeting_type.name if event.meeting_type else '',
                'location_type': event.get_location_type_display(),
                'status': event.get_status_display(),
                'organizer': event.organizer.get_full_name() or event.organizer.username,
                'candidate': (event.related_candidate.user.get_full_name()
                            if event.related_candidate else ''),
            }
        })

    return JsonResponse(events_data, safe=False)
