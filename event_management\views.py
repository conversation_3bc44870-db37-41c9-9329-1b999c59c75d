from django.shortcuts import render, get_object_or_404
from .models import Event


def event_list(request):
    """
    View for displaying a list of all events.
    """
    events = Event.objects.filter(is_upcoming=True)
    return render(request, 'event_management/event_list.html', {'events': events})


def event_detail(request, event_id):
    """
    View for displaying details of a specific event.
    """
    event = get_object_or_404(Event, id=event_id)
    return render(request, 'event_management/event_detail.html', {'event': event})
