import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smarch.settings')
django.setup()

# Import models
from django.db import connection
from django.db.models import Q
from users.models import ApplicantProfile as UserApplicantProfile
from application.models import ApplicationFormResponse

# Get all form responses
responses = ApplicationFormResponse.objects.all()
print(f"Found {len(responses)} form responses")

# For each response, update the foreign key
for response in responses:
    try:
        # Get the user from the old applicant profile
        user = response.applicant.user
        
        # Find the corresponding profile in the users app
        new_profile = UserApplicantProfile.objects.get(user=user)
        
        # Update the foreign key
        response.applicant = new_profile
        response.save()
        
        print(f"Updated response {response.id}")
    except Exception as e:
        print(f"Error updating response {response.id}: {e}")

print("Database update completed")
