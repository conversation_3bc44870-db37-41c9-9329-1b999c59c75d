-- Create a temporary table to store the mapping between old and new profile IDs
CREATE TEMPORARY TABLE profile_mapping (
    old_id INTEGER,
    new_id INTEGER
);

-- Insert the mapping data
INSERT INTO profile_mapping (old_id, new_id)
SELECT ap.id, up.id
FROM application_applicantprofile ap
JOIN users_applicantprofile up ON ap.user_id = up.user_id;

-- Update the foreign keys in the application_applicationformresponse table
UPDATE application_applicationformresponse
SET applicant_id = (
    SELECT new_id
    FROM profile_mapping
    WHERE old_id = application_applicationformresponse.applicant_id
)
WHERE EXISTS (
    SELECT 1
    FROM profile_mapping
    WHERE old_id = application_applicationformresponse.applicant_id
);

-- Drop the temporary table
DROP TABLE profile_mapping;
