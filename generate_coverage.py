#!/usr/bin/env python
"""
Script to generate test coverage reports for the Smarch platform.
"""
import os
import sys
import django
import coverage
from django.conf import settings
from django.test.utils import get_runner

if __name__ == "__main__":
    # Start coverage
    cov = coverage.Coverage(
        source=['services', 'users', 'subscription', 'contact_management'],
        omit=['*/tests/*', '*/migrations/*', '*/admin.py', '*/apps.py']
    )
    cov.start()
    
    # Set up Django
    os.environ['DJANGO_SETTINGS_MODULE'] = 'smarch.settings'
    django.setup()
    
    # Get the test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2, interactive=True)
    
    # Run the tests
    print("Running tests with coverage...")
    failures = test_runner.run_tests(['services', 'users', 'subscription', 'contact_management', 'tests'])
    
    # Stop coverage and generate report
    cov.stop()
    cov.save()
    
    # Generate HTML report
    print("Generating coverage report...")
    cov.html_report(directory='htmlcov')
    
    # Print coverage report
    print("Coverage report:")
    cov.report()
    
    # Exit with appropriate code
    sys.exit(bool(failures))
