from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.conf import settings

from .models import Conversation, Message
from django.contrib.auth import get_user_model

# Import the email notification service
try:
    from notifications.utils import EmailNotificationService
    email_service_available = True
except ImportError:
    email_service_available = False

User = get_user_model()


@login_required
def inbox(request):
    """View for displaying the user's inbox (list of conversations)."""
    # Get all conversations for the current user
    conversations = Conversation.objects.filter(
        participants=request.user
    ).prefetch_related('participants', 'messages')

    # Paginate the results
    paginator = Paginator(conversations, 10)  # Show 10 conversations per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Count unread messages for each conversation
    for conversation in page_obj:
        conversation.unread_count = conversation.messages.filter(
            sender__id__in=conversation.participants.exclude(id=request.user.id).values_list('id', flat=True),
            is_read=False
        ).count()

    return render(request, 'messaging/inbox.html', {
        'page_obj': page_obj,
        'total_conversations': conversations.count(),
    })


@login_required
def conversation_detail(request, conversation_id):
    """View for displaying a specific conversation."""
    # Get the conversation
    conversation = get_object_or_404(
        Conversation.objects.prefetch_related('messages', 'participants'),
        id=conversation_id,
        participants=request.user
    )

    # Get the other participant
    other_participant = conversation.get_other_participant(request.user)

    # Mark all messages from the other participant as read
    Message.objects.filter(
        conversation=conversation,
        sender=other_participant,
        is_read=False
    ).update(is_read=True)

    return render(request, 'messaging/conversation_detail.html', {
        'conversation': conversation,
        'other_participant': other_participant,
    })


@login_required
def new_conversation(request, recipient_id):
    """View for starting a new conversation with a user."""
    # Get the recipient
    recipient = get_object_or_404(User, id=recipient_id)

    # Check if a conversation already exists between these users
    existing_conversation = Conversation.objects.filter(
        participants=request.user
    ).filter(
        participants=recipient
    ).first()

    if existing_conversation:
        # If a conversation already exists, redirect to it
        return redirect('messaging:conversation_detail', conversation_id=existing_conversation.id)

    # Create a new conversation
    conversation = Conversation.objects.create()
    conversation.participants.add(request.user, recipient)

    return redirect('messaging:conversation_detail', conversation_id=conversation.id)


@login_required
def send_message(request, conversation_id):
    """View for sending a message in a conversation."""
    if request.method != 'POST':
        return redirect('messaging:conversation_detail', conversation_id=conversation_id)

    # Get the conversation
    conversation = get_object_or_404(
        Conversation,
        id=conversation_id,
        participants=request.user
    )

    # Get the message content
    content = request.POST.get('content', '').strip()

    if not content:
        messages.error(request, "Message cannot be empty.")
        return redirect('messaging:conversation_detail', conversation_id=conversation_id)

    # Create the message
    message = Message.objects.create(
        conversation=conversation,
        sender=request.user,
        content=content
    )

    # Update the conversation's updated_at timestamp
    conversation.save()  # This will update the auto_now field

    # Send email notification if the service is available
    if email_service_available:
        try:
            # Get the recipient (the other participant in the conversation)
            recipient = conversation.get_other_participant(request.user)
            if recipient:
                EmailNotificationService.send_new_message_notification(message)
        except Exception as e:
            # Log the error but don't prevent the message from being sent
            print(f"Error sending message notification: {str(e)}")

    return redirect('messaging:conversation_detail', conversation_id=conversation_id)


@login_required
def mark_as_read(request, message_id):
    """AJAX view for marking a message as read."""
    # Get the message
    message = get_object_or_404(
        Message,
        id=message_id,
        conversation__participants=request.user
    )

    # Mark the message as read
    message.mark_as_read()

    return JsonResponse({'success': True})
