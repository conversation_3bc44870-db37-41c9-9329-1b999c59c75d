from django.contrib import admin
from .models import NotificationPreference, Notification, EmailLog


@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    list_display = ('user', 'email_new_messages', 'email_contact_requests',
                    'email_application_updates', 'email_subscription_updates')
    search_fields = ('user__username', 'user__email')
    list_filter = ('email_new_messages', 'email_contact_requests',
                   'email_application_updates', 'email_subscription_updates')


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('user', 'notification_type', 'title', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('user__username', 'user__email', 'title', 'message')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'


@admin.register(EmailLog)
class EmailLogAdmin(admin.ModelAdmin):
    list_display = ('recipient', 'subject', 'status', 'sent_at')
    list_filter = ('status', 'sent_at')
    search_fields = ('recipient', 'subject', 'body')
    readonly_fields = ('sent_at',)
    date_hierarchy = 'sent_at'
