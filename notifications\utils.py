import logging
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone

from .models import EmailLog, Notification

User = get_user_model()
logger = logging.getLogger(__name__)


class EmailNotificationService:
    """Service for sending email notifications."""

    @staticmethod
    def send_email(recipient_email, subject, template_name, context, related_user=None, content_object=None):
        """
        Send an email using a template.
        
        Args:
            recipient_email (str): Email address of the recipient
            subject (str): Email subject
            template_name (str): Path to the email template
            context (dict): Context data for the template
            related_user (User, optional): User related to this email
            content_object (Model, optional): Object related to this email
        
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            # Render HTML content
            html_content = render_to_string(template_name, context)
            # Create plain text content
            text_content = strip_tags(html_content)
            
            # Create email message
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[recipient_email]
            )
            email.attach_alternative(html_content, "text/html")
            
            # Send email
            email.send()
            
            # Log the email
            email_log = EmailLog(
                recipient=recipient_email,
                subject=subject,
                body=text_content,
                sent_at=timezone.now(),
                status='sent',
                related_user=related_user
            )
            
            # Add content object if provided
            if content_object:
                content_type = ContentType.objects.get_for_model(content_object)
                email_log.content_type = content_type
                email_log.object_id = content_object.id
            
            email_log.save()
            
            return True
        except Exception as e:
            logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
            
            # Log the failed email
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                body=f"Failed to send: {str(e)}",
                sent_at=timezone.now(),
                status='failed',
                related_user=related_user
            )
            
            return False

    @classmethod
    def send_new_message_notification(cls, message):
        """
        Send notification about a new message.
        
        Args:
            message: The message object
        """
        recipient = message.conversation.get_other_participant(message.sender)
        
        if not recipient:
            logger.error(f"Could not find recipient for message {message.id}")
            return False
        
        # Check if user wants to receive message notifications
        try:
            preferences = recipient.notification_preferences
            if not preferences.email_new_messages:
                return False
        except:
            # If preferences don't exist, default to sending
            pass
        
        context = {
            'recipient': recipient,
            'sender': message.sender,
            'message': message,
            'conversation': message.conversation,
            'site_url': settings.SITE_URL if hasattr(settings, 'SITE_URL') else '/',
        }
        
        # Create notification
        Notification.objects.create(
            user=recipient,
            notification_type='message',
            title=f"New message from {message.sender.username}",
            message=message.content[:100] + ('...' if len(message.content) > 100 else ''),
            content_object=message
        )
        
        return cls.send_email(
            recipient_email=recipient.email,
            subject=f"New message from {message.sender.username}",
            template_name='emails/new_message.html',
            context=context,
            related_user=recipient,
            content_object=message
        )

    @classmethod
    def send_contact_request_notification(cls, contact_request):
        """
        Send notification about a new contact request.
        
        Args:
            contact_request: The contact request object
        """
        recipient = contact_request.recipient.user
        
        # Check if user wants to receive contact request notifications
        try:
            preferences = recipient.notification_preferences
            if not preferences.email_contact_requests:
                return False
        except:
            # If preferences don't exist, default to sending
            pass
        
        context = {
            'recipient': recipient,
            'sender': contact_request.sender,
            'contact_request': contact_request,
            'site_url': settings.SITE_URL if hasattr(settings, 'SITE_URL') else '/',
        }
        
        # Create notification
        Notification.objects.create(
            user=recipient,
            notification_type='contact_request',
            title=f"New contact request from {contact_request.sender.company_name}",
            message=contact_request.message[:100] + ('...' if len(contact_request.message) > 100 else ''),
            content_object=contact_request
        )
        
        return cls.send_email(
            recipient_email=recipient.email,
            subject=f"New contact request from {contact_request.sender.company_name}",
            template_name='emails/contact_request.html',
            context=context,
            related_user=recipient,
            content_object=contact_request
        )

    @classmethod
    def send_contact_form_notification(cls, contact_form_data):
        """
        Send notification about a new contact form submission.
        
        Args:
            contact_form_data: The contact form data
        """
        # Send to admin
        admin_emails = [email for name, email in settings.ADMINS]
        
        if not admin_emails:
            logger.warning("No admin emails configured for contact form notifications")
            return False
        
        context = {
            'name': contact_form_data.name,
            'email': contact_form_data.email,
            'subject': contact_form_data.subject,
            'message': contact_form_data.message,
            'site_url': settings.SITE_URL if hasattr(settings, 'SITE_URL') else '/',
        }
        
        # Send confirmation to the user
        cls.send_email(
            recipient_email=contact_form_data.email,
            subject="Thank you for contacting us",
            template_name='emails/contact_form_confirmation.html',
            context=context,
            content_object=contact_form_data
        )
        
        # Send notification to admins
        for admin_email in admin_emails:
            cls.send_email(
                recipient_email=admin_email,
                subject=f"New contact form submission: {contact_form_data.subject}",
                template_name='emails/contact_form_submission.html',
                context=context,
                content_object=contact_form_data
            )
        
        return True
