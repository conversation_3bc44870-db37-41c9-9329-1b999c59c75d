from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse

from .models import NotificationPreference, Notification


@login_required
def notification_preferences(request):
    """View for managing notification preferences."""
    # Get or create notification preferences for the user
    preferences, created = NotificationPreference.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # Update preferences based on form data
        preferences.email_new_messages = request.POST.get('email_new_messages') == 'on'
        preferences.email_contact_requests = request.POST.get('email_contact_requests') == 'on'
        preferences.email_application_updates = request.POST.get('email_application_updates') == 'on'
        preferences.email_subscription_updates = request.POST.get('email_subscription_updates') == 'on'
        preferences.save()

        messages.success(request, "Your notification preferences have been updated.")
        return redirect('notifications:preferences')

    return render(request, 'notifications/preferences.html', {
        'preferences': preferences
    })


@login_required
def mark_notification_as_read(request, notification_id):
    """Mark a notification as read."""
    notification = get_object_or_404(Notification, id=notification_id, user=request.user)
    notification.mark_as_read()

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({'success': True})

    # If not an AJAX request, redirect back to the referring page
    return redirect(request.META.get('HTTP_REFERER', 'core:home'))


@login_required
def mark_all_notifications_as_read(request):
    """Mark all notifications as read."""
    Notification.objects.filter(user=request.user, is_read=False).update(is_read=True)

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({'success': True})

    # If not an AJAX request, redirect back to the referring page
    return redirect(request.META.get('HTTP_REFERER', 'core:home'))
