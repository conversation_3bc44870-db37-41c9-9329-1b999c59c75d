#!/usr/bin/env python
"""
Script to run integration tests for the Smarch platform.
"""
import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner
import unittest

if __name__ == "__main__":
    # Set up Django
    os.environ['DJANGO_SETTINGS_MODULE'] = 'smarch.settings'
    django.setup()
    
    # Get the test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2, interactive=True)
    
    # Run the tests
    print("Running integration tests...")
    test_suite = unittest.TestLoader().discover('tests')
    failures = test_runner.run_tests(['tests'])
    
    # Exit with appropriate code
    sys.exit(bool(failures))
