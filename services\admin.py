from django.contrib import admin
from .models import (
    Offer, Subscription, ContactRequest, Payment,
    JobRequirement, CandidateScore, MatchingCriteria,
    PassiveCandidate, OutreachSequence, OutreachAttempt
)


@admin.register(Offer)
class OfferAdmin(admin.ModelAdmin):
    list_display = ('title', 'hiring_partner', 'price', 'is_premium', 'is_active')
    list_filter = ('is_premium', 'is_active', 'created_at')
    search_fields = ('title', 'description', 'hiring_partner__company_name')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = ('hiring_partner', 'plan', 'start_date', 'end_date', 'status')
    list_filter = ('plan', 'status', 'start_date')
    search_fields = ('hiring_partner__company_name', 'hiring_partner__user__email')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(ContactRequest)
class ContactRequestAdmin(admin.ModelAdmin):
    list_display = ('sender', 'recipient', 'status', 'sent_at', 'responded_at')
    list_filter = ('status', 'sent_at')
    search_fields = (
        'sender__company_name',
        'recipient__user__username',
        'message'
    )
    readonly_fields = ('sent_at',)


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = (
        'transaction_id',
        'subscription',
        'amount',
        'status',
        'created_at'
    )
    list_filter = ('status', 'created_at')
    search_fields = (
        'transaction_id',
        'subscription__hiring_partner__company_name'
    )
    readonly_fields = ('created_at',)


@admin.register(JobRequirement)
class JobRequirementAdmin(admin.ModelAdmin):
    list_display = ('offer', 'min_experience_years', 'remote_work_allowed', 'created_at')
    list_filter = ('remote_work_allowed', 'created_at')
    search_fields = ('offer__title', 'offer__hiring_partner__company_name')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(CandidateScore)
class CandidateScoreAdmin(admin.ModelAdmin):
    list_display = ('candidate', 'offer', 'overall_score', 'quality_rating', 'calculated_at')
    list_filter = ('quality_rating', 'calculated_at', 'offer__hiring_partner')
    search_fields = ('candidate__user__first_name', 'candidate__user__last_name',
                    'offer__title', 'offer__hiring_partner__company_name')
    readonly_fields = ('calculated_at', 'created_at')


@admin.register(MatchingCriteria)
class MatchingCriteriaAdmin(admin.ModelAdmin):
    list_display = ('hiring_partner', 'strict_skill_matching', 'allow_junior_candidates',
                   'use_ai_scoring', 'created_at')
    list_filter = ('strict_skill_matching', 'allow_junior_candidates',
                  'prioritize_local_candidates', 'use_ai_scoring')
    search_fields = ('hiring_partner__company_name', 'hiring_partner__user__email')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(PassiveCandidate)
class PassiveCandidateAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'email', 'current_company', 'status', 'interest_level', 'discovery_source', 'created_at')
    list_filter = ('status', 'interest_level', 'discovery_source', 'discovered_by', 'created_at')
    search_fields = ('first_name', 'last_name', 'email', 'current_company', 'current_position')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('first_name', 'last_name', 'email', 'phone')
        }),
        ('Professional Information', {
            'fields': ('current_company', 'current_position', 'industry', 'location', 'experience_years')
        }),
        ('Skills & Profile', {
            'fields': ('skills', 'bio')
        }),
        ('Online Presence', {
            'fields': ('linkedin_url', 'github_url', 'portfolio_url')
        }),
        ('Sourcing Information', {
            'fields': ('discovery_source', 'discovered_by', 'discovery_notes')
        }),
        ('Engagement Tracking', {
            'fields': ('status', 'interest_level', 'last_contact_date', 'next_follow_up_date')
        }),
        ('Conversion', {
            'fields': ('converted_to_applicant',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(OutreachSequence)
class OutreachSequenceAdmin(admin.ModelAdmin):
    list_display = ('name', 'sequence_type', 'hiring_partner', 'is_active', 'max_attempts', 'created_at')
    list_filter = ('sequence_type', 'is_active', 'hiring_partner', 'created_at')
    search_fields = ('name', 'hiring_partner__company_name', 'subject_template')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'sequence_type', 'hiring_partner', 'is_active')
        }),
        ('Configuration', {
            'fields': ('delay_days', 'max_attempts')
        }),
        ('Templates', {
            'fields': ('subject_template', 'message_template')
        }),
        ('Targeting', {
            'fields': ('target_roles', 'target_industries', 'min_experience_years')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(OutreachAttempt)
class OutreachAttemptAdmin(admin.ModelAdmin):
    list_display = ('passive_candidate', 'sequence', 'attempt_type', 'attempt_number', 'status', 'scheduled_for', 'sent_at')
    list_filter = ('attempt_type', 'status', 'hiring_partner', 'scheduled_for', 'sent_at')
    search_fields = ('passive_candidate__first_name', 'passive_candidate__last_name',
                    'passive_candidate__email', 'subject', 'sequence__name')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('passive_candidate', 'sequence', 'hiring_partner')
        }),
        ('Attempt Details', {
            'fields': ('attempt_type', 'attempt_number', 'subject', 'message')
        }),
        ('Tracking', {
            'fields': ('status', 'scheduled_for', 'sent_at', 'opened_at', 'clicked_at', 'replied_at')
        }),
        ('Response', {
            'fields': ('response_received', 'response_content'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )