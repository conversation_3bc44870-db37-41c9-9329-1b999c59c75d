"""
AI-powered candidate matching service for Smarch platform.
Provides intelligent candidate-job matching with quality scoring.
"""

import re
from typing import List, Dict, Tuple, Optional
from django.db.models import Q
from users.models import ApplicantProfile
from .models import Offer, JobRequirement, CandidateScore, MatchingCriteria


class CandidateMatchingService:
    """Service for AI-powered candidate matching and scoring."""

    def __init__(self, hiring_partner):
        self.hiring_partner = hiring_partner
        self.matching_criteria = self._get_or_create_matching_criteria()

    def _get_or_create_matching_criteria(self):
        """Get or create matching criteria for the hiring partner."""
        criteria, created = MatchingCriteria.objects.get_or_create(
            hiring_partner=self.hiring_partner
        )
        return criteria

    def find_matching_candidates(self, offer: Offer, limit: int = 50) -> List[Dict]:
        """
        Find and score candidates for a specific job offer.
        Returns a list of candidates with their matching scores.
        """
        # Get job requirements
        requirements = self._get_or_create_job_requirements(offer)

        # Get all active candidates
        candidates = ApplicantProfile.objects.filter(
            user__is_active=True
        ).select_related('user')

        # Apply basic filters
        candidates = self._apply_basic_filters(candidates, requirements)

        # Score each candidate
        scored_candidates = []
        for candidate in candidates[:limit * 2]:  # Get more to filter later
            score_data = self._calculate_candidate_score(candidate, offer, requirements)
            if score_data['overall_score'] > 20:  # Minimum threshold
                scored_candidates.append(score_data)

        # Sort by score and return top matches
        scored_candidates.sort(key=lambda x: x['overall_score'], reverse=True)
        return scored_candidates[:limit]

    def _get_or_create_job_requirements(self, offer: Offer) -> JobRequirement:
        """Get or create job requirements from offer description."""
        requirements, created = JobRequirement.objects.get_or_create(
            offer=offer,
            defaults=self._extract_requirements_from_description(offer)
        )
        return requirements

    def _extract_requirements_from_description(self, offer: Offer) -> Dict:
        """Extract job requirements from offer description using NLP."""
        description = offer.description.lower()

        # Extract skills using common patterns
        skills = self._extract_skills_from_text(description)

        # Extract experience requirements
        experience = self._extract_experience_from_text(description)

        # Extract location preferences
        locations = self._extract_locations_from_text(description)

        # Extract languages
        languages = self._extract_languages_from_text(description)

        return {
            'required_skills': skills[:10],  # Top 10 skills
            'preferred_skills': skills[10:20] if len(skills) > 10 else [],
            'min_experience_years': experience.get('min', 0),
            'max_experience_years': experience.get('max'),
            'preferred_locations': locations,
            'required_languages': languages,
            'remote_work_allowed': 'remote' in description or 'work from home' in description,
        }

    def _extract_skills_from_text(self, text: str) -> List[str]:
        """Extract skills from job description text."""
        # Common technical skills patterns
        skill_patterns = [
            r'\b(python|java|javascript|react|angular|vue|node\.?js|django|flask)\b',
            r'\b(sql|mysql|postgresql|mongodb|redis|elasticsearch)\b',
            r'\b(aws|azure|gcp|docker|kubernetes|jenkins|git)\b',
            r'\b(html|css|bootstrap|tailwind|sass|less)\b',
            r'\b(machine learning|ai|data science|analytics|tableau)\b',
            r'\b(project management|agile|scrum|kanban|jira)\b',
            r'\b(communication|leadership|teamwork|problem solving)\b',
        ]

        skills = []
        for pattern in skill_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            skills.extend([match.title() for match in matches])

        # Remove duplicates while preserving order
        return list(dict.fromkeys(skills))

    def _extract_experience_from_text(self, text: str) -> Dict[str, Optional[int]]:
        """Extract experience requirements from text."""
        # Patterns for experience
        exp_patterns = [
            r'(\d+)[\+\-\s]*(?:to|-)?\s*(\d+)?\s*years?\s*(?:of\s*)?experience',
            r'minimum\s*(\d+)\s*years?',
            r'at least\s*(\d+)\s*years?',
            r'(\d+)\+\s*years?',
        ]

        min_exp = 0
        max_exp = None

        for pattern in exp_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                if isinstance(matches[0], tuple):
                    nums = [int(x) for x in matches[0] if x.isdigit()]
                    if len(nums) >= 2:
                        min_exp = min(nums)
                        max_exp = max(nums)
                    elif len(nums) == 1:
                        min_exp = nums[0]
                else:
                    min_exp = int(matches[0])
                break

        return {'min': min_exp, 'max': max_exp}

    def _extract_locations_from_text(self, text: str) -> List[str]:
        """Extract location preferences from text."""
        # Common location patterns
        location_patterns = [
            r'\b(new york|san francisco|los angeles|chicago|boston|seattle|austin|denver)\b',
            r'\b(london|paris|berlin|amsterdam|stockholm|copenhagen)\b',
            r'\b(toronto|vancouver|montreal)\b',
            r'\b(sydney|melbourne|brisbane)\b',
        ]

        locations = []
        for pattern in location_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            locations.extend([match.title() for match in matches])

        return list(dict.fromkeys(locations))

    def _extract_languages_from_text(self, text: str) -> List[str]:
        """Extract language requirements from text."""
        language_patterns = [
            r'\b(english|spanish|french|german|italian|portuguese|chinese|japanese|korean)\b',
            r'\b(mandarin|cantonese|arabic|hindi|russian|dutch|swedish|norwegian)\b',
        ]

        languages = []
        for pattern in language_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            languages.extend([match.title() for match in matches])

        return list(dict.fromkeys(languages))

    def _apply_basic_filters(self, candidates, requirements: JobRequirement):
        """Apply basic filters to candidate queryset."""
        filtered = candidates

        # Filter by minimum experience if specified
        if requirements.min_experience_years > 0:
            if not self.matching_criteria.allow_junior_candidates:
                filtered = filtered.filter(
                    position_experience_years__gte=requirements.min_experience_years
                )

        # Filter by location if specified and local preference is set
        if (requirements.preferred_locations and
            self.matching_criteria.prioritize_local_candidates):
            location_q = Q()
            for location in requirements.preferred_locations:
                location_q |= Q(location__icontains=location)
            filtered = filtered.filter(location_q)

        return filtered

    def _calculate_candidate_score(self, candidate: ApplicantProfile,
                                 offer: Offer, requirements: JobRequirement) -> Dict:
        """Calculate comprehensive matching score for a candidate."""

        # Get candidate data
        candidate_skills = self._get_candidate_skills(candidate)
        candidate_experience = getattr(candidate, 'position_experience_years', 0) or 0
        candidate_location = getattr(candidate, 'location', '') or ''
        candidate_languages = self._get_candidate_languages(candidate)

        # Calculate individual scores
        skills_score = self._calculate_skills_score(
            candidate_skills, requirements.required_skills, requirements.preferred_skills
        )

        experience_score = self._calculate_experience_score(
            candidate_experience, requirements.min_experience_years, requirements.max_experience_years
        )

        location_score = self._calculate_location_score(
            candidate_location, requirements.preferred_locations, requirements.remote_work_allowed
        )

        language_score = self._calculate_language_score(
            candidate_languages, requirements.required_languages
        )

        # Calculate weighted overall score
        weights = {
            'skills': requirements.skills_weight,
            'experience': requirements.experience_weight,
            'location': requirements.location_weight,
            'language': requirements.language_weight,
        }

        overall_score = (
            (skills_score * weights['skills'] +
             experience_score * weights['experience'] +
             location_score * weights['location'] +
             language_score * weights['language']) / 100
        )

        # Determine quality rating
        quality_rating = self._determine_quality_rating(overall_score)

        # Analyze skill gaps
        skill_analysis = self._analyze_skill_gaps(
            candidate_skills, requirements.required_skills, requirements.preferred_skills
        )

        return {
            'candidate': candidate,
            'offer': offer,
            'overall_score': round(overall_score, 1),
            'skills_score': round(skills_score, 1),
            'experience_score': round(experience_score, 1),
            'location_score': round(location_score, 1),
            'language_score': round(language_score, 1),
            'quality_rating': quality_rating,
            'matched_skills': skill_analysis['matched'],
            'missing_skills': skill_analysis['missing'],
            'skill_gaps': skill_analysis['gaps'],
        }

    def _get_candidate_skills(self, candidate: ApplicantProfile) -> List[str]:
        """Extract skills from candidate profile."""
        skills = []

        # Get skills from various sources
        if hasattr(candidate, 'skills') and candidate.skills:
            if isinstance(candidate.skills, list):
                skills.extend(candidate.skills)
            elif isinstance(candidate.skills, str):
                skills.extend([s.strip() for s in candidate.skills.split(',')])

        # Get skills from bio/description
        if candidate.bio:
            extracted_skills = self._extract_skills_from_text(candidate.bio.lower())
            skills.extend(extracted_skills)

        # Get skills from role/industry
        if candidate.role:
            role_skills = self._extract_skills_from_text(candidate.role.lower())
            skills.extend(role_skills)

        # Remove duplicates and clean up
        return list(dict.fromkeys([skill.strip().title() for skill in skills if skill.strip()]))

    def _get_candidate_languages(self, candidate: ApplicantProfile) -> List[str]:
        """Extract languages from candidate profile."""
        languages = []

        if hasattr(candidate, 'languages') and candidate.languages:
            if isinstance(candidate.languages, list):
                languages.extend(candidate.languages)
            elif isinstance(candidate.languages, str):
                languages.extend([l.strip() for l in candidate.languages.split(',')])

        return [lang.strip().title() for lang in languages if lang.strip()]

    def _calculate_skills_score(self, candidate_skills: List[str],
                               required_skills: List[str], preferred_skills: List[str]) -> float:
        """Calculate skills matching score."""
        if not required_skills and not preferred_skills:
            return 100.0

        candidate_skills_lower = [skill.lower() for skill in candidate_skills]
        required_lower = [skill.lower() for skill in required_skills]
        preferred_lower = [skill.lower() for skill in preferred_skills]

        # Calculate required skills match
        required_matches = sum(1 for skill in required_lower if skill in candidate_skills_lower)
        required_score = (required_matches / len(required_lower) * 100) if required_lower else 100

        # Calculate preferred skills match
        preferred_matches = sum(1 for skill in preferred_lower if skill in candidate_skills_lower)
        preferred_score = (preferred_matches / len(preferred_lower) * 100) if preferred_lower else 0

        # Weight: 70% required, 30% preferred
        return (required_score * 0.7) + (preferred_score * 0.3)

    def _calculate_experience_score(self, candidate_exp: int, min_exp: int, max_exp: Optional[int]) -> float:
        """Calculate experience matching score."""
        if min_exp == 0 and max_exp is None:
            return 100.0

        # Perfect match range
        if max_exp and min_exp <= candidate_exp <= max_exp:
            return 100.0

        # Above minimum but no maximum set
        if candidate_exp >= min_exp and max_exp is None:
            # Slight penalty for being overqualified
            if candidate_exp > min_exp + 5:
                return max(80.0, 100.0 - (candidate_exp - min_exp - 5) * 2)
            return 100.0

        # Below minimum
        if candidate_exp < min_exp:
            gap = min_exp - candidate_exp
            return max(0.0, 100.0 - (gap * 20))  # 20% penalty per year below

        # Above maximum
        if max_exp and candidate_exp > max_exp:
            excess = candidate_exp - max_exp
            return max(60.0, 100.0 - (excess * 10))  # 10% penalty per year above

        return 100.0

    def _calculate_location_score(self, candidate_location: str,
                                 preferred_locations: List[str], remote_allowed: bool) -> float:
        """Calculate location compatibility score."""
        if not preferred_locations:
            return 100.0

        if remote_allowed:
            return 100.0  # Remote work makes location irrelevant

        candidate_location_lower = candidate_location.lower()

        # Check for exact or partial matches
        for location in preferred_locations:
            if location.lower() in candidate_location_lower or candidate_location_lower in location.lower():
                return 100.0

        # No location match
        return 50.0 if self.matching_criteria.prioritize_local_candidates else 80.0

    def _calculate_language_score(self, candidate_languages: List[str],
                                 required_languages: List[str]) -> float:
        """Calculate language requirements score."""
        if not required_languages:
            return 100.0

        candidate_langs_lower = [lang.lower() for lang in candidate_languages]
        required_langs_lower = [lang.lower() for lang in required_languages]

        matches = sum(1 for lang in required_langs_lower if lang in candidate_langs_lower)
        return (matches / len(required_langs_lower)) * 100

    def _determine_quality_rating(self, overall_score: float) -> str:
        """Determine quality rating based on overall score."""
        if overall_score >= 85:
            return 'excellent'
        elif overall_score >= 70:
            return 'good'
        elif overall_score >= 50:
            return 'fair'
        else:
            return 'poor'

    def _analyze_skill_gaps(self, candidate_skills: List[str],
                           required_skills: List[str], preferred_skills: List[str]) -> Dict:
        """Analyze skill gaps and provide recommendations."""
        candidate_skills_lower = [skill.lower() for skill in candidate_skills]
        required_lower = [skill.lower() for skill in required_skills]
        preferred_lower = [skill.lower() for skill in preferred_skills]

        # Find matches
        matched_required = [skill for skill in required_skills
                           if skill.lower() in candidate_skills_lower]
        matched_preferred = [skill for skill in preferred_skills
                            if skill.lower() in candidate_skills_lower]

        # Find missing skills
        missing_required = [skill for skill in required_skills
                           if skill.lower() not in candidate_skills_lower]
        missing_preferred = [skill for skill in preferred_skills
                            if skill.lower() not in candidate_skills_lower]

        # Generate skill gap recommendations
        gaps = []
        if missing_required:
            gaps.append({
                'type': 'critical',
                'skills': missing_required,
                'recommendation': 'These required skills are missing and should be prioritized for training.'
            })

        if missing_preferred:
            gaps.append({
                'type': 'enhancement',
                'skills': missing_preferred,
                'recommendation': 'These preferred skills would enhance the candidate\'s profile.'
            })

        return {
            'matched': matched_required + matched_preferred,
            'missing': missing_required + missing_preferred,
            'gaps': gaps
        }

    def save_candidate_score(self, score_data: Dict) -> CandidateScore:
        """Save candidate score to database."""
        score, created = CandidateScore.objects.update_or_create(
            candidate=score_data['candidate'],
            offer=score_data['offer'],
            defaults={
                'hiring_partner': self.hiring_partner,
                'overall_score': score_data['overall_score'],
                'skills_score': score_data['skills_score'],
                'experience_score': score_data['experience_score'],
                'location_score': score_data['location_score'],
                'language_score': score_data['language_score'],
                'quality_rating': score_data['quality_rating'],
                'matched_skills': score_data['matched_skills'],
                'missing_skills': score_data['missing_skills'],
                'skill_gaps': score_data['skill_gaps'],
            }
        )
        return score
