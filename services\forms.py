from django import forms
from users.models import HiringPartnerProfile
from .models import Offer, Subscription, ContactRequest, Payment


class HiringPartnerForm(forms.ModelForm):
    """Form for hiring partners to register their company details."""
    class Meta:
        model = HiringPartnerProfile
        fields = [
            'company_name', 'company_size', 'profile_image', 'primary_location',
            'other_locations', 'branch', 'industry', 'company_description',
            'company_needs', 'average_wages', 'contract_types', 'working_hours',
            'work_location', 'phone_number', 'company_address', 'company_city',
            'company_state', 'company_zip', 'company_website', 'job_title',
            'services_offered'
        ]
        widgets = {
            'company_description': forms.Textarea(attrs={'rows': 4}),
            'company_needs': forms.Textarea(attrs={'rows': 4}),
            'services_offered': forms.Textarea(attrs={'rows': 4}),
            'other_locations': forms.TextInput(
                attrs={'placeholder': 'e.g., Stockholm, Gothenburg, Malmö'}
            ),
        }
        help_texts = {
            'company_name': 'Enter your company or organization name',
            'company_size': 'Select the size of your company',
            'primary_location': 'Enter your primary business location',
            'other_locations': 'Enter other locations where you operate (comma-separated)',
            'branch': 'Enter your industry branch or sector',
            'industry': 'Select your primary industry',
            'company_description': 'Provide a brief description of your company',
            'company_needs': 'Describe your hiring needs and requirements',
            'average_wages': 'Enter the average wage range for positions (optional)',
            'contract_types': 'Specify the types of contracts you offer (e.g., full-time, part-time)',
            'working_hours': 'Describe your typical working hours (optional)',
            'work_location': 'Select the primary work location type',
            'phone_number': 'Enter your business contact number',
            'company_address': 'Enter your company address',
            'company_city': 'Enter your company city',
            'company_state': 'Enter your company state/province',
            'company_zip': 'Enter your company postal code',
            'company_website': 'Enter your company website URL (optional)',
            'job_title': 'Enter your job title within the company (optional)',
            'services_offered': 'Describe the services your company offers'
        }


class OfferForm(forms.ModelForm):
    """Form for creating or updating service offers."""
    class Meta:
        model = Offer
        fields = ['title', 'description', 'price', 'is_premium', 'is_active']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4}),
        }


class SubscriptionForm(forms.ModelForm):
    """Form for managing hiring partner subscriptions."""
    class Meta:
        model = Subscription
        fields = ['plan', 'start_date', 'end_date', 'status', 'is_auto_renew', 'payment_method']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
        }


class ContactRequestForm(forms.ModelForm):
    """Form for submitting contact requests."""
    class Meta:
        model = ContactRequest
        fields = ['message']
        widgets = {
            'message': forms.Textarea(attrs={'rows': 4, 'placeholder': 'Enter your message to the applicant...'}),
        }
        labels = {
            'message': 'Your Message',
        }
        help_texts = {
            'message': 'Introduce yourself and explain why you are interested in connecting with this applicant.',
        }


class PaymentForm(forms.ModelForm):
    """Form for processing payments."""
    class Meta:
        model = Payment
        fields = ['amount', 'payment_method', 'status', 'billing_details']
        widgets = {
            'billing_details': forms.Textarea(attrs={'rows': 3}),
        }