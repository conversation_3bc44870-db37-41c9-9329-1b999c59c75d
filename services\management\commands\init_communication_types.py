from django.core.management.base import BaseCommand
from services.models import CommunicationType


class Command(BaseCommand):
    help = 'Initialize communication types in the database'

    def handle(self, *args, **options):
        communication_types = [
            {
                'name': 'email',
                'display_name': 'Email',
                'icon': 'bi-envelope',
                'color': '#0d6efd'
            },
            {
                'name': 'phone',
                'display_name': 'Phone Call',
                'icon': 'bi-telephone',
                'color': '#198754'
            },
            {
                'name': 'meeting',
                'display_name': 'Meeting',
                'icon': 'bi-calendar-event',
                'color': '#fd7e14'
            },
            {
                'name': 'interview',
                'display_name': 'Interview',
                'icon': 'bi-camera-video',
                'color': '#dc3545'
            },
            {
                'name': 'message',
                'display_name': 'Platform Message',
                'icon': 'bi-chat-dots',
                'color': '#6f42c1'
            },
            {
                'name': 'note',
                'display_name': 'Internal Note',
                'icon': 'bi-sticky',
                'color': '#6c757d'
            }
        ]

        created_count = 0
        for comm_type_data in communication_types:
            comm_type, created = CommunicationType.objects.get_or_create(
                name=comm_type_data['name'],
                defaults=comm_type_data
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created communication type: {comm_type.display_name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Communication type already exists: {comm_type.display_name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully initialized {created_count} new communication types')
        )
