from django.core.management.base import BaseCommand
from services.models import CommunicationType


class Command(BaseCommand):
    help = 'Create default communication types for recruitment'

    def handle(self, *args, **options):
        communication_types = [
            {
                'name': 'email',
                'display_name': 'Email',
                'icon': 'bi-envelope',
                'color': '#007bff'
            },
            {
                'name': 'phone',
                'display_name': 'Phone Call',
                'icon': 'bi-telephone',
                'color': '#28a745'
            },
            {
                'name': 'meeting',
                'display_name': 'Meeting',
                'icon': 'bi-calendar-event',
                'color': '#6f42c1'
            },
            {
                'name': 'message',
                'display_name': 'Platform Message',
                'icon': 'bi-chat-dots',
                'color': '#17a2b8'
            },
            {
                'name': 'interview',
                'display_name': 'Interview',
                'icon': 'bi-person-video2',
                'color': '#fd7e14'
            },
            {
                'name': 'note',
                'display_name': 'Internal Note',
                'icon': 'bi-sticky',
                'color': '#ffc107'
            },
        ]

        for comm_type_data in communication_types:
            comm_type, created = CommunicationType.objects.get_or_create(
                name=comm_type_data['name'],
                defaults=comm_type_data
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created communication type: {comm_type.display_name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Communication type already exists: {comm_type.display_name}')
                )

        self.stdout.write(
            self.style.SUCCESS('Communication types setup completed!')
        )
