from django.core.management.base import BaseCommand
from services.models import PipelineStage


class Command(BaseCommand):
    help = 'Create default pipeline stages for recruitment'

    def handle(self, *args, **options):
        stages = [
            {'name': 'new', 'display_name': 'New', 'order': 1, 'color': '#17a2b8'},
            {'name': 'screening', 'display_name': 'Screening', 'order': 2, 'color': '#ffc107'},
            {'name': 'interview', 'display_name': 'Interview', 'order': 3, 'color': '#fd7e14'},
            {'name': 'offer', 'display_name': 'Offer', 'order': 4, 'color': '#6f42c1'},
            {'name': 'hired', 'display_name': 'Hired', 'order': 5, 'color': '#28a745'},
            {'name': 'rejected', 'display_name': 'Rejected', 'order': 6, 'color': '#dc3545'},
        ]

        for stage_data in stages:
            stage, created = PipelineStage.objects.get_or_create(
                name=stage_data['name'],
                defaults=stage_data
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created stage: {stage.display_name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Stage already exists: {stage.display_name}')
                )

        self.stdout.write(
            self.style.SUCCESS('Pipeline stages setup completed!')
        )
