# Generated by Django 4.2.7 on 2025-04-17 10:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0003_alter_applicantprofile_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='HiringPartnerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(max_length=200)),
                ('company_size', models.CharField(choices=[('1-10', '1-10 employees'), ('11-50', '11-50 employees'), ('51-200', '51-200 employees'), ('201-500', '201-500 employees'), ('501-1000', '501-1000 employees'), ('1001+', '1001+ employees')], max_length=20)),
                ('profile_image', models.ImageField(blank=True, null=True, upload_to='hiring_partners/')),
                ('primary_location', models.Char<PERSON>ield(max_length=150)),
                ('other_locations', models.JSONField(blank=True, null=True)),
                ('branch', models.CharField(max_length=100)),
                ('company_description', models.TextField(blank=True, null=True)),
                ('company_needs', models.TextField()),
                ('average_wages', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('contract_types', models.CharField(help_text='E.g., full-time, part-time, contract, internship, etc.', max_length=100)),
                ('working_hours', models.CharField(blank=True, max_length=100, null=True)),
                ('work_location', models.CharField(choices=[('remote', 'Remote'), ('on-site', 'On-site'), ('hybrid', 'Hybrid')], default='on-site', max_length=50)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('company_address', models.CharField(blank=True, max_length=255, null=True)),
                ('company_city', models.CharField(blank=True, max_length=100, null=True)),
                ('company_state', models.CharField(blank=True, max_length=100, null=True)),
                ('company_zip', models.CharField(blank=True, max_length=20, null=True)),
                ('company_website', models.URLField(blank=True, null=True)),
                ('job_title', models.CharField(blank=True, max_length=100, null=True)),
                ('services_offered', models.TextField(blank=True, null=True)),
                ('industry', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='services_hiring_partner_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Hiring Partner Profile',
                'verbose_name_plural': 'Hiring Partner Profiles',
            },
        ),
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan', models.CharField(choices=[('basic', 'Basic'), ('standard', 'Standard'), ('premium', 'Premium'), ('enterprise', 'Enterprise')], max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('cancelled', 'Cancelled'), ('expired', 'Expired'), ('pending', 'Pending')], default='pending', max_length=20)),
                ('is_auto_renew', models.BooleanField(default=True)),
                ('payment_method', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('hiring_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='services.hiringpartnerprofile')),
            ],
            options={
                'verbose_name': 'Subscription',
                'verbose_name_plural': 'Subscriptions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('refunded', 'Refunded')], max_length=20)),
                ('transaction_id', models.CharField(max_length=100, unique=True)),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('billing_details', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='services.subscription')),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Offer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('is_premium', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('hiring_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='offers', to='services.hiringpartnerprofile')),
            ],
            options={
                'verbose_name': 'Offer',
                'verbose_name_plural': 'Offers',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ContactRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField()),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('accepted', 'Accepted'), ('declined', 'Declined')], default='pending', max_length=20)),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('responded_at', models.DateTimeField(blank=True, null=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_requests', to='users.applicantprofile')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_requests', to='services.hiringpartnerprofile')),
            ],
            options={
                'verbose_name': 'Contact Request',
                'verbose_name_plural': 'Contact Requests',
                'ordering': ['-sent_at'],
            },
        ),
    ]
