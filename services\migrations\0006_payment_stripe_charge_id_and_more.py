# Generated by Django 4.2.7 on 2025-05-23 12:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0005_delete_hiringpartnerprofile'),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='stripe_charge_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='stripe_payment_intent_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='stripe_customer_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='stripe_payment_method_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='stripe_subscription_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]
