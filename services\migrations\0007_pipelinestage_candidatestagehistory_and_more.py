# Generated by Django 4.2.7 on 2025-05-26 12:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_alter_applicantprofile_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('services', '0006_payment_stripe_charge_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PipelineStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('new', 'New'), ('screening', 'Screening'), ('interview', 'Interview'), ('offer', 'Offer'), ('hired', 'Hired'), ('rejected', 'Rejected')], max_length=50, unique=True)),
                ('display_name', models.CharField(max_length=100)),
                ('order', models.IntegerField(default=0)),
                ('color', models.CharField(default='#6c757d', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Pipeline Stage',
                'verbose_name_plural': 'Pipeline Stages',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='CandidateStageHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notes', models.TextField(blank=True, null=True)),
                ('changed_at', models.DateTimeField(auto_now_add=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stage_history', to='users.applicantprofile')),
                ('changed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('hiring_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='candidate_stage_changes', to='users.hiringpartnerprofile')),
                ('previous_stage', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='candidate_exits', to='services.pipelinestage')),
                ('stage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='candidate_entries', to='services.pipelinestage')),
            ],
            options={
                'verbose_name': 'Candidate Stage History',
                'verbose_name_plural': 'Candidate Stage Histories',
                'ordering': ['-changed_at'],
            },
        ),
        migrations.CreateModel(
            name='CandidatePipeline',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='medium', max_length=10)),
                ('notes', models.TextField(blank=True, null=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pipeline_positions', to='users.applicantprofile')),
                ('current_stage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='current_candidates', to='services.pipelinestage')),
                ('hiring_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pipeline_candidates', to='users.hiringpartnerprofile')),
            ],
            options={
                'verbose_name': 'Candidate Pipeline',
                'verbose_name_plural': 'Candidate Pipelines',
                'ordering': ['-updated_at'],
                'unique_together': {('candidate', 'hiring_partner')},
            },
        ),
    ]
