# Generated by Django 4.2.7 on 2025-05-26 13:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('users', '0003_alter_applicantprofile_options_and_more'),
        ('services', '0007_pipelinestage_candidatestagehistory_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommunicationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(blank=True, max_length=200, null=True)),
                ('content', models.TextField()),
                ('scheduled_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('requires_follow_up', models.BooleanField(default=False)),
                ('follow_up_date', models.DateTimeField(blank=True, null=True)),
                ('follow_up_completed', models.BooleanField(default=False)),
                ('external_id', models.CharField(blank=True, max_length=100, null=True)),
                ('attachments', models.JSONField(blank=True, default=list)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='communications', to='users.applicantprofile')),
            ],
            options={
                'verbose_name': 'Communication Log',
                'verbose_name_plural': 'Communication Logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CommunicationType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('email', 'Email'), ('phone', 'Phone Call'), ('meeting', 'Meeting'), ('message', 'Platform Message'), ('interview', 'Interview'), ('note', 'Internal Note')], max_length=20, unique=True)),
                ('display_name', models.CharField(max_length=50)),
                ('icon', models.CharField(default='bi-chat', max_length=20)),
                ('color', models.CharField(default='#6c757d', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Communication Type',
                'verbose_name_plural': 'Communication Types',
            },
        ),
        migrations.CreateModel(
            name='FollowUpReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reminder_type', models.CharField(choices=[('email', 'Email Reminder'), ('platform', 'Platform Notification'), ('calendar', 'Calendar Event')], default='platform', max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('scheduled_for', models.DateTimeField()),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='follow_up_reminders', to='users.applicantprofile')),
                ('communication_log', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='follow_up_reminders', to='services.communicationlog')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('hiring_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='follow_up_reminders', to='users.hiringpartnerprofile')),
            ],
            options={
                'verbose_name': 'Follow-up Reminder',
                'verbose_name_plural': 'Follow-up Reminders',
                'ordering': ['scheduled_for'],
            },
        ),
        migrations.CreateModel(
            name='CommunicationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('template_type', models.CharField(choices=[('email', 'Email Template'), ('message', 'Message Template'), ('interview_invite', 'Interview Invitation'), ('offer_letter', 'Offer Letter'), ('rejection', 'Rejection Letter'), ('follow_up', 'Follow-up Message')], max_length=20)),
                ('subject_template', models.CharField(blank=True, max_length=200, null=True)),
                ('content_template', models.TextField()),
                ('is_global', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('available_variables', models.JSONField(default=list, help_text='Available template variables like {candidate_name}, {company_name}, etc.')),
                ('hiring_partner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='communication_templates', to='users.hiringpartnerprofile')),
            ],
            options={
                'verbose_name': 'Communication Template',
                'verbose_name_plural': 'Communication Templates',
                'ordering': ['template_type', 'name'],
            },
        ),
        migrations.AddField(
            model_name='communicationlog',
            name='communication_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='communications', to='services.communicationtype'),
        ),
        migrations.AddField(
            model_name='communicationlog',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='communicationlog',
            name='hiring_partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='communications_sent', to='users.hiringpartnerprofile'),
        ),
    ]
