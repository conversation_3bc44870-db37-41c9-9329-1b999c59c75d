# Generated by Django 4.2.7 on 2025-05-26 15:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_alter_applicantprofile_options_and_more'),
        ('services', '0008_communicationlog_communicationtype_followupreminder_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MatchingCriteria',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('default_skills_weight', models.IntegerField(default=40)),
                ('default_experience_weight', models.IntegerField(default=30)),
                ('default_location_weight', models.IntegerField(default=20)),
                ('default_language_weight', models.IntegerField(default=10)),
                ('strict_skill_matching', models.BooleanField(default=False)),
                ('allow_junior_candidates', models.BooleanField(default=True)),
                ('prioritize_local_candidates', models.BooleanField(default=False)),
                ('use_ai_scoring', models.BooleanField(default=False)),
                ('ai_model_preference', models.CharField(blank=True, choices=[('openai', 'OpenAI GPT'), ('claude', 'Anthropic Claude'), ('local', 'Local Model')], default='openai', max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('hiring_partner', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='matching_criteria', to='users.hiringpartnerprofile')),
            ],
        ),
        migrations.CreateModel(
            name='JobRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('required_skills', models.JSONField(default=list, help_text='List of required skills')),
                ('preferred_skills', models.JSONField(default=list, help_text='List of preferred skills')),
                ('min_experience_years', models.IntegerField(default=0)),
                ('max_experience_years', models.IntegerField(blank=True, null=True)),
                ('preferred_locations', models.JSONField(default=list, help_text='List of preferred locations')),
                ('remote_work_allowed', models.BooleanField(default=False)),
                ('required_languages', models.JSONField(default=list, help_text='List of required languages')),
                ('education_level', models.CharField(blank=True, max_length=50, null=True)),
                ('skills_weight', models.IntegerField(default=40, help_text='Weight for skills matching (0-100)')),
                ('experience_weight', models.IntegerField(default=30, help_text='Weight for experience matching (0-100)')),
                ('location_weight', models.IntegerField(default=20, help_text='Weight for location matching (0-100)')),
                ('language_weight', models.IntegerField(default=10, help_text='Weight for language matching (0-100)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('offer', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='requirements', to='services.offer')),
            ],
        ),
        migrations.CreateModel(
            name='CandidateScore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_score', models.FloatField(default=0.0)),
                ('skills_score', models.FloatField(default=0.0)),
                ('experience_score', models.FloatField(default=0.0)),
                ('location_score', models.FloatField(default=0.0)),
                ('language_score', models.FloatField(default=0.0)),
                ('matched_skills', models.JSONField(default=list)),
                ('missing_skills', models.JSONField(default=list)),
                ('skill_gaps', models.JSONField(default=list)),
                ('ai_score', models.FloatField(blank=True, null=True)),
                ('ai_reasoning', models.TextField(blank=True, null=True)),
                ('quality_rating', models.CharField(choices=[('excellent', 'Excellent Match'), ('good', 'Good Match'), ('fair', 'Fair Match'), ('poor', 'Poor Match')], default='fair', max_length=20)),
                ('calculated_at', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_scores', to='users.applicantprofile')),
                ('hiring_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='candidate_scores', to='users.hiringpartnerprofile')),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='candidate_scores', to='services.offer')),
            ],
            options={
                'ordering': ['-overall_score', '-calculated_at'],
                'unique_together': {('candidate', 'offer')},
            },
        ),
    ]
