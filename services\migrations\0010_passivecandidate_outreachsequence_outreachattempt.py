# Generated by Django 4.2.7 on 2025-05-26 16:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_alter_applicantprofile_options_and_more'),
        ('services', '0009_matchingcriteria_jobrequirement_candidatescore'),
    ]

    operations = [
        migrations.CreateModel(
            name='PassiveCandidate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=50)),
                ('last_name', models.Char<PERSON>ield(max_length=50)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('current_company', models.CharField(blank=True, max_length=100, null=True)),
                ('current_position', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('industry', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('location', models.Char<PERSON><PERSON>(blank=True, max_length=200, null=True)),
                ('experience_years', models.IntegerField(default=0)),
                ('skills', models.JSONField(default=list, help_text='List of candidate skills')),
                ('bio', models.TextField(blank=True, null=True)),
                ('linkedin_url', models.URLField(blank=True, null=True)),
                ('github_url', models.URLField(blank=True, null=True)),
                ('portfolio_url', models.URLField(blank=True, null=True)),
                ('discovery_source', models.CharField(choices=[('linkedin', 'LinkedIn'), ('github', 'GitHub'), ('referral', 'Employee Referral'), ('social_media', 'Social Media'), ('conference', 'Conference/Event'), ('website', 'Company Website'), ('other', 'Other')], default='other', max_length=20)),
                ('discovery_notes', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('discovered', 'Discovered'), ('contacted', 'Contacted'), ('engaged', 'Engaged'), ('qualified', 'Qualified'), ('converted', 'Converted to Active'), ('not_responsive', 'Not Responsive'), ('declined', 'Declined')], default='discovered', max_length=20)),
                ('interest_level', models.CharField(choices=[('unknown', 'Unknown'), ('not_interested', 'Not Interested'), ('slightly_interested', 'Slightly Interested'), ('interested', 'Interested'), ('very_interested', 'Very Interested')], default='unknown', max_length=20)),
                ('last_contact_date', models.DateTimeField(blank=True, null=True)),
                ('next_follow_up_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('converted_to_applicant', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='passive_candidate_source', to='users.applicantprofile')),
                ('discovered_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='discovered_candidates', to='users.hiringpartnerprofile')),
            ],
            options={
                'verbose_name': 'Passive Candidate',
                'verbose_name_plural': 'Passive Candidates',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='OutreachSequence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('sequence_type', models.CharField(choices=[('initial_contact', 'Initial Contact'), ('follow_up', 'Follow-up'), ('nurture', 'Nurture Campaign'), ('re_engagement', 'Re-engagement')], max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('delay_days', models.IntegerField(default=3, help_text='Days between sequence steps')),
                ('max_attempts', models.IntegerField(default=3, help_text='Maximum outreach attempts')),
                ('subject_template', models.CharField(max_length=200)),
                ('message_template', models.TextField()),
                ('target_roles', models.JSONField(default=list, help_text='Target job roles')),
                ('target_industries', models.JSONField(default=list, help_text='Target industries')),
                ('min_experience_years', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('hiring_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outreach_sequences', to='users.hiringpartnerprofile')),
            ],
            options={
                'verbose_name': 'Outreach Sequence',
                'verbose_name_plural': 'Outreach Sequences',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='OutreachAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attempt_type', models.CharField(choices=[('email', 'Email'), ('linkedin_message', 'LinkedIn Message'), ('phone_call', 'Phone Call'), ('text_message', 'Text Message')], default='email', max_length=20)),
                ('attempt_number', models.IntegerField(default=1)),
                ('subject', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('opened', 'Opened'), ('clicked', 'Clicked'), ('replied', 'Replied'), ('bounced', 'Bounced'), ('failed', 'Failed')], default='scheduled', max_length=20)),
                ('scheduled_for', models.DateTimeField()),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('opened_at', models.DateTimeField(blank=True, null=True)),
                ('clicked_at', models.DateTimeField(blank=True, null=True)),
                ('replied_at', models.DateTimeField(blank=True, null=True)),
                ('response_received', models.BooleanField(default=False)),
                ('response_content', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('hiring_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outreach_attempts', to='users.hiringpartnerprofile')),
                ('passive_candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outreach_attempts', to='services.passivecandidate')),
                ('sequence', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attempts', to='services.outreachsequence')),
            ],
            options={
                'verbose_name': 'Outreach Attempt',
                'verbose_name_plural': 'Outreach Attempts',
                'ordering': ['-created_at'],
                'unique_together': {('passive_candidate', 'sequence', 'attempt_number')},
            },
        ),
    ]
