"""
Passive candidate sourcing service for Smarch platform.
Provides tools for discovering, tracking, and engaging passive candidates.
"""

import re
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.core.mail import send_mail
from django.conf import settings

from .models import (
    PassiveCandidate, OutreachSequence, OutreachAttempt,
    HiringPartnerProfile, Offer
)


class PassiveCandidateSourcingService:
    """Service for passive candidate discovery and engagement."""
    
    def __init__(self, hiring_partner: HiringPartnerProfile):
        self.hiring_partner = hiring_partner
    
    def discover_candidate(self, candidate_data: Dict) -> PassiveCandidate:
        """
        Add a new passive candidate to the database.
        
        Args:
            candidate_data: Dictionary containing candidate information
            
        Returns:
            PassiveCandidate instance
        """
        # Check if candidate already exists
        existing = PassiveCandidate.objects.filter(
            email=candidate_data.get('email')
        ).first()
        
        if existing:
            # Update existing candidate
            for key, value in candidate_data.items():
                if hasattr(existing, key) and value:
                    setattr(existing, key, value)
            existing.save()
            return existing
        
        # Create new passive candidate
        candidate = PassiveCandidate.objects.create(
            first_name=candidate_data.get('first_name', ''),
            last_name=candidate_data.get('last_name', ''),
            email=candidate_data.get('email'),
            phone=candidate_data.get('phone', ''),
            current_company=candidate_data.get('current_company', ''),
            current_position=candidate_data.get('current_position', ''),
            industry=candidate_data.get('industry', ''),
            location=candidate_data.get('location', ''),
            experience_years=candidate_data.get('experience_years', 0),
            skills=candidate_data.get('skills', []),
            bio=candidate_data.get('bio', ''),
            linkedin_url=candidate_data.get('linkedin_url', ''),
            github_url=candidate_data.get('github_url', ''),
            portfolio_url=candidate_data.get('portfolio_url', ''),
            discovery_source=candidate_data.get('discovery_source', 'other'),
            discovered_by=self.hiring_partner,
            discovery_notes=candidate_data.get('discovery_notes', ''),
        )
        
        return candidate
    
    def bulk_import_candidates(self, candidates_list: List[Dict]) -> Dict:
        """
        Import multiple candidates at once.
        
        Args:
            candidates_list: List of candidate dictionaries
            
        Returns:
            Dictionary with import statistics
        """
        results = {
            'created': 0,
            'updated': 0,
            'errors': [],
            'total': len(candidates_list)
        }
        
        for candidate_data in candidates_list:
            try:
                # Validate required fields
                if not candidate_data.get('email'):
                    results['errors'].append(f"Missing email for candidate: {candidate_data}")
                    continue
                
                existing = PassiveCandidate.objects.filter(
                    email=candidate_data['email']
                ).first()
                
                if existing:
                    # Update existing
                    for key, value in candidate_data.items():
                        if hasattr(existing, key) and value:
                            setattr(existing, key, value)
                    existing.save()
                    results['updated'] += 1
                else:
                    # Create new
                    self.discover_candidate(candidate_data)
                    results['created'] += 1
                    
            except Exception as e:
                results['errors'].append(f"Error processing {candidate_data.get('email', 'unknown')}: {str(e)}")
        
        return results
    
    def find_candidates_for_offer(self, offer: Offer, limit: int = 20) -> List[PassiveCandidate]:
        """
        Find passive candidates that match a specific job offer.
        
        Args:
            offer: Job offer to match against
            limit: Maximum number of candidates to return
            
        Returns:
            List of matching passive candidates
        """
        # Extract keywords from job description
        keywords = self._extract_keywords_from_offer(offer)
        
        # Build query
        query = Q()
        
        # Search by skills
        for keyword in keywords:
            query |= Q(skills__icontains=keyword)
            query |= Q(bio__icontains=keyword)
            query |= Q(current_position__icontains=keyword)
        
        # Filter by status (exclude declined and not_responsive)
        query &= ~Q(status__in=['declined', 'not_responsive'])
        
        # Get candidates
        candidates = PassiveCandidate.objects.filter(query).distinct()
        
        # Score and sort candidates
        scored_candidates = []
        for candidate in candidates[:limit * 2]:  # Get more to filter later
            score = self._calculate_candidate_match_score(candidate, offer, keywords)
            if score > 30:  # Minimum threshold
                candidate.match_score = score
                scored_candidates.append(candidate)
        
        # Sort by score and return top matches
        scored_candidates.sort(key=lambda x: x.match_score, reverse=True)
        return scored_candidates[:limit]
    
    def _extract_keywords_from_offer(self, offer: Offer) -> List[str]:
        """Extract relevant keywords from job offer."""
        text = f"{offer.title} {offer.description}".lower()
        
        # Common tech keywords
        tech_keywords = [
            'python', 'java', 'javascript', 'react', 'angular', 'vue', 'node',
            'django', 'flask', 'spring', 'sql', 'mysql', 'postgresql', 'mongodb',
            'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'git',
            'machine learning', 'ai', 'data science', 'analytics', 'devops',
            'frontend', 'backend', 'fullstack', 'mobile', 'ios', 'android'
        ]
        
        found_keywords = []
        for keyword in tech_keywords:
            if keyword in text:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _calculate_candidate_match_score(self, candidate: PassiveCandidate, 
                                       offer: Offer, keywords: List[str]) -> int:
        """Calculate how well a candidate matches an offer."""
        score = 0
        
        # Skills matching (40 points)
        candidate_skills = [skill.lower() for skill in candidate.skills]
        matched_skills = sum(1 for keyword in keywords if keyword in candidate_skills)
        if keywords:
            score += (matched_skills / len(keywords)) * 40
        
        # Bio/description matching (30 points)
        bio_text = (candidate.bio or '').lower()
        bio_matches = sum(1 for keyword in keywords if keyword in bio_text)
        if keywords:
            score += (bio_matches / len(keywords)) * 30
        
        # Experience level (20 points)
        if candidate.experience_years >= 2:
            score += min(candidate.experience_years * 3, 20)
        
        # Interest level bonus (10 points)
        interest_bonus = candidate.get_interest_score() / 10
        score += interest_bonus
        
        return min(int(score), 100)
    
    def create_outreach_sequence(self, sequence_data: Dict) -> OutreachSequence:
        """
        Create a new outreach sequence.
        
        Args:
            sequence_data: Dictionary containing sequence configuration
            
        Returns:
            OutreachSequence instance
        """
        sequence = OutreachSequence.objects.create(
            name=sequence_data.get('name'),
            sequence_type=sequence_data.get('sequence_type', 'initial_contact'),
            hiring_partner=self.hiring_partner,
            is_active=sequence_data.get('is_active', True),
            delay_days=sequence_data.get('delay_days', 3),
            max_attempts=sequence_data.get('max_attempts', 3),
            subject_template=sequence_data.get('subject_template', ''),
            message_template=sequence_data.get('message_template', ''),
            target_roles=sequence_data.get('target_roles', []),
            target_industries=sequence_data.get('target_industries', []),
            min_experience_years=sequence_data.get('min_experience_years', 0),
        )
        
        return sequence
    
    def start_outreach_sequence(self, candidate: PassiveCandidate, 
                               sequence: OutreachSequence) -> OutreachAttempt:
        """
        Start an outreach sequence for a candidate.
        
        Args:
            candidate: Passive candidate to contact
            sequence: Outreach sequence to use
            
        Returns:
            First outreach attempt
        """
        # Check if sequence is already running for this candidate
        existing_attempt = OutreachAttempt.objects.filter(
            passive_candidate=candidate,
            sequence=sequence,
            status__in=['scheduled', 'sent']
        ).first()
        
        if existing_attempt:
            return existing_attempt
        
        # Create first outreach attempt
        subject = self._personalize_template(sequence.subject_template, candidate)
        message = self._personalize_template(sequence.message_template, candidate)
        
        attempt = OutreachAttempt.objects.create(
            passive_candidate=candidate,
            sequence=sequence,
            hiring_partner=self.hiring_partner,
            attempt_type='email',
            attempt_number=1,
            subject=subject,
            message=message,
            scheduled_for=timezone.now() + timedelta(minutes=5),  # Schedule soon
            status='scheduled'
        )
        
        return attempt
    
    def _personalize_template(self, template: str, candidate: PassiveCandidate) -> str:
        """Personalize message template with candidate data."""
        replacements = {
            '{first_name}': candidate.first_name,
            '{last_name}': candidate.last_name,
            '{full_name}': candidate.full_name,
            '{current_company}': candidate.current_company or 'your current company',
            '{current_position}': candidate.current_position or 'your current role',
            '{company_name}': self.hiring_partner.company_name,
            '{recruiter_name}': self.hiring_partner.user.get_full_name(),
        }
        
        personalized = template
        for placeholder, value in replacements.items():
            personalized = personalized.replace(placeholder, value)
        
        return personalized
    
    def send_scheduled_outreach(self) -> Dict:
        """
        Send all scheduled outreach attempts.
        
        Returns:
            Dictionary with sending statistics
        """
        # Get scheduled attempts
        scheduled_attempts = OutreachAttempt.objects.filter(
            hiring_partner=self.hiring_partner,
            status='scheduled',
            scheduled_for__lte=timezone.now()
        )
        
        results = {
            'sent': 0,
            'failed': 0,
            'errors': []
        }
        
        for attempt in scheduled_attempts:
            try:
                # Send email
                success = self._send_email_attempt(attempt)
                
                if success:
                    attempt.status = 'sent'
                    attempt.sent_at = timezone.now()
                    attempt.passive_candidate.status = 'contacted'
                    attempt.passive_candidate.last_contact_date = timezone.now()
                    attempt.passive_candidate.save()
                    results['sent'] += 1
                else:
                    attempt.status = 'failed'
                    results['failed'] += 1
                
                attempt.save()
                
            except Exception as e:
                results['errors'].append(f"Error sending to {attempt.passive_candidate.email}: {str(e)}")
                results['failed'] += 1
        
        return results
    
    def _send_email_attempt(self, attempt: OutreachAttempt) -> bool:
        """Send an individual email attempt."""
        try:
            send_mail(
                subject=attempt.subject,
                message=attempt.message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[attempt.passive_candidate.email],
                fail_silently=False,
            )
            return True
        except Exception:
            return False
    
    def get_sourcing_analytics(self) -> Dict:
        """Get analytics for passive candidate sourcing."""
        candidates = PassiveCandidate.objects.filter(discovered_by=self.hiring_partner)
        
        analytics = {
            'total_candidates': candidates.count(),
            'by_status': dict(candidates.values_list('status').annotate(count=Count('id'))),
            'by_source': dict(candidates.values_list('discovery_source').annotate(count=Count('id'))),
            'by_interest': dict(candidates.values_list('interest_level').annotate(count=Count('id'))),
            'avg_experience': candidates.aggregate(avg_exp=Avg('experience_years'))['avg_exp'] or 0,
            'recent_discoveries': candidates.filter(
                created_at__gte=timezone.now() - timedelta(days=30)
            ).count(),
            'conversion_rate': self._calculate_conversion_rate(candidates),
        }
        
        return analytics
    
    def _calculate_conversion_rate(self, candidates) -> float:
        """Calculate conversion rate from passive to active candidates."""
        total = candidates.count()
        converted = candidates.filter(converted_to_applicant__isnull=False).count()
        
        if total == 0:
            return 0.0
        
        return (converted / total) * 100
