"""
Stripe service module for handling all Stripe-related operations.
"""
import stripe
from django.conf import settings
from django.urls import reverse
from django.utils import timezone
from datetime import datetime, timedelta

# Initialize Stripe with the API key
stripe.api_key = settings.STRIPE_SECRET_KEY

# Define plan IDs and prices
PLAN_PRICES = {
    'basic': 9900,  # $99.00
    'standard': 19900,  # $199.00
    'premium': 29900,  # $299.00
    'enterprise': 49900  # $499.00
}

# Define plan features for reference
PLAN_FEATURES = {
    'basic': {
        'contact_requests': 10,
        'applicant_views': 100,
        'job_posts': 3,
    },
    'standard': {
        'contact_requests': 30,
        'applicant_views': 500,
        'job_posts': 15,
    },
    'premium': {
        'contact_requests': float('inf'),  # Unlimited
        'applicant_views': float('inf'),  # Unlimited
        'job_posts': 50,
    },
    'enterprise': {
        'contact_requests': float('inf'),  # Unlimited
        'applicant_views': float('inf'),  # Unlimited
        'job_posts': float('inf'),  # Unlimited
    }
}


class StripeService:
    """Service class for handling Stripe operations."""

    @staticmethod
    def create_customer(user, token=None):
        """
        Create a Stripe customer for the given user.
        
        Args:
            user: The user to create a customer for
            token: The Stripe token for the payment method
            
        Returns:
            The created customer object
        """
        try:
            customer = stripe.Customer.create(
                email=user.email,
                name=f"{user.first_name} {user.last_name}",
                description=f"Customer for {user.email}",
                source=token  # Attach the payment method if provided
            )
            return customer
        except stripe.error.StripeError as e:
            # Log the error and re-raise
            print(f"Error creating Stripe customer: {str(e)}")
            raise

    @staticmethod
    def create_subscription(customer_id, plan, payment_method_id=None):
        """
        Create a subscription for the given customer.
        
        Args:
            customer_id: The Stripe customer ID
            plan: The subscription plan ('basic', 'standard', 'premium', 'enterprise')
            payment_method_id: The Stripe payment method ID
            
        Returns:
            The created subscription object
        """
        try:
            # If a payment method is provided, attach it to the customer
            if payment_method_id:
                payment_method = stripe.PaymentMethod.attach(
                    payment_method_id,
                    customer=customer_id
                )
                
                # Set as the default payment method
                stripe.Customer.modify(
                    customer_id,
                    invoice_settings={
                        'default_payment_method': payment_method_id
                    }
                )

            # Create the subscription
            subscription = stripe.Subscription.create(
                customer=customer_id,
                items=[
                    {
                        'price_data': {
                            'currency': 'usd',
                            'product_data': {
                                'name': f'{plan.title()} Plan',
                                'description': f'Smarch {plan.title()} Subscription Plan',
                            },
                            'unit_amount': PLAN_PRICES.get(plan.lower(), PLAN_PRICES['basic']),
                            'recurring': {
                                'interval': 'month',
                            }
                        },
                    },
                ],
                payment_behavior='default_incomplete',
                expand=['latest_invoice.payment_intent'],
            )
            return subscription
        except stripe.error.StripeError as e:
            # Log the error and re-raise
            print(f"Error creating Stripe subscription: {str(e)}")
            raise

    @staticmethod
    def cancel_subscription(subscription_id):
        """
        Cancel a subscription.
        
        Args:
            subscription_id: The Stripe subscription ID
            
        Returns:
            The cancelled subscription object
        """
        try:
            subscription = stripe.Subscription.modify(
                subscription_id,
                cancel_at_period_end=True
            )
            return subscription
        except stripe.error.StripeError as e:
            # Log the error and re-raise
            print(f"Error cancelling Stripe subscription: {str(e)}")
            raise

    @staticmethod
    def update_subscription(subscription_id, new_plan):
        """
        Update a subscription to a new plan.
        
        Args:
            subscription_id: The Stripe subscription ID
            new_plan: The new subscription plan
            
        Returns:
            The updated subscription object
        """
        try:
            # Get the subscription to find the item ID
            subscription = stripe.Subscription.retrieve(subscription_id)
            
            # Update the subscription item with the new price
            updated_subscription = stripe.Subscription.modify(
                subscription_id,
                items=[{
                    'id': subscription['items']['data'][0].id,
                    'price_data': {
                        'currency': 'usd',
                        'product_data': {
                            'name': f'{new_plan.title()} Plan',
                            'description': f'Smarch {new_plan.title()} Subscription Plan',
                        },
                        'unit_amount': PLAN_PRICES.get(new_plan.lower(), PLAN_PRICES['basic']),
                        'recurring': {
                            'interval': 'month',
                        }
                    },
                }],
                proration_behavior='create_prorations',
            )
            return updated_subscription
        except stripe.error.StripeError as e:
            # Log the error and re-raise
            print(f"Error updating Stripe subscription: {str(e)}")
            raise

    @staticmethod
    def create_checkout_session(user, plan, success_url, cancel_url, customer_id=None):
        """
        Create a Stripe Checkout Session for subscription payment.
        
        Args:
            user: The user making the payment
            plan: The subscription plan
            success_url: URL to redirect to on successful payment
            cancel_url: URL to redirect to on cancelled payment
            customer_id: Existing Stripe customer ID (if any)
            
        Returns:
            The created checkout session
        """
        try:
            # Set up the checkout session
            checkout_session = stripe.checkout.Session.create(
                customer=customer_id,
                payment_method_types=['card'],
                line_items=[
                    {
                        'price_data': {
                            'currency': 'usd',
                            'product_data': {
                                'name': f'{plan.title()} Plan',
                                'description': f'Smarch {plan.title()} Subscription Plan',
                            },
                            'unit_amount': PLAN_PRICES.get(plan.lower(), PLAN_PRICES['basic']),
                            'recurring': {
                                'interval': 'month',
                            }
                        },
                        'quantity': 1,
                    },
                ],
                mode='subscription',
                success_url=success_url,
                cancel_url=cancel_url,
                client_reference_id=str(user.id),
                customer_email=None if customer_id else user.email,
                metadata={
                    'plan': plan,
                    'user_id': str(user.id),
                }
            )
            return checkout_session
        except stripe.error.StripeError as e:
            # Log the error and re-raise
            print(f"Error creating Stripe checkout session: {str(e)}")
            raise

    @staticmethod
    def handle_webhook_event(payload, sig_header):
        """
        Handle a webhook event from Stripe.
        
        Args:
            payload: The webhook payload
            sig_header: The Stripe signature header
            
        Returns:
            The parsed event
        """
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
            )
            return event
        except ValueError as e:
            # Invalid payload
            print(f"Invalid payload: {str(e)}")
            raise
        except stripe.error.SignatureVerificationError as e:
            # Invalid signature
            print(f"Invalid signature: {str(e)}")
            raise
