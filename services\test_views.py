from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.urls import reverse


class PaymentViewsTestCase(TestCase):
    """Test cases for payment-related views."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()

        # Mock user
        self.user = MagicMock()
        self.user.id = 1
        self.user.email = '<EMAIL>'

        # Mock hiring partner
        self.hiring_partner = MagicMock()
        self.hiring_partner.user = self.user
        self.hiring_partner.company_name = 'Test Company'
        self.hiring_partner.stripe_customer_id = 'cus_test123'

        # Mock subscription
        self.subscription = MagicMock()
        self.subscription.id = 1
        self.subscription.hiring_partner = self.hiring_partner
        self.subscription.plan = 'basic'
        self.subscription.status = 'active'
        self.subscription.stripe_subscription_id = 'sub_test123'

    @patch('stripe.Customer.retrieve')
    @patch('services.stripe_service.StripeService.create_customer')
    @patch('services.stripe_service.StripeService.create_subscription')
    def test_payment_process_view(
        self, mock_create_sub, mock_create_cust, mock_retrieve
    ):
        """Test the payment process view."""
        # Set up the mocks
        mock_customer = MagicMock()
        mock_customer.id = 'cus_test123'
        mock_retrieve.return_value = mock_customer
        mock_create_cust.return_value = mock_customer

        mock_subscription = MagicMock()
        mock_subscription.id = 'sub_test123'
        mock_subscription.latest_invoice.payment_intent.id = 'pi_test123'
        mock_create_sub.return_value = mock_subscription

        # Log in the user
        self.client.login(username='testuser', password='testpassword')

        # Make the request
        url = reverse('services:payment')
        data = {
            'plan': 'basic',
            'stripeToken': 'tok_test123',
            'auto_renew': 'on'
        }
        response = self.client.post(url, data)

        # Assertions
        self.assertEqual(response.status_code, 302)  # Redirect
        self.assertRedirects(response, reverse('services:subscription'))

    @patch('services.stripe_service.StripeService.handle_webhook_event')
    def test_stripe_webhook_view(self, mock_handle_event):
        """Test the Stripe webhook view."""
        # Set up the mock
        mock_event = {
            'type': 'checkout.session.completed',
            'data': {
                'object': {
                    'client_reference_id': str(self.user.id),
                    'metadata': {
                        'plan': 'basic'
                    }
                }
            }
        }
        mock_handle_event.return_value = mock_event

        # Make the request
        url = reverse('services:webhook')
        headers = {'HTTP_STRIPE_SIGNATURE': 'test_signature'}
        response = self.client.post(
            url,
            data='{}',
            content_type='application/json',
            **headers
        )

        # Assertions
        self.assertEqual(response.status_code, 200)
        mock_handle_event.assert_called_once()

    @patch('services.stripe_service.StripeService.cancel_subscription')
    def test_cancel_subscription_view(self, mock_cancel):
        """Test the cancel subscription view."""
        # Set up the mock
        mock_subscription = MagicMock()
        mock_subscription.id = 'sub_test123'
        mock_cancel.return_value = mock_subscription

        # Log in the user
        self.client.login(username='testuser', password='testpassword')

        # Make the request
        url = reverse(
            'services:cancel_subscription',
            args=[self.subscription.id]
        )
        response = self.client.get(url)

        # Assertions
        self.assertEqual(response.status_code, 302)  # Redirect
        self.assertRedirects(response, reverse('services:subscription'))
