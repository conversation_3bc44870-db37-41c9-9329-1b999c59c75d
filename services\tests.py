import unittest
from unittest.mock import patch, MagicMock

from .stripe_service import StripeService


class StripeServiceTestCase(unittest.TestCase):
    """Test cases for the StripeService class."""

    def setUp(self):
        """Set up test data."""
        self.user = MagicMock()
        self.user.email = '<EMAIL>'
        self.user.first_name = 'Test'
        self.user.last_name = 'User'

    @patch('stripe.Customer.create')
    def test_create_customer(self, mock_create):
        """Test creating a Stripe customer."""
        # Set up the mock
        mock_customer = MagicMock()
        mock_customer.id = 'cus_test123'
        mock_create.return_value = mock_customer

        # Call the method
        customer = StripeService.create_customer(self.user, 'tok_test123')

        # Assertions
        mock_create.assert_called_once_with(
            email=self.user.email,
            name=f"{self.user.first_name} {self.user.last_name}",
            description=f"Customer for {self.user.email}",
            source='tok_test123'
        )
        self.assertEqual(customer.id, 'cus_test123')

    @patch('stripe.PaymentMethod.attach')
    @patch('stripe.Customer.modify')
    @patch('stripe.Subscription.create')
    def test_create_subscription(self, mock_sub_create, mock_customer_modify, mock_pm_attach):
        """Test creating a Stripe subscription."""
        # Set up the mocks
        mock_subscription = MagicMock()
        mock_subscription.id = 'sub_test123'
        mock_sub_create.return_value = mock_subscription

        # Call the method
        subscription = StripeService.create_subscription(
            customer_id='cus_test123',
            plan='basic',
            payment_method_id='pm_test123'
        )

        # Assertions
        mock_pm_attach.assert_called_once_with(
            'pm_test123',
            customer='cus_test123'
        )

        mock_customer_modify.assert_called_once_with(
            'cus_test123',
            invoice_settings={
                'default_payment_method': 'pm_test123'
            }
        )

        mock_sub_create.assert_called_once()
        self.assertEqual(subscription.id, 'sub_test123')

    @patch('stripe.Subscription.modify')
    def test_cancel_subscription(self, mock_modify):
        """Test cancelling a Stripe subscription."""
        # Set up the mock
        mock_subscription = MagicMock()
        mock_subscription.id = 'sub_test123'
        mock_modify.return_value = mock_subscription

        # Call the method
        subscription = StripeService.cancel_subscription('sub_test123')

        # Assertions
        mock_modify.assert_called_once_with(
            'sub_test123',
            cancel_at_period_end=True
        )
        self.assertEqual(subscription.id, 'sub_test123')

    @patch('stripe.Subscription.retrieve')
    @patch('stripe.Subscription.modify')
    def test_update_subscription(self, mock_modify, mock_retrieve):
        """Test updating a Stripe subscription to a new plan."""
        # Set up the mocks
        mock_subscription = MagicMock()
        mock_subscription.id = 'sub_test123'
        mock_subscription['items'] = {'data': [MagicMock(id='si_test123')]}
        mock_retrieve.return_value = mock_subscription

        mock_updated_subscription = MagicMock()
        mock_updated_subscription.id = 'sub_test123'
        mock_modify.return_value = mock_updated_subscription

        # Call the method
        subscription = StripeService.update_subscription('sub_test123', 'premium')

        # Assertions
        mock_retrieve.assert_called_once_with('sub_test123')
        mock_modify.assert_called_once()
        self.assertEqual(subscription.id, 'sub_test123')

    @patch('stripe.checkout.Session.create')
    def test_create_checkout_session(self, mock_create):
        """Test creating a Stripe checkout session."""
        # Set up the mock
        mock_session = MagicMock()
        mock_session.id = 'cs_test123'
        mock_create.return_value = mock_session

        # Call the method
        session = StripeService.create_checkout_session(
            user=self.user,
            plan='basic',
            success_url='https://example.com/success',
            cancel_url='https://example.com/cancel',
            customer_id='cus_test123'
        )

        # Assertions
        mock_create.assert_called_once()
        self.assertEqual(session.id, 'cs_test123')

    @patch('stripe.Webhook.construct_event')
    def test_handle_webhook_event(self, mock_construct):
        """Test handling a Stripe webhook event."""
        # Set up the mock
        mock_event = {'type': 'checkout.session.completed'}
        mock_construct.return_value = mock_event

        # Call the method
        event = StripeService.handle_webhook_event(
            payload='{}',
            sig_header='test_signature'
        )

        # Assertions
        mock_construct.assert_called_once()
        self.assertEqual(event, mock_event)
