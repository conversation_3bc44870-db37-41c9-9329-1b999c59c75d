"""
URL configuration for smarch project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from users.views import SignupRedirectView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('core.urls')),

    # Override the allauth signup URL with our custom view
    path('accounts/signup/', SignupRedirectView.as_view(), name='account_signup'),
    path('accounts/', include('allauth.urls')),

    path('application/', include('application.urls')),
    path('services/', include('services.urls')),
    path('blog/', include('blog.urls')),
    path('events/', include('event_management.urls')),
    path('contact/', include('contact_management.urls')),
    path('insights/', include('insights_management.urls')),
    path('staff/', include('staff_management.urls')),
    path('users/', include('users.urls')),
    path('subscribe/', include('subscription.urls')),
    path('messages/', include('messaging.urls')),
    path('notifications/', include('notifications.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
