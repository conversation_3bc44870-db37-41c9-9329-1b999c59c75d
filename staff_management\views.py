from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.utils import timezone
from django.db import models
from application.models import ApplicationFormResponse


def is_staff_member(user):
    """Check if the user is a staff member."""
    return user.is_staff


@login_required
@user_passes_test(is_staff_member)
def staff_dashboard(request):
    """
    View for displaying the staff dashboard.
    """
    return render(request, 'staff_management/dashboard.html')


@login_required
@user_passes_test(is_staff_member)
def application_list(request):
    """
    View for displaying a list of all applications with filtering options.
    """
    # Get filter parameters
    status_filter = request.GET.getlist('status', ['draft', 'submitted', 'in_review', 'approved'])
    search_query = request.GET.get('search', '')

    # Base queryset
    applications = ApplicationFormResponse.objects.all()

    # Apply filters
    if status_filter:
        applications = applications.filter(status__in=status_filter)

    # Apply search if provided
    if search_query:
        applications = applications.filter(
            models.Q(applicant__user__username__icontains=search_query) |
            models.Q(applicant__user__email__icontains=search_query) |
            models.Q(applicant__user__first_name__icontains=search_query) |
            models.Q(applicant__user__last_name__icontains=search_query) |
            models.Q(applicant__case_number__icontains=search_query)
        )

    # Order by submission date
    applications = applications.order_by('-submitted_at')

    context = {
        'applications': applications,
        'status_filter': status_filter,
        'search_query': search_query
    }
    return render(request, 'staff_management/application_list.html', context)


@login_required
@user_passes_test(is_staff_member)
def application_detail(request, application_id):
    """
    View for displaying details of a specific application.
    Also handles status updates and note additions.
    """
    application = get_object_or_404(ApplicationFormResponse, id=application_id)

    if request.method == 'POST':
        # Handle status update
        if 'update_status' in request.POST:
            new_status = request.POST.get('status')
            if new_status in ['in_review', 'approved', 'rejected']:
                application.status = new_status
                application.review_date = timezone.now()
                application.reviewed_by = request.user
                application.save()

                # Process the application if it hasn't been processed yet
                if new_status in ['approved', 'rejected'] and not application.has_refined_info():
                    application.process_application()

                messages.success(request, f"Application status updated to {new_status.replace('_', ' ').title()}")

        # Handle note addition
        elif 'add_note' in request.POST:
            note = request.POST.get('note')
            if note:
                # If there are existing notes, append the new note
                if application.notes:
                    application.notes += f"\n\n[{timezone.now().strftime('%Y-%m-%d %H:%M')} - {request.user.get_full_name()}]\n{note}"
                else:
                    application.notes = f"[{timezone.now().strftime('%Y-%m-%d %H:%M')} - {request.user.get_full_name()}]\n{note}"
                application.save()
                messages.success(request, "Note added successfully")

    context = {
        'application': application
    }
    return render(request, 'staff_management/application_detail.html', context)


@login_required
@user_passes_test(is_staff_member)
def applicant_list(request):
    """
    View for displaying a list of all applicants.
    """
    return render(request, 'staff_management/applicant_list.html')


@login_required
@user_passes_test(is_staff_member)
def service_user_list(request):
    """
    View for displaying a list of all hiring partners.
    """
    return render(request, 'staff_management/service_user_list.html')


@login_required
@user_passes_test(is_staff_member)
def smarch_panel(request):
    """
    View for displaying the Smarch admin panel.
    """
    return render(request, 'staff_management/smarch_panel.html')
