:root {
    /* Primary Colors */
    --primary: #0d6efd;
    --primary-dark: #0b5ed7;
    --primary-light: #6ea8fe;
    --primary-very-light: #cfe2ff;

    /* Secondary Colors */
    --secondary: #6c757d;
    --secondary-dark: #5c636a;
    --secondary-light: #a7acb1;
    --secondary-very-light: #e9ecef;

    /* Success Colors */
    --success: #198754;
    --success-dark: #157347;
    --success-light: #75b798;
    --success-very-light: #d1e7dd;

    /* Danger Colors */
    --danger: #dc3545;
    --danger-dark: #bb2d3b;
    --danger-light: #ea868f;
    --danger-very-light: #f8d7da;

    /* Warning Colors */
    --warning: #ffc107;
    --warning-dark: #ffca2c;
    --warning-light: #ffda6a;
    --warning-very-light: #fff3cd;

    /* Info Colors */
    --info: #0dcaf0;
    --info-dark: #31d2f2;
    --info-light: #6edff6;
    --info-very-light: #cff4fc;

    /* Neutral Colors */
    --white: #ffffff;
    --light: #f8f9fa;
    --dark: #212529;
    --black: #000000;

    /* Typography */
    --font-family-sans-serif: 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

    /* Spacing */
    --spacer: 1rem;

    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 2rem;
    --border-radius-pill: 50rem;
    --border-radius-circle: 50%;

    /* Box Shadow */
    --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

    /* Transitions */
    --transition-base: all 0.2s ease-in-out;
    --transition-fade: opacity 0.15s linear;
    --transition-collapse: height 0.35s ease;
}

/* Dark Mode Variables */
[data-bs-theme="dark"] {
    --primary: #6ea8fe;
    --primary-dark: #0d6efd;
    --primary-light: #9ec5fe;
    --primary-very-light: #031633;

    --secondary: #a7acb1;
    --secondary-dark: #6c757d;
    --secondary-light: #c4c8cc;
    --secondary-very-light: #1a1d20;

    --success: #75b798;
    --success-dark: #198754;
    --success-light: #a3cfbb;
    --success-very-light: #051b11;

    --danger: #ea868f;
    --danger-dark: #dc3545;
    --danger-light: #f1aeb5;
    --danger-very-light: #2c0b0e;

    --warning: #ffda6a;
    --warning-dark: #ffc107;
    --warning-light: #ffe69c;
    --warning-very-light: #332701;

    --info: #6edff6;
    --info-dark: #0dcaf0;
    --info-light: #a7e9f4;
    --info-very-light: #032830;

    --white: #212529;
    --light: #343a40;
    --dark: #f8f9fa;
    --black: #ffffff;

    color-scheme: dark;
}

/* Global Styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: var(--font-family-sans-serif);
    transition: var(--transition-base);
}

main {
    flex: 1;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
}

a {
    transition: var(--transition-base);
}

.btn {
    transition: var(--transition-base);
}

.card {
    transition: var(--transition-base);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* Navigation */
.navbar {
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition-base);
}

.navbar-brand {
    font-weight: 700;
}

.nav-link {
    position: relative;
    transition: var(--transition-base);
}

.nav-link:hover {
    transform: translateY(-2px);
}

.dropdown-menu {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 1rem;
    transition: var(--transition-base);
}

.dropdown-item:hover {
    background-color: var(--primary-very-light);
}

/* Home Page */
.carousel-item {
    height: 500px;
    position: relative;
}

.carousel-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.6));
}

.carousel-item img {
    object-fit: cover;
    height: 100%;
    width: 100%;
}

.carousel-caption {
    z-index: 1;
    bottom: 20%;
}

.card-img-top {
    height: 200px;
    object-fit: cover;
}

/* Profile Pages */
.profile-image {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid var(--white);
    box-shadow: var(--box-shadow);
}

/* Form Styles */
.form-container {
    max-width: 800px;
    margin: 0 auto;
}

.form-control, .form-select {
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    border: 1px solid var(--secondary-light);
    transition: var(--transition-base);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Blog Page */
.blog-post-img {
    height: 300px;
    object-fit: cover;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Services Browser */
.applicant-card {
    border: 1px solid var(--secondary-very-light);
    transition: var(--transition-base);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.applicant-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

/* Services Dashboard */
.insights-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition-base);
}

.insights-card:hover {
    box-shadow: var(--box-shadow);
}

/* Staff Panel */
.staff-dashboard-card {
    border-left: 4px solid var(--primary);
    transition: var(--transition-base);
    border-radius: var(--border-radius);
}

.staff-dashboard-card:hover {
    background-color: var(--primary-very-light);
    transform: translateX(5px);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-in-up {
    animation: slideInUp 0.5s ease-in-out;
}

/* Dark Mode Toggle */
.dark-mode-toggle {
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius-circle);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-base);
}

.dark-mode-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

[data-bs-theme="dark"] .dark-mode-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Card Hover Effects */
.hover-lift {
    transition: var(--transition-base);
}

.hover-lift:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow);
}

.hover-shadow {
    transition: var(--transition-base);
}

.hover-shadow:hover {
    box-shadow: var(--box-shadow);
}

.hover-border-primary {
    transition: var(--transition-base);
    border-left: 4px solid transparent;
}

.hover-border-primary:hover {
    border-left: 4px solid var(--primary);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .carousel-item {
        height: 300px;
    }

    .profile-image {
        width: 100px;
        height: 100px;
    }

    .navbar-nav .dropdown-menu {
        border: none;
        box-shadow: none;
        background-color: transparent;
    }

    .dropdown-item {
        color: rgba(255, 255, 255, 0.55);
    }

    .dropdown-item:hover {
        color: rgba(255, 255, 255, 0.75);
        background-color: transparent;
    }
}