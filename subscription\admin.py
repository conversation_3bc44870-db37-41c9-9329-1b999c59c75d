from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Subscriber


@admin.register(Subscriber)
class SubscriberAdmin(admin.ModelAdmin):
    list_display = ('email', 'name', 'subscribed_at', 'is_active', 'ip_address')
    list_filter = ('is_active', 'subscribed_at')
    search_fields = ('email', 'name')
    readonly_fields = ('subscribed_at', 'ip_address', 'user_agent', 'referrer')
    fieldsets = (
        (None, {
            'fields': ('email', 'name', 'is_active')
        }),
        (_('Subscription Info'), {
            'fields': ('subscribed_at', 'ip_address', 'user_agent', 'referrer')
        }),
    )
    actions = ['mark_active', 'mark_inactive']

    def mark_active(self, request, queryset):
        queryset.update(is_active=True)
        self.message_user(
            request,
            _(f'{queryset.count()} subscribers marked as active.')
        )
    mark_active.short_description = _('Mark selected subscribers as active')

    def mark_inactive(self, request, queryset):
        queryset.update(is_active=False)
        self.message_user(
            request,
            _(f'{queryset.count()} subscribers marked as inactive.')
        )
    mark_inactive.short_description = _('Mark selected subscribers as inactive')
