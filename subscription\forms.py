from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Subscriber


class SubscriberForm(forms.ModelForm):
    """Form for collecting email subscriptions."""
    email = forms.EmailField(
        label=_('Email address'),
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your email address'),
            'required': True,
        })
    )
    
    name = forms.CharField(
        label=_('Name'),
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your name (optional)'),
        })
    )
    
    class Meta:
        model = Subscriber
        fields = ['email', 'name']
