# Generated by Django 4.2.7 on 2025-04-23 12:12

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Subscriber',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email address')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='Name')),
                ('subscribed_at', models.DateTimeField(auto_now_add=True, verbose_name='Subscribed at')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('referrer', models.URLField(blank=True, verbose_name='Referrer')),
            ],
            options={
                'verbose_name': 'Subscriber',
                'verbose_name_plural': 'Subscribers',
                'ordering': ['-subscribed_at'],
            },
        ),
    ]
