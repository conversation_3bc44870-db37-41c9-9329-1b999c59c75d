from django.db import models
from django.utils.translation import gettext_lazy as _


class Subscriber(models.Model):
    """Model for storing email subscribers for launch notifications."""
    email = models.EmailField(_('Email address'), unique=True)
    name = models.CharField(_('Name'), max_length=100, blank=True)
    subscribed_at = models.DateTimeField(_('Subscribed at'), auto_now_add=True)
    is_active = models.BooleanField(_('Active'), default=True)
    ip_address = models.GenericIPAddressField(_('IP Address'), blank=True, null=True)
    user_agent = models.TextField(_('User Agent'), blank=True)
    referrer = models.URLField(_('Referrer'), blank=True)

    class Meta:
        verbose_name = _('Subscriber')
        verbose_name_plural = _('Subscribers')
        ordering = ['-subscribed_at']

    def __str__(self):
        return self.email
