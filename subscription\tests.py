from django.test import TestCase
from django.contrib.auth import get_user_model
from datetime import date, timedelta

from users.models import HiringPartnerProfile
from services.models import Subscription, Payment

User = get_user_model()


class SubscriptionModelTestCase(TestCase):
    """Test cases for the Subscription model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.hiring_partner = HiringPartnerProfile.objects.create(
            user=self.user,
            company_name='Test Company',
            industry='Technology',
            company_size='10-50',
            primary_location='New York',
            company_website='https://example.com',
            services_offered='Software Development'
        )

    def test_subscription_creation(self):
        """Test creating a subscription."""
        subscription = Subscription.objects.create(
            hiring_partner=self.hiring_partner,
            plan='basic',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=30),
            status='active',
            is_auto_renew=True,
            payment_method='Credit Card',
            stripe_customer_id='cus_test123',
            stripe_subscription_id='sub_test123'
        )

        self.assertEqual(subscription.plan, 'basic')
        self.assertEqual(subscription.status, 'active')
        self.assertEqual(subscription.stripe_customer_id, 'cus_test123')
        self.assertEqual(subscription.stripe_subscription_id, 'sub_test123')
        self.assertTrue(subscription.is_auto_renew)

    def test_subscription_str_method(self):
        """Test the string representation of a subscription."""
        subscription = Subscription.objects.create(
            hiring_partner=self.hiring_partner,
            plan='basic',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=30),
            status='active'
        )

        expected_str = f"{self.hiring_partner.company_name} - basic Plan"
        self.assertEqual(str(subscription), expected_str)


class PaymentModelTestCase(TestCase):
    """Test cases for the Payment model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.hiring_partner = HiringPartnerProfile.objects.create(
            user=self.user,
            company_name='Test Company',
            industry='Technology',
            company_size='10-50',
            primary_location='New York',
            company_website='https://example.com',
            services_offered='Software Development'
        )
        self.subscription = Subscription.objects.create(
            hiring_partner=self.hiring_partner,
            plan='basic',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=30),
            status='active',
            stripe_subscription_id='sub_test123'
        )

    def test_payment_creation(self):
        """Test creating a payment."""
        payment = Payment.objects.create(
            subscription=self.subscription,
            amount=99.00,
            status='succeeded',
            transaction_id='txn_test123',
            payment_method='Credit Card',
            stripe_payment_intent_id='pi_test123',
            stripe_charge_id='ch_test123'
        )

        self.assertEqual(payment.amount, 99.00)
        self.assertEqual(payment.status, 'succeeded')
        self.assertEqual(payment.transaction_id, 'txn_test123')
        self.assertEqual(payment.stripe_payment_intent_id, 'pi_test123')
        self.assertEqual(payment.stripe_charge_id, 'ch_test123')

    def test_payment_str_method(self):
        """Test the string representation of a payment."""
        payment = Payment.objects.create(
            subscription=self.subscription,
            amount=99.00,
            status='succeeded',
            transaction_id='txn_test123'
        )

        expected_str = f"Payment {payment.transaction_id} - {payment.amount}"
        self.assertEqual(str(payment), expected_str)
