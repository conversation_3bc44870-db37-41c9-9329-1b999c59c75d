from django.shortcuts import render, redirect
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from .forms import SubscriberForm
from .models import Subscriber


def get_client_ip(request):
    """Get the client IP address from the request."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def landing_page(request):
    """View for the landing page with subscription form."""
    form = SubscriberForm()
    return render(request, 'subscription/landing.html', {'form': form})


@require_POST
def subscribe(request):
    """Handle subscription form submission."""
    form = SubscriberForm(request.POST)

    # Check if it's an AJAX request
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    if form.is_valid():
        # Check if email already exists
        email = form.cleaned_data['email']
        if Subscriber.objects.filter(email=email).exists():
            if is_ajax:
                return JsonResponse({
                    'success': False,
                    'message': _('This email is already subscribed!')
                })
            messages.info(request, _('This email is already subscribed!'))
            return redirect('subscription:landing_page')

        # Create new subscriber
        subscriber = form.save(commit=False)
        subscriber.ip_address = get_client_ip(request)
        subscriber.user_agent = request.META.get('HTTP_USER_AGENT', '')
        subscriber.referrer = request.META.get('HTTP_REFERER', '')
        subscriber.save()

        if is_ajax:
            return JsonResponse({
                'success': True,
                'message': _('Thank you for subscribing! We\'ll notify you when we launch.')
            })

        messages.success(
            request,
            _('Thank you for subscribing! We\'ll notify you when we launch.')
        )
        return redirect('subscription:landing_page')

    if is_ajax:
        return JsonResponse({
            'success': False,
            'message': _('Please enter a valid email address.')
        })

    messages.error(request, _('Please enter a valid email address.'))
    return render(request, 'subscription/landing.html', {'form': form})


def unsubscribe(request, email):
    """Handle unsubscribe requests."""
    # This is a simple implementation. In a production environment,
    # you would want to use a token-based system for security.
    try:
        subscriber = Subscriber.objects.get(email=email)
        subscriber.is_active = False
        subscriber.save()
        messages.success(
            request,
            _('You have been successfully unsubscribed.')
        )
    except Subscriber.DoesNotExist:
        messages.error(
            request,
            _('This email is not in our subscription list.')
        )

    return redirect('subscription:landing_page')
