{% extends "common/base.html" %}
{% load i18n %}
{% load account socialaccount %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Sign In" %} | Smarch{% endblock %}

{% block content %}
<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h1 class="h3 mb-0">{% trans "Sign In" %}</h1>
        </div>
        <div class="card-body">
          {% get_providers as socialaccount_providers %}

          {% if socialaccount_providers %}
          <div class="mb-4">
            <p class="text-center">{% blocktrans with site.name as site_name %}Sign in with one of your existing accounts:{% endblocktrans %}</p>
            <div class="socialaccount_ballot d-flex justify-content-center gap-3 mb-3">
              <div class="socialaccount_providers">
                {% include "socialaccount/snippets/provider_list.html" with process="login" %}
              </div>
            </div>
            <div class="login-or border-top border-bottom py-2 mb-3 text-center text-muted">
              {% trans "or" %}
            </div>
          </div>
          {% endif %}

          <form class="login" method="POST" action="{% url 'account_login' %}">
            {% csrf_token %}
            <div class="alert alert-info mb-3">
              <small><i class="bi bi-info-circle-fill me-2"></i> You can log in with either your username or email address.</small>
            </div>
            {{ form|crispy }}
            {% if redirect_field_value %}
            <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
            {% endif %}

            <div class="d-flex justify-content-between align-items-center mt-4">
              <a class="button secondaryAction" href="{% url 'account_reset_password' %}">
                {% trans "Forgot Password?" %}
              </a>
              <button class="btn btn-primary" type="submit">{% trans "Sign In" %}</button>
            </div>
          </form>

          <div class="mt-4 text-center">
            <p>{% blocktrans %}Don't have an account yet? <a href="{{ signup_url }}">Sign up</a>{% endblocktrans %}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}