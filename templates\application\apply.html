{% extends "common/base.html" %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "SMARCH Dream Job & Career Aspirations Survey" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <!-- Sidebar - Application Steps -->
    <div class="col-lg-3 mb-4">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h5 class="card-title mb-0">Application Process</h5>
        </div>
        <div class="card-body">
          <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex align-items-center">
              <span class="badge bg-primary rounded-circle me-2">1</span>
              Create Profile
              <i class="bi bi-check-circle-fill text-success ms-auto"></i>
            </li>
            <li class="list-group-item d-flex align-items-center active">
              <span class="badge bg-primary rounded-circle me-2">2</span>
              Complete Survey
              <i class="bi bi-arrow-right ms-auto"></i>
            </li>
            <li class="list-group-item d-flex align-items-center text-muted">
              <span class="badge bg-secondary rounded-circle me-2">3</span>
              Application Review
            </li>
            <li class="list-group-item d-flex align-items-center text-muted">
              <span class="badge bg-secondary rounded-circle me-2">4</span>
              Matching Process
            </li>
          </ul>

          <div class="mt-4">
            <p class="text-muted small">Need help? Contact our support team</p>
            <a href="{% url 'core:about' %}?inquiry_type=support" class="btn btn-outline-primary btn-sm w-100">
              <i class="bi bi-question-circle me-1"></i> Get Help
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Application Form -->
    <div class="col-lg-9">
      <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h1 class="h3 mb-0">{% trans "SMARCH Dream Job & Career Aspirations Survey" %}</h1>
        </div>
        <div class="card-body">
          <div class="alert alert-info mb-4">
            <i class="bi bi-info-circle me-2"></i>
            <p>Welcome to the SMARCH Dream Job & Career Aspirations Survey!</p>
            <p>At SMARCH (Smart Match Alliance Recruitment Collaboration Hub), our mission is to revolutionize the job market by connecting talented professionals with opportunities that perfectly align with their skills and aspirations.</p>
            <p>This survey is part of our pilot program and is completely free of charge. Your input will help us create tailored job opportunities and collaborative teams that can fast-track your career goals.</p>
            <p>Thank you for taking the time to share your dream job vision and relevant experiences. Let's work together to build a brighter future for your career!</p>
          </div>

          <form method="POST" action="{% url 'application:apply' %}" class="application-form">
            {% csrf_token %}

            <div class="accordion" id="applicationAccordion">
              <!-- Section 1: Basic Information -->
              <div class="accordion-item mb-3 border">
                <h2 class="accordion-header" id="headingBasic">
                  <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseBasic" aria-expanded="true" aria-controls="collapseBasic">
                    <i class="bi bi-person me-2"></i> Section 1: Basic Information
                  </button>
                </h2>
                <div id="collapseBasic" class="accordion-collapse collapse show" aria-labelledby="headingBasic" data-bs-parent="#applicationAccordion">
                  <div class="accordion-body">
                    <div class="row">
                      <div class="col-md-6">
                        {{ form.full_name|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.date_of_birth|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.current_location|as_crispy_field }}
                      </div>
                      <div class="col-md-12 other-location-field" style="display: none;">
                        {{ form.other_location|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.registered_with_arbetsformedlingen|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.employment_status|as_crispy_field }}
                      </div>
                      <div class="col-md-6 unemployed-since-field">
                        {{ form.unemployed_since|as_crispy_field }}
                      </div>
                      <div class="col-md-6 registered-unemployed-field">
                        {{ form.registered_unemployed_date|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.job_search_duration|as_crispy_field }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section 2: Dream Job Details -->
              <div class="accordion-item mb-3 border">
                <h2 class="accordion-header" id="headingDreamJob">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDreamJob" aria-expanded="false" aria-controls="collapseDreamJob">
                    <i class="bi bi-briefcase me-2"></i> Section 2: Dream Job Details
                  </button>
                </h2>
                <div id="collapseDreamJob" class="accordion-collapse collapse" aria-labelledby="headingDreamJob" data-bs-parent="#applicationAccordion">
                  <div class="accordion-body">
                    <div class="row">
                      <div class="col-md-12">
                        {{ form.dream_job|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.industry|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.category|as_crispy_field }}
                      </div>
                      <div class="col-md-12 other-category-field" style="display: none;">
                        {{ form.other_category|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.has_experience|as_crispy_field }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section 3: Experience and Growth -->
              <div class="accordion-item mb-3 border">
                <h2 class="accordion-header" id="headingExperience">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExperience" aria-expanded="false" aria-controls="collapseExperience">
                    <i class="bi bi-graph-up me-2"></i> Section 3: Experience and Growth
                  </button>
                </h2>
                <div id="collapseExperience" class="accordion-collapse collapse" aria-labelledby="headingExperience" data-bs-parent="#applicationAccordion">
                  <div class="accordion-body">
                    <div class="row">
                      <div class="col-md-12">
                        {{ form.years_of_experience|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.current_skills|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.additional_skills|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.acquiring_skills|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.achievable_in_5_years|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.challenges|as_crispy_field }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section 4: Work Preferences -->
              <div class="accordion-item mb-3 border">
                <h2 class="accordion-header" id="headingWorkPreferences">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseWorkPreferences" aria-expanded="false" aria-controls="collapseWorkPreferences">
                    <i class="bi bi-briefcase me-2"></i> Section 4: Work Preferences
                  </button>
                </h2>
                <div id="collapseWorkPreferences" class="accordion-collapse collapse" aria-labelledby="headingWorkPreferences" data-bs-parent="#applicationAccordion">
                  <div class="accordion-body">
                    <div class="row">
                      <div class="col-md-6">
                        {{ form.remote_work|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.willing_to_relocate|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.work_type|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.support_needed|as_crispy_field }}
                      </div>
                      <div class="col-md-12 other-support-field" style="display: none;">
                        {{ form.other_support|as_crispy_field }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section 5: Additional Information -->
              <div class="accordion-item mb-3 border">
                <h2 class="accordion-header" id="headingAdditional">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAdditional" aria-expanded="false" aria-controls="collapseAdditional">
                    <i class="bi bi-plus-circle me-2"></i> Section 5: Additional Information
                  </button>
                </h2>
                <div id="collapseAdditional" class="accordion-collapse collapse" aria-labelledby="headingAdditional" data-bs-parent="#applicationAccordion">
                  <div class="accordion-body">
                    <div class="row">
                      <div class="col-md-12">
                        {{ form.additional_comments|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.interested_in_pilot|as_crispy_field }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section 6: Non-Disclosure Agreement (NDA) (Non-collapsible) -->
              <div class="card mb-3 border">
                <div class="card-header bg-primary text-white">
                  <h5 class="card-title mb-0"><i class="bi bi-shield-lock me-2"></i> Section 6: Non-Disclosure Agreement (NDA)</h5>
                </div>
                <div class="card-body">
                  <div class="border border-warning rounded p-3 mb-4" style="background-color: #fff3cd;">
                    <h5 class="text-dark">{% trans "Non-Disclosure Agreement (NDA)" %}</h5>
                    <p>{% trans "To protect the business ideas and discussions during our program, please review our Non-Disclosure Agreement. By selecting 'I agree', you confirm that you will keep all proprietary information shared during the SMARCH sessions confidential and will not use or share this information outside the program without permission." %}</p>
                    <button type="button" class="btn btn-primary mb-3" data-bs-toggle="modal" data-bs-target="#ndaModal">
                      <i class="bi bi-file-text me-1"></i> {% trans "Read Full NDA Text" %}
                    </button>
                  </div>
                  <div class="row">
                    <div class="col-md-12">
                      {{ form.nda_agreement|as_crispy_field }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
              <button type="button" class="btn btn-outline-secondary me-md-2" id="saveDraftBtn">
                <i class="bi bi-save me-1"></i> Save as Draft
              </button>
              <button type="submit" name="submit_application" value="true" class="btn btn-primary">
                <i class="bi bi-send me-1"></i> Submit Application
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- NDA Modal -->
<div class="modal fade" id="ndaModal" tabindex="-1" aria-labelledby="ndaModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="ndaModalLabel">{% trans "Non-Disclosure Agreement (NDA)" %}</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <h6 class="text-muted mb-3">{% trans "Non-Disclosure Agreement (NDA) Text:" %}</h6>
        <p>{% trans "This agreement is made between the participant ('Recipient') and SMARCH ('Disclosing Party'). By participating in the SMARCH sessions, the Recipient agrees to keep confidential all proprietary or sensitive information shared during the meetings, including but not limited to business ideas, strategies, and concepts." %}</p>
        <p>{% trans "The Recipient agrees that any ideas, strategies, or concepts shared by other participants are the sole property of the original creator and cannot be used, developed, or shared outside of the SMARCH program without explicit permission." %}</p>
        <p>{% trans "This obligation remains in effect until the information becomes public, or is disclosed by SMARCH or the original creator, with any public disclosure of selected ideas to be mutually agreed upon between SMARCH and the creator, ensuring the best timing and approach for both parties." %}</p>
        <p>{% trans "Breach of this agreement may result in termination of program involvement and further legal action." %}</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Fix for NDA section auto-collapse issue
    const ndaButton = document.querySelector('button[data-bs-target="#collapseNDA"]');
    const ndaSection = document.getElementById('collapseNDA');

    if (ndaButton && ndaSection) {
      ndaButton.addEventListener('click', function(e) {
        // Prevent the default accordion behavior
        e.stopPropagation();

        // Toggle the section manually
        if (ndaSection.classList.contains('show')) {
          ndaSection.classList.remove('show');
          ndaButton.classList.add('collapsed');
          ndaButton.setAttribute('aria-expanded', 'false');
        } else {
          ndaSection.classList.add('show');
          ndaButton.classList.remove('collapsed');
          ndaButton.setAttribute('aria-expanded', 'true');
        }
      });
    }
    // Show/hide other location field based on selection
    const currentLocationField = document.getElementById('id_current_location');
    const otherLocationField = document.querySelector('.other-location-field');

    function toggleOtherLocation() {
      if (currentLocationField.value === 'other') {
        otherLocationField.style.display = 'block';
      } else {
        otherLocationField.style.display = 'none';
      }
    }

    currentLocationField.addEventListener('change', toggleOtherLocation);
    toggleOtherLocation(); // Initial check

    // Show/hide other category field based on selection
    const categoryField = document.getElementById('id_category');
    const otherCategoryField = document.querySelector('.other-category-field');

    function toggleOtherCategory() {
      if (categoryField.value === 'other') {
        otherCategoryField.style.display = 'block';
      } else {
        otherCategoryField.style.display = 'none';
      }
    }

    categoryField.addEventListener('change', toggleOtherCategory);
    toggleOtherCategory(); // Initial check

    // Show/hide other support field based on selection
    const supportNeededField = document.getElementById('id_support_needed');
    const otherSupportField = document.querySelector('.other-support-field');

    function toggleOtherSupport() {
      // Check if any of the selected options is 'other'
      const selectedOptions = Array.from(supportNeededField.selectedOptions);
      const hasOtherSelected = selectedOptions.some(option => option.value === 'other');

      if (hasOtherSelected) {
        otherSupportField.style.display = 'block';
      } else {
        otherSupportField.style.display = 'none';
      }
    }

    if (supportNeededField) {
      supportNeededField.addEventListener('change', toggleOtherSupport);
      toggleOtherSupport(); // Initial check
    }

    // Show/hide unemployed since field based on employment status
    const employmentStatusField = document.getElementById('id_employment_status');
    const unemployedSinceField = document.querySelector('.unemployed-since-field');

    function toggleUnemployedSince() {
      if (employmentStatusField && employmentStatusField.value.includes('unemployed')) {
        unemployedSinceField.style.display = 'block';
      } else {
        unemployedSinceField.style.display = 'none';
      }
    }

    if (employmentStatusField) {
      employmentStatusField.addEventListener('change', toggleUnemployedSince);
      toggleUnemployedSince(); // Initial check
    }

    // Show/hide registered unemployed date field based on registration status
    const registeredField = document.getElementById('id_registered_with_arbetsformedlingen');
    const registeredUnemployedField = document.querySelector('.registered-unemployed-field');

    function toggleRegisteredUnemployed() {
      if (registeredField && registeredField.value === 'yes') {
        registeredUnemployedField.style.display = 'block';
      } else {
        registeredUnemployedField.style.display = 'none';
      }
    }

    if (registeredField) {
      registeredField.addEventListener('change', toggleRegisteredUnemployed);
      toggleRegisteredUnemployed(); // Initial check
    }

    // Save as draft functionality
    document.getElementById('saveDraftBtn').addEventListener('click', function() {
      // Add a hidden field to indicate this is a draft save
      const draftInput = document.createElement('input');
      draftInput.type = 'hidden';
      draftInput.name = 'save_draft';
      draftInput.value = 'true';
      document.querySelector('form').appendChild(draftInput);

      // Submit the form
      document.querySelector('form').submit();
    });
  });
</script>
{% endblock %}