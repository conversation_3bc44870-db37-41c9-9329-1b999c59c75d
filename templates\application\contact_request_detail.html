{% extends 'common/base.html' %}
{% load static %}

{% block title %}Contact Request Details - Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row mb-4">
    <div class="col-md-8">
      <h1 class="mb-3">Contact Request Details</h1>
      <p class="text-muted">Review and respond to this contact request.</p>
    </div>
    <div class="col-md-4 text-md-end">
      <a href="{% url 'application:contact_requests' %}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> Back to Requests
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8">
      <!-- Contact Request Card -->
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Request Information</h5>
          <span class="badge {% if contact_request.status == 'pending' %}bg-warning text-dark{% elif contact_request.status == 'accepted' %}bg-success{% elif contact_request.status == 'declined' %}bg-danger{% endif %}">
            {{ contact_request.status|title }}
          </span>
        </div>
        <div class="card-body">
          <div class="mb-4">
            <h6 class="text-muted mb-2">From:</h6>
            <div class="d-flex align-items-center">
              {% if contact_request.sender.profile_image %}
                <img src="{{ contact_request.sender.profile_image.url }}" alt="{{ contact_request.sender.company_name }}" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
              {% else %}
                <div class="bg-light rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                  <i class="bi bi-building fs-4"></i>
                </div>
              {% endif %}
              <div>
                <h5 class="mb-0">{{ contact_request.sender.company_name }}</h5>
                <p class="text-muted mb-0">{{ contact_request.sender.industry }}</p>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <h6 class="text-muted mb-2">Message:</h6>
            <div class="card bg-light">
              <div class="card-body">
                <p class="mb-0">{{ contact_request.message }}</p>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <h6 class="text-muted mb-2">Sent on:</h6>
            <p>{{ contact_request.sent_at|date:"F d, Y" }} at {{ contact_request.sent_at|time:"g:i A" }}</p>
          </div>

          {% if contact_request.status == 'accepted' %}
            <div class="alert alert-success">
              <i class="bi bi-check-circle me-2"></i> You have accepted this contact request. The hiring partner can now see your contact information.
            </div>
            <div class="card border-success mb-4">
              <div class="card-header bg-success text-white">
                <h6 class="mb-0">Hiring Partner Contact Information</h6>
              </div>
              <div class="card-body">
                <p class="mb-2"><strong>Email:</strong> {{ contact_request.sender.user.email }}</p>
                {% if contact_request.sender.phone_number %}
                  <p class="mb-0"><strong>Phone:</strong> {{ contact_request.sender.phone_number }}</p>
                {% endif %}
                {% if contact_request.sender.company_website %}
                  <p class="mb-0"><strong>Website:</strong> <a href="{{ contact_request.sender.company_website }}" target="_blank">{{ contact_request.sender.company_website }}</a></p>
                {% endif %}

                <div class="mt-3">
                  <a href="{% url 'messaging:new_conversation' contact_request.sender.user.id %}" class="btn btn-primary">
                    <i class="bi bi-chat-dots me-2"></i> Start Conversation
                  </a>
                </div>
              </div>
            </div>
          {% elif contact_request.status == 'declined' %}
            <div class="alert alert-danger">
              <i class="bi bi-x-circle me-2"></i> You have declined this contact request.
            </div>
          {% else %}
            <div class="alert alert-warning">
              <i class="bi bi-exclamation-triangle me-2"></i> This contact request is pending your response.
            </div>
          {% endif %}
        </div>
        <div class="card-footer">
          <div class="d-flex justify-content-between">
            {% if contact_request.status == 'pending' %}
              <div>
                <a href="{% url 'application:contact_request_respond' contact_request.id 'accept' %}" class="btn btn-success">
                  <i class="bi bi-check-circle"></i> Accept Request
                </a>
                <a href="{% url 'application:contact_request_respond' contact_request.id 'decline' %}" class="btn btn-danger">
                  <i class="bi bi-x-circle"></i> Decline Request
                </a>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <!-- Hiring Partner Info Card -->
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Hiring Partner Information</h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            {% if contact_request.sender.profile_image %}
              <img src="{{ contact_request.sender.profile_image.url }}" alt="{{ contact_request.sender.company_name }}" class="rounded-circle mb-3" style="width: 100px; height: 100px; object-fit: cover;">
            {% else %}
              <div class="bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                <i class="bi bi-building fs-1"></i>
              </div>
            {% endif %}
            <h5>{{ contact_request.sender.company_name }}</h5>
            <p class="text-muted">{{ contact_request.sender.industry }}</p>
          </div>

          <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span><i class="bi bi-geo-alt me-2"></i> Location</span>
              <span class="text-muted">{{ contact_request.sender.primary_location }}</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span><i class="bi bi-people me-2"></i> Company Size</span>
              <span class="text-muted">{{ contact_request.sender.company_size }}</span>
            </li>
            {% if contact_request.sender.company_website %}
              <li class="list-group-item">
                <i class="bi bi-globe me-2"></i> <a href="{{ contact_request.sender.company_website }}" target="_blank">Visit Website</a>
              </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
