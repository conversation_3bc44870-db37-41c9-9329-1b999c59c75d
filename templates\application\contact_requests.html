{% extends 'common/base.html' %}
{% load static %}

{% block title %}Contact Requests - Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row mb-4">
    <div class="col-md-8">
      <h1 class="mb-3">Contact Requests</h1>
      <p class="text-muted">Manage contact requests from hiring partners.</p>
    </div>
    <div class="col-md-4 text-md-end">
      <a href="{% url 'users:dashboard' %}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> Back to Dashboard
      </a>
    </div>
  </div>

  <!-- Contact Requests Table -->
  <div class="card">
    <div class="card-body">
      {% if page_obj %}
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Date</th>
                <th>Hiring Partner</th>
                <th>Message</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for request in page_obj %}
                <tr>
                  <td>{{ request.sent_at|date:"M d, Y" }}</td>
                  <td>
                    {{ request.sender.company_name }}
                  </td>
                  <td>{{ request.message|truncatechars:50 }}</td>
                  <td>
                    {% if request.status == 'pending' %}
                      <span class="badge bg-warning text-dark">Pending</span>
                    {% elif request.status == 'accepted' %}
                      <span class="badge bg-success">Accepted</span>
                    {% elif request.status == 'declined' %}
                      <span class="badge bg-danger">Declined</span>
                    {% endif %}
                  </td>
                  <td>
                    <a href="{% url 'application:contact_request_detail' request.id %}" class="btn btn-sm btn-primary">View</a>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
          <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
              {% if page_obj.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% endif %}

              {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                  <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                  <li class="page-item">
                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                  </li>
                {% endif %}
              {% endfor %}

              {% if page_obj.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
              {% endif %}
            </ul>
          </nav>
        {% endif %}
      {% else %}
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i> You don't have any contact requests from hiring partners yet.
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
