{% extends 'common/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Application" %} | Smarch{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 text-center">
            <h1 class="mb-4">{% trans "Start Your Application" %}</h1>
            <p class="lead mb-4">{% trans "Begin your journey with <PERSON><PERSON><PERSON> by submitting your application. We'll help you find the perfect match for your skills and aspirations." %}</p>
            
            {% if user.is_authenticated %}
                {% if user.user_type == 'applicant' %}
                    <div class="d-grid gap-3 d-sm-flex justify-content-sm-center">
                        <a href="{% url 'application:apply' %}" class="btn btn-primary btn-lg px-4 gap-3">
                            {% trans "Submit Application" %}
                        </a>
                        <a href="{% url 'application:status' %}" class="btn btn-outline-secondary btn-lg px-4">
                            {% trans "Check Status" %}
                        </a>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        {% trans "You need to create an applicant profile to submit an application." %}
                        <a href="{% url 'users:applicant_signup' %}" class="alert-link">{% trans "Create Profile" %}</a>
                    </div>
                {% endif %}
            {% else %}
                <div class="d-grid gap-3 d-sm-flex justify-content-sm-center">
                    <a href="{% url 'account_signup' %}" class="btn btn-primary btn-lg px-4 gap-3">
                        {% trans "Sign Up" %}
                    </a>
                    <a href="{% url 'account_login' %}" class="btn btn-outline-secondary btn-lg px-4">
                        {% trans "Login" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <div class="row mt-5">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-3x mb-3 text-primary"></i>
                    <h3 class="card-title">{% trans "Simple Process" %}</h3>
                    <p class="card-text">{% trans "Fill out our easy-to-use application form and let us know about your skills and preferences." %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-search fa-3x mb-3 text-success"></i>
                    <h3 class="card-title">{% trans "Smart Matching" %}</h3>
                    <p class="card-text">{% trans "Our intelligent system matches you with the best opportunities based on your profile." %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-3x mb-3 text-info"></i>
                    <h3 class="card-title">{% trans "Track Progress" %}</h3>
                    <p class="card-text">{% trans "Monitor your application status and receive updates throughout the process." %}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 