{% extends "common/base.html" %}
{% load i18n %}

{% block title %}{% trans "Application Status" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <!-- Sidebar - Application Steps -->
    <div class="col-lg-3 mb-4">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h5 class="card-title mb-0">Application Process</h5>
        </div>
        <div class="card-body">
          <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex align-items-center">
              <span class="badge bg-primary rounded-circle me-2">1</span>
              Create Profile
              <i class="bi bi-check-circle-fill text-success ms-auto"></i>
            </li>
            <li class="list-group-item d-flex align-items-center">
              <span class="badge bg-primary rounded-circle me-2">2</span>
              Complete Application
              <i class="bi bi-check-circle-fill text-success ms-auto"></i>
            </li>
            <li class="list-group-item d-flex align-items-center active">
              <span class="badge bg-primary rounded-circle me-2">3</span>
              Application Review
              <i class="bi bi-hourglass-split ms-auto"></i>
            </li>
            <li class="list-group-item d-flex align-items-center text-muted">
              <span class="badge bg-secondary rounded-circle me-2">4</span>
              Matching Process
            </li>
          </ul>

          <div class="mt-4">
            <p class="text-muted small">Need help? Contact our support team</p>
            <a href="{% url 'core:about' %}?inquiry_type=support" class="btn btn-outline-primary btn-sm w-100">
              <i class="bi bi-question-circle me-1"></i> Get Help
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="col-lg-9">
      <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h1 class="h3 mb-0">{% trans "Application Status" %}</h1>
          <a href="{% url 'application:update' %}" class="btn btn-light btn-sm">
            <i class="bi bi-pencil me-1"></i> {% trans "Update Application" %}
          </a>
        </div>
        <div class="card-body">
          <div class="alert alert-info mb-4">
            <i class="bi bi-info-circle me-2"></i>
            {% blocktrans %}Your application is being reviewed. We will notify you of any updates or changes to your status.{% endblocktrans %}
          </div>

          <div class="row mb-4">
            <div class="col-12">
              <h5 class="border-bottom pb-2 mb-3">Application Overview</h5>

              {% with latest_application=applications.0 %}
              <div class="row">
                <div class="col-md-3">
                  <p class="text-muted mb-1">Application ID</p>
                  <p class="fw-bold">APP-{{ latest_application.id }}</p>
                </div>
                <div class="col-md-3">
                  <p class="text-muted mb-1">Case Number</p>
                  <p class="fw-bold">{{ latest_application.applicant.case_number }}</p>
                </div>
                <div class="col-md-3">
                  <p class="text-muted mb-1">Queue Position</p>
                  <p class="fw-bold">{{ latest_application.applicant.get_display_queue_position }}</p>
                </div>
                <div class="col-md-3">
                  <p class="text-muted mb-1">Status</p>
                  <p>
                    {% if latest_application.status == 'draft' %}
                      <span class="badge bg-secondary">Draft</span>
                    {% elif latest_application.status == 'submitted' %}
                      <span class="badge bg-info">Submitted</span>
                    {% elif latest_application.status == 'in_review' %}
                      <span class="badge bg-warning text-dark">In Review</span>
                    {% elif latest_application.status == 'approved' %}
                      <span class="badge bg-success">Approved</span>
                    {% elif latest_application.status == 'rejected' %}
                      <span class="badge bg-danger">Rejected</span>
                    {% endif %}
                  </p>
                </div>
              </div>

              <div class="row mt-3">
                <div class="col-md-6">
                  <p class="text-muted mb-1">Submission Date</p>
                  <p class="fw-bold">{{ latest_application.submitted_at|date:"F d, Y" }}</p>
                </div>
                <div class="col-md-6">
                  <p class="text-muted mb-1">Last Updated</p>
                  <p class="fw-bold">{{ latest_application.updated_at|date:"F d, Y" }}</p>
                </div>
              </div>

              {% if latest_application.status != 'draft' %}
              <div class="mt-4">
                <h6 class="border-bottom pb-2 mb-3">Dream Job Details</h6>
                <div class="row">
                  {% if latest_application.refinedinfo %}
                  <div class="col-md-4">
                    <p class="text-muted mb-1">Industry</p>
                    <p class="fw-bold">{{ latest_application.refinedinfo.industry|default:"Not specified" }}</p>
                  </div>
                  <div class="col-md-4">
                    <p class="text-muted mb-1">Branch</p>
                    <p class="fw-bold">{{ latest_application.refinedinfo.branch|default:"Not specified" }}</p>
                  </div>
                  <div class="col-md-4">
                    <p class="text-muted mb-1">Role</p>
                    <p class="fw-bold">{{ latest_application.refinedinfo.role|default:"Not specified" }}</p>
                  </div>
                  {% else %}
                  <div class="col-12">
                    <div class="alert alert-warning">
                      <i class="bi bi-exclamation-triangle me-2"></i>
                      <p>Your application is still being processed. Dream job details will appear here once processing is complete.</p>
                      <div class="mt-2">
                        <form method="POST" action="{% url 'application:process' latest_application.id %}">
                          {% csrf_token %}
                          <button type="submit" class="btn btn-sm btn-warning">
                            <i class="bi bi-arrow-repeat me-1"></i> Process Application
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>
                  {% endif %}
                </div>

                {% if latest_application.refinedinfo %}
                <div class="row mt-3">
                  <div class="col-md-4">
                    <p class="text-muted mb-1">Experience</p>
                    <p class="fw-bold">{{ latest_application.refinedinfo.experience_years }} years</p>
                  </div>
                  <div class="col-md-8">
                    <p class="text-muted mb-1">Work Preferences</p>
                    <p>
                      {% for pref in latest_application.refinedinfo.work_preferences %}
                        <span class="badge bg-light text-dark me-1">
                          {% if pref == 'full_time' %}Full time
                          {% elif pref == 'part_time' %}Part time
                          {% elif pref == 'freelance' %}Freelance
                          {% else %}{{ pref }}
                          {% endif %}
                        </span>
                      {% empty %}
                        <span class="text-muted">Not specified</span>
                      {% endfor %}
                    </p>
                  </div>
                </div>

                <div class="row mt-3">
                  <div class="col-12">
                    <p class="text-muted mb-1">Skills</p>
                    <p>
                      {% for skill in latest_application.refinedinfo.skills %}
                        <span class="badge bg-primary me-1">{{ skill }}</span>
                      {% empty %}
                        <span class="text-muted">Not specified</span>
                      {% endfor %}
                    </p>
                  </div>
                </div>

                <div class="row mt-3">
                  <div class="col-12">
                    <p class="text-muted mb-1">Languages</p>
                    <p>
                      {% for language in latest_application.refinedinfo.languages %}
                        <span class="badge bg-info text-dark me-1">{{ language }}</span>
                      {% empty %}
                        <span class="text-muted">Not specified</span>
                      {% endfor %}
                    </p>
                  </div>
                </div>
                {% endif %}
              </div>
              {% endif %}
              {% endwith %}
            </div>
          </div>

          <!-- Status Timeline -->
          <div class="row mb-4">
            <div class="col-12">
              <h5 class="border-bottom pb-2 mb-3">Application Timeline</h5>

              <div class="position-relative pb-5">
                <!-- Timeline Line -->
                <div class="position-absolute h-100" style="width: 2px; background-color: #dee2e6; left: 15px; top: 0;"></div>

                {% with latest_application=applications.0 %}
                <!-- Submitted -->
                <div class="d-flex mb-4 position-relative">
                  <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; z-index: 1;">
                    <i class="bi bi-check-lg"></i>
                  </div>
                  <div class="ms-4">
                    <h6 class="mb-1">Application Submitted</h6>
                    <p class="text-muted small mb-2">{{ latest_application.submitted_at|date:"F d, Y - h:i A" }}</p>
                    <p class="mb-0">Your application has been successfully submitted.</p>
                  </div>
                </div>

                <!-- In Review -->
                <div class="d-flex mb-4 position-relative">
                  {% if latest_application.status == 'draft' or latest_application.status == 'submitted' %}
                    <div class="rounded-circle bg-light text-muted d-flex align-items-center justify-content-center border" style="width: 32px; height: 32px; z-index: 1;">
                      <i class="bi bi-hourglass"></i>
                    </div>
                  {% else %}
                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; z-index: 1;">
                      <i class="bi bi-check-lg"></i>
                    </div>
                  {% endif %}
                  <div class="ms-4">
                    <h6 class="mb-1">Application Review</h6>
                    {% if latest_application.status == 'in_review' or latest_application.status == 'approved' or latest_application.status == 'rejected' %}
                      <p class="text-muted small mb-2">{{ latest_application.review_date|date:"F d, Y - h:i A"|default:"In progress" }}</p>
                      <p class="mb-0">Your application is being reviewed by our team.</p>
                    {% else %}
                      <p class="text-muted small mb-2">Pending</p>
                      <p class="mb-0">Waiting for review to begin.</p>
                    {% endif %}
                  </div>
                </div>

                <!-- Decision -->
                <div class="d-flex position-relative">
                  {% if latest_application.status == 'approved' or latest_application.status == 'rejected' %}
                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; z-index: 1;">
                      <i class="bi bi-check-lg"></i>
                    </div>
                  {% else %}
                    <div class="rounded-circle bg-light text-muted d-flex align-items-center justify-content-center border" style="width: 32px; height: 32px; z-index: 1;">
                      <i class="bi bi-hourglass"></i>
                    </div>
                  {% endif %}
                  <div class="ms-4">
                    <h6 class="mb-1">Final Decision</h6>
                    {% if latest_application.status == 'approved' %}
                      <p class="text-muted small mb-2">{{ latest_application.review_date|date:"F d, Y - h:i A" }}</p>
                      <p class="mb-0 text-success"><i class="bi bi-check-circle me-1"></i> Your application has been approved!</p>
                    {% elif latest_application.status == 'rejected' %}
                      <p class="text-muted small mb-2">{{ latest_application.review_date|date:"F d, Y - h:i A" }}</p>
                      <p class="mb-0 text-danger"><i class="bi bi-x-circle me-1"></i> Unfortunately, your application was not approved at this time.</p>
                    {% else %}
                      <p class="text-muted small mb-2">Pending</p>
                      <p class="mb-0">Waiting for final decision.</p>
                    {% endif %}
                  </div>
                </div>
                {% endwith %}
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="d-flex justify-content-between mt-4">
            <a href="{% url 'users:profile' %}" class="btn btn-outline-secondary">
              <i class="bi bi-person me-1"></i> View Profile
            </a>
            <div>
              {% with latest_application=applications.0 %}
              {% if latest_application.status == 'draft' %}
                <a href="{% url 'application:apply' %}" class="btn btn-primary">
                  <i class="bi bi-pencil me-1"></i> Continue Application
                </a>
              {% elif latest_application.status == 'rejected' %}
                <a href="{% url 'application:apply' %}" class="btn btn-primary">
                  <i class="bi bi-plus-circle me-1"></i> Submit New Application
                </a>
              {% else %}
                <button type="button" class="btn btn-secondary" disabled>
                  <i class="bi bi-hourglass-split me-1"></i> Application in Progress
                </button>
              {% endif %}
              {% endwith %}
            </div>
          </div>
        </div>
      </div>

      <!-- Previous Applications -->
      {% if applications.count > 1 %}
      <div class="card shadow mt-4">
        <div class="card-header bg-light">
          <h5 class="card-title mb-0">Previous Applications</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Submission Date</th>
                  <th>Status</th>
                  <th>Review Date</th>
                </tr>
              </thead>
              <tbody>
                {% for application in applications %}
                {% if not forloop.first %}
                <tr>
                  <td>APP-{{ application.id }}</td>
                  <td>{{ application.submitted_at|date:"M d, Y" }}</td>
                  <td>
                    {% if application.status == 'draft' %}
                      <span class="badge bg-secondary">Draft</span>
                    {% elif application.status == 'submitted' %}
                      <span class="badge bg-info">Submitted</span>
                    {% elif application.status == 'in_review' %}
                      <span class="badge bg-warning text-dark">In Review</span>
                    {% elif application.status == 'approved' %}
                      <span class="badge bg-success">Approved</span>
                    {% elif application.status == 'rejected' %}
                      <span class="badge bg-danger">Rejected</span>
                    {% endif %}
                  </td>
                  <td>{{ application.review_date|date:"M d, Y"|default:"-" }}</td>
                </tr>
                {% endif %}
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}