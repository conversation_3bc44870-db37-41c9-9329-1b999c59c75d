{% extends "common/base.html" %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Update Your Application" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <!-- Sidebar - Application Steps -->
    <div class="col-lg-3 mb-4">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h5 class="card-title mb-0">Application Process</h5>
        </div>
        <div class="card-body">
          <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex align-items-center">
              <span class="badge bg-primary rounded-circle me-2">1</span>
              Create Profile
              <i class="bi bi-check-circle-fill text-success ms-auto"></i>
            </li>
            <li class="list-group-item d-flex align-items-center">
              <span class="badge bg-primary rounded-circle me-2">2</span>
              Complete Survey
              <i class="bi bi-check-circle-fill text-success ms-auto"></i>
            </li>
            <li class="list-group-item d-flex align-items-center active">
              <span class="badge bg-primary rounded-circle me-2">3</span>
              Update Application
              <i class="bi bi-arrow-right ms-auto"></i>
            </li>
            <li class="list-group-item d-flex align-items-center text-muted">
              <span class="badge bg-secondary rounded-circle me-2">4</span>
              Matching Process
            </li>
          </ul>

          <div class="mt-4">
            <p class="text-muted small">Need help? Contact our support team</p>
            <a href="{% url 'core:about' %}?inquiry_type=support" class="btn btn-outline-primary btn-sm w-100">
              <i class="bi bi-question-circle me-1"></i> Get Help
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Application Form -->
    <div class="col-lg-9">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h2 class="h4 mb-0">{% trans "Update Your Application" %}</h2>
        </div>
        <div class="card-body">
          <div class="alert alert-info mb-4">
            <i class="bi bi-info-circle me-2"></i>
            <p>{% trans "We've added some new questions to help us better match you with opportunities. Please update your application with this additional information." %}</p>
          </div>

          <form method="POST" action="{% url 'application:update' %}" class="application-form">
            {% csrf_token %}

            <div class="accordion" id="applicationAccordion">
              <!-- Section 1: Basic Information -->
              <div class="accordion-item mb-3">
                <h2 class="accordion-header" id="headingOne">
                  <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                    <i class="bi bi-person-badge me-2"></i> {% trans "Basic Information" %}
                  </button>
                </h2>
                <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#applicationAccordion">
                  <div class="accordion-body">
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        {{ form.full_name|as_crispy_field }}
                      </div>
                      <div class="col-md-6 mb-3">
                        {{ form.date_of_birth|as_crispy_field }}
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        {{ form.current_location|as_crispy_field }}
                      </div>
                      <div class="col-md-6 mb-3 other-location-field" style="display: none;">
                        {{ form.other_location|as_crispy_field }}
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        {{ form.registered_with_arbetsformedlingen|as_crispy_field }}
                      </div>
                      <div class="col-md-6 mb-3">
                        {{ form.employment_status|as_crispy_field }}
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        {{ form.unemployed_since|as_crispy_field }}
                      </div>
                      <div class="col-md-6 mb-3">
                        {{ form.registered_unemployed_date|as_crispy_field }}
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-12 mb-3">
                        {{ form.job_search_duration|as_crispy_field }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section 2: Dream Job Details -->
              <div class="accordion-item mb-3">
                <h2 class="accordion-header" id="headingTwo">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                    <i class="bi bi-briefcase me-2"></i> {% trans "Dream Job Details" %}
                  </button>
                </h2>
                <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#applicationAccordion">
                  <div class="accordion-body">
                    <div class="mb-3">
                      {{ form.dream_job|as_crispy_field }}
                    </div>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        {{ form.industry|as_crispy_field }}
                      </div>
                      <div class="col-md-6 mb-3">
                        {{ form.category|as_crispy_field }}
                      </div>
                    </div>
                    <div class="mb-3 other-category-field" style="display: none;">
                      {{ form.other_category|as_crispy_field }}
                    </div>
                    <div class="mb-3">
                      {{ form.has_experience|as_crispy_field }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section 3: Experience and Growth -->
              <div class="accordion-item mb-3">
                <h2 class="accordion-header" id="headingThree">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                    <i class="bi bi-graph-up me-2"></i> {% trans "Experience and Growth" %}
                  </button>
                </h2>
                <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#applicationAccordion">
                  <div class="accordion-body">
                    <div class="mb-3">
                      {{ form.years_of_experience|as_crispy_field }}
                    </div>
                    <div class="mb-3">
                      {{ form.current_skills|as_crispy_field }}
                    </div>
                    <div class="mb-3">
                      {{ form.additional_skills|as_crispy_field }}
                    </div>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        {{ form.acquiring_skills|as_crispy_field }}
                      </div>
                      <div class="col-md-6 mb-3">
                        {{ form.achievable_in_5_years|as_crispy_field }}
                      </div>
                    </div>
                    <div class="mb-3">
                      {{ form.challenges|as_crispy_field }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section 4: Work Preferences -->
              <div class="accordion-item mb-3">
                <h2 class="accordion-header" id="headingWorkPreferences">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseWorkPreferences" aria-expanded="false" aria-controls="collapseWorkPreferences">
                    <i class="bi bi-briefcase me-2"></i> {% trans "Work Preferences" %}
                  </button>
                </h2>
                <div id="collapseWorkPreferences" class="accordion-collapse collapse" aria-labelledby="headingWorkPreferences" data-bs-parent="#applicationAccordion">
                  <div class="accordion-body">
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        {{ form.remote_work|as_crispy_field }}
                      </div>
                      <div class="col-md-6 mb-3">
                        {{ form.willing_to_relocate|as_crispy_field }}
                      </div>
                    </div>
                    <div class="mb-3">
                      {{ form.work_type|as_crispy_field }}
                    </div>
                    <div class="mb-3">
                      {{ form.support_needed|as_crispy_field }}
                    </div>
                    <div class="mb-3 other-support-field" style="display: none;">
                      {{ form.other_support|as_crispy_field }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section 5: Additional Information -->
              <div class="accordion-item mb-3">
                <h2 class="accordion-header" id="headingFive">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                    <i class="bi bi-info-circle me-2"></i> {% trans "Additional Information" %}
                  </button>
                </h2>
                <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#applicationAccordion">
                  <div class="accordion-body">
                    <div class="mb-3">
                      {{ form.additional_comments|as_crispy_field }}
                    </div>
                    <div class="mb-3">
                      {{ form.interested_in_pilot|as_crispy_field }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Section 6: NDA (Non-collapsible) -->
              <div class="card mb-3">
                <div class="card-header bg-primary text-white">
                  <h5 class="card-title mb-0"><i class="bi bi-shield-lock me-2"></i> {% trans "Non-Disclosure Agreement" %}</h5>
                </div>
                <div class="card-body">
                  <div class="border border-warning rounded p-3 mb-3" style="background-color: #fff3cd;">
                    <h5 class="text-dark">{% trans "Non-Disclosure Agreement (NDA)" %}</h5>
                    <p>{% trans "To protect the business ideas and discussions during our program, please review our Non-Disclosure Agreement. By selecting 'I agree', you confirm that you will keep all proprietary information shared during the SMARCH sessions confidential and will not use or share this information outside the program without permission." %}</p>
                    <button type="button" class="btn btn-primary mb-3" data-bs-toggle="modal" data-bs-target="#ndaModal">
                      <i class="bi bi-file-text me-1"></i> {% trans "Read Full NDA Text" %}
                    </button>
                  </div>
                  <div class="mb-3">
                    {{ form.nda_agreement|as_crispy_field }}
                  </div>
                </div>
              </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
              <a href="{% url 'application:status' %}" class="btn btn-outline-secondary me-md-2">
                <i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}
              </a>
              <button type="submit" name="update_application" value="true" class="btn btn-primary">
                <i class="bi bi-check-circle me-1"></i> {% trans "Update Application" %}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- NDA Modal -->
<div class="modal fade" id="ndaModal" tabindex="-1" aria-labelledby="ndaModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="ndaModalLabel">{% trans "Non-Disclosure Agreement (NDA)" %}</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <h6 class="text-muted mb-3">{% trans "Non-Disclosure Agreement (NDA) Text:" %}</h6>
        <p>{% trans "This agreement is made between the participant ('Recipient') and SMARCH ('Disclosing Party'). By participating in the SMARCH sessions, the Recipient agrees to keep confidential all proprietary or sensitive information shared during the meetings, including but not limited to business ideas, strategies, and concepts." %}</p>
        <p>{% trans "The Recipient agrees that any ideas, strategies, or concepts shared by other participants are the sole property of the original creator and cannot be used, developed, or shared outside of the SMARCH program without explicit permission." %}</p>
        <p>{% trans "This obligation remains in effect until the information becomes public, or is disclosed by SMARCH or the original creator, with any public disclosure of selected ideas to be mutually agreed upon between SMARCH and the creator, ensuring the best timing and approach for both parties." %}</p>
        <p>{% trans "Breach of this agreement may result in termination of program involvement and further legal action." %}</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Fix for NDA section auto-collapse issue
    const ndaButton = document.querySelector('button[data-bs-target="#collapseSix"]');
    const ndaSection = document.getElementById('collapseSix');

    if (ndaButton && ndaSection) {
      ndaButton.addEventListener('click', function(e) {
        // Prevent the default accordion behavior
        e.stopPropagation();

        // Toggle the section manually
        if (ndaSection.classList.contains('show')) {
          ndaSection.classList.remove('show');
          ndaButton.classList.add('collapsed');
          ndaButton.setAttribute('aria-expanded', 'false');
        } else {
          ndaSection.classList.add('show');
          ndaButton.classList.remove('collapsed');
          ndaButton.setAttribute('aria-expanded', 'true');
        }
      });
    }
    // Show/hide other location field based on selection
    const currentLocationField = document.getElementById('id_current_location');
    const otherLocationField = document.querySelector('.other-location-field');

    function toggleOtherLocation() {
      if (currentLocationField.value === 'other') {
        otherLocationField.style.display = 'block';
      } else {
        otherLocationField.style.display = 'none';
      }
    }

    currentLocationField.addEventListener('change', toggleOtherLocation);
    toggleOtherLocation(); // Initial check

    // Show/hide other category field based on selection
    const categoryField = document.getElementById('id_category');
    const otherCategoryField = document.querySelector('.other-category-field');

    function toggleOtherCategory() {
      if (categoryField.value === 'other') {
        otherCategoryField.style.display = 'block';
      } else {
        otherCategoryField.style.display = 'none';
      }
    }

    categoryField.addEventListener('change', toggleOtherCategory);
    toggleOtherCategory(); // Initial check

    // Show/hide other support field based on selection
    const supportNeededField = document.getElementById('id_support_needed');
    const otherSupportField = document.querySelector('.other-support-field');

    function toggleOtherSupport() {
      // Check if any of the selected options is 'other'
      const selectedOptions = Array.from(supportNeededField.selectedOptions);
      const hasOtherSelected = selectedOptions.some(option => option.value === 'other');

      if (hasOtherSelected) {
        otherSupportField.style.display = 'block';
      } else {
        otherSupportField.style.display = 'none';
      }
    }

    if (supportNeededField) {
      supportNeededField.addEventListener('change', toggleOtherSupport);
      toggleOtherSupport(); // Initial check
    }

    // Show/hide unemployed_since field based on employment status
    const employmentStatusField = document.getElementById('id_employment_status');
    const unemployedSinceField = document.getElementById('id_unemployed_since').closest('.mb-3');

    function toggleUnemployedSince() {
      if (employmentStatusField.value.includes('unemployed')) {
        unemployedSinceField.style.display = 'block';
      } else {
        unemployedSinceField.style.display = 'none';
      }
    }

    employmentStatusField.addEventListener('change', toggleUnemployedSince);
    toggleUnemployedSince(); // Initial check

    // Show/hide registered unemployed date field based on registration status
    const registeredField = document.getElementById('id_registered_with_arbetsformedlingen');
    const registeredUnemployedField = document.getElementById('id_registered_unemployed_date').closest('.mb-3');

    function toggleRegisteredUnemployed() {
      if (registeredField.value === 'yes') {
        registeredUnemployedField.style.display = 'block';
      } else {
        registeredUnemployedField.style.display = 'none';
      }
    }

    registeredField.addEventListener('change', toggleRegisteredUnemployed);
    toggleRegisteredUnemployed(); // Initial check
  });
</script>
{% endblock %}
