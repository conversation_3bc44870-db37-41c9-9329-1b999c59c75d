{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ post.title }} | {% trans "Blog" %} | Smarch{% endblock %}

{% block meta %}
<meta name="description" content="{{ post.excerpt|default:post.content|truncatechars:160 }}">
<meta property="og:title" content="{{ post.title }} | Smarch">
<meta property="og:description" content="{{ post.excerpt|default:post.content|truncatechars:160 }}">
{% if post.featured_image %}
<meta property="og:image" content="{{ post.featured_image.url }}">
{% endif %}
<meta property="og:type" content="article">
<meta property="article:published_time" content="{{ post.published_date|date:'c' }}">
<meta property="article:author" content="{{ post.author.get_full_name }}">
{% for category in post.categories.all %}
<meta property="article:tag" content="{{ category.name }}">
{% endfor %}
{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <!-- Main Content -->
    <div class="col-lg-8">
      <!-- Article Header -->
      <header class="mb-4">
        <h1 class="display-4 fw-bold">{{ post.title }}</h1>
        <div class="text-muted mb-3">
          {% if post.author %}
          <span>{% trans "By" %} <a href="{% url 'blog:author' post.author.username %}">{{ post.author.get_full_name }}</a></span>
          {% endif %}
          <span class="mx-1">•</span>
          <span>{{ post.published_date|date:"F j, Y" }}</span>
          {% if post.categories.exists %}
          <span class="mx-1">•</span>
          {% for category in post.categories.all %}
            <a href="{% url 'blog:category' category.slug %}" class="badge bg-secondary text-decoration-none link-light">{{ category.name }}</a>
          {% endfor %}
          {% endif %}
        </div>
      </header>
      
      <!-- Featured Image -->
      {% if post.featured_image %}
      <figure class="mb-4">
        <img class="img-fluid rounded" src="{{ post.featured_image.url }}" alt="{{ post.title }}">
        {% if post.image_caption %}
        <figcaption class="text-center text-muted mt-2">{{ post.image_caption }}</figcaption>
        {% endif %}
      </figure>
      {% endif %}
      
      <!-- Article Content -->
      <div class="blog-content mb-5">
        {% if post.excerpt %}
        <div class="lead mb-4">{{ post.excerpt }}</div>
        {% endif %}
        
        {{ post.content|safe }}
      </div>
      
      <!-- Tags -->
      {% if post.tags.exists %}
      <div class="mb-5">
        <h5>{% trans "Tags" %}:</h5>
        {% for tag in post.tags.all %}
        <a href="{% url 'blog:tag' tag.slug %}" class="badge bg-light text-dark text-decoration-none me-1">{{ tag.name }}</a>
        {% endfor %}
      </div>
      {% endif %}
      
      <!-- Author Bio -->
      {% if post.author %}
      <div class="card mb-5 border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex">
            <div class="flex-shrink-0">
              {% if post.author.profile_picture %}
              <img src="{{ post.author.profile_picture.url }}" class="rounded-circle" width="80" height="80" alt="{{ post.author.get_full_name }}">
              {% else %}
              <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                <i class="bi bi-person-fill" style="font-size: 2rem;"></i>
              </div>
              {% endif %}
            </div>
            <div class="flex-grow-1 ms-3">
              <h5 class="mb-1">{{ post.author.get_full_name }}</h5>
              <p class="text-muted">{{ post.author.bio|default:'' }}</p>
              <div class="d-flex">
                {% if post.author.website %}
                <a href="{{ post.author.website }}" class="btn btn-sm btn-outline-primary me-2" target="_blank"><i class="bi bi-globe"></i></a>
                {% endif %}
                {% if post.author.twitter %}
                <a href="{{ post.author.twitter }}" class="btn btn-sm btn-outline-primary me-2" target="_blank"><i class="bi bi-twitter"></i></a>
                {% endif %}
                {% if post.author.linkedin %}
                <a href="{{ post.author.linkedin }}" class="btn btn-sm btn-outline-primary me-2" target="_blank"><i class="bi bi-linkedin"></i></a>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      {% endif %}
      
      <!-- Comments Section -->
      <div id="comments" class="mb-5">
        <h3 class="mb-4">{% trans "Comments" %} ({{ comments|length }})</h3>
        
        <!-- Comment Form -->
        {% if user.is_authenticated %}
        <div class="card mb-4 border-0 shadow-sm">
          <div class="card-body">
            <form method="post" action="{% url 'blog:add_comment' post.slug %}">
              {% csrf_token %}
              <div class="mb-3">
                <label for="comment" class="form-label">{% trans "Leave a comment" %}</label>
                <textarea class="form-control" id="comment" name="content" rows="3" required></textarea>
              </div>
              <button type="submit" class="btn btn-primary">{% trans "Submit" %}</button>
            </form>
          </div>
        </div>
        {% else %}
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i>
          {% trans "Please" %} <a href="{% url 'account_login' %}?next={{ request.path }}">{% trans "log in" %}</a> {% trans "to leave a comment" %}.
        </div>
        {% endif %}
        
        <!-- Comments List -->
        {% if comments %}
        <div class="comments-list">
          {% for comment in comments %}
          <div class="card mb-3 border-0 shadow-sm">
            <div class="card-body">
              <div class="d-flex">
                <div class="flex-shrink-0">
                  {% if comment.author.profile_picture %}
                  <img src="{{ comment.author.profile_picture.url }}" class="rounded-circle" width="50" height="50" alt="{{ comment.author.get_full_name }}">
                  {% else %}
                  <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                    <i class="bi bi-person-fill"></i>
                  </div>
                  {% endif %}
                </div>
                <div class="flex-grow-1 ms-3">
                  <div class="d-flex justify-content-between align-items-start">
                    <div>
                      <h6 class="mb-0">{{ comment.author.get_full_name }}</h6>
                      <p class="text-muted small mb-2">{{ comment.created_at|date:"F j, Y \a\t g:i a" }}</p>
                    </div>
                    {% if user.is_authenticated and comment.author == user %}
                    <div class="dropdown">
                      <button class="btn btn-sm text-muted" type="button" id="commentDropdown{{ comment.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-three-dots-vertical"></i>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="commentDropdown{{ comment.id }}">
                        <li><a class="dropdown-item" href="{% url 'blog:edit_comment' comment.id %}">{% trans "Edit" %}</a></li>
                        <li><a class="dropdown-item text-danger" href="{% url 'blog:delete_comment' comment.id %}" onclick="return confirm('{% trans "Are you sure you want to delete this comment?" %}');">{% trans "Delete" %}</a></li>
                      </ul>
                    </div>
                    {% endif %}
                  </div>
                  <p class="mb-0">{{ comment.content }}</p>
                </div>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
        {% else %}
        <div class="alert alert-light">
          <i class="bi bi-chat me-2"></i>
          {% trans "No comments yet. Be the first to comment!" %}
        </div>
        {% endif %}
      </div>
      
      <!-- Related Posts -->
      {% if related_posts %}
      <div class="mb-5">
        <h3 class="mb-4">{% trans "Related Posts" %}</h3>
        <div class="row row-cols-1 row-cols-md-2 g-4">
          {% for related in related_posts %}
          <div class="col">
            <div class="card h-100 border-0 shadow-sm">
              {% if related.featured_image %}
              <img src="{{ related.featured_image.url }}" class="card-img-top" alt="{{ related.title }}">
              {% else %}
              <div class="bg-light text-center py-5">
                <i class="bi bi-file-text" style="font-size: 3rem;"></i>
              </div>
              {% endif %}
              <div class="card-body">
                <h5 class="card-title">{{ related.title }}</h5>
                <p class="card-text">{{ related.excerpt|default:related.content|truncatechars:100 }}</p>
                <a href="{% url 'blog:detail' related.slug %}" class="btn btn-outline-primary btn-sm">{% trans "Read More" %}</a>
              </div>
              <div class="card-footer bg-transparent border-top-0">
                <small class="text-muted">{{ related.published_date|date:"F j, Y" }}</small>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
      {% endif %}
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
      <!-- Search Widget -->
      <div class="card mb-4 border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">{% trans "Search" %}</h5>
        </div>
        <div class="card-body">
          <form action="{% url 'blog:search' %}" method="get">
            <div class="input-group">
              <input type="text" class="form-control" name="q" placeholder="{% trans 'Search for...' %}" aria-label="Search" aria-describedby="button-search">
              <button class="btn btn-primary" type="submit" id="button-search"><i class="bi bi-search"></i></button>
            </div>
          </form>
        </div>
      </div>
      
      <!-- Categories Widget -->
      <div class="card mb-4 border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">{% trans "Categories" %}</h5>
        </div>
        <div class="card-body">
          <div class="list-group list-group-flush">
            {% for category in categories %}
            <a href="{% url 'blog:category' category.slug %}" class="list-group-item d-flex justify-content-between align-items-center">
              {{ category.name }}
              <span class="badge bg-primary rounded-pill">{{ category.post_count }}</span>
            </a>
            {% empty %}
            <p class="text-muted mb-0">{% trans "No categories found." %}</p>
            {% endfor %}
          </div>
        </div>
      </div>
      
      <!-- Recent Posts Widget -->
      <div class="card mb-4 border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">{% trans "Recent Posts" %}</h5>
        </div>
        <div class="card-body">
          <ul class="list-unstyled mb-0">
            {% for recent in recent_posts %}
            <li class="media mb-3 pb-3 {% if not forloop.last %}border-bottom{% endif %}">
              <div class="d-flex">
                {% if recent.featured_image %}
                <img src="{{ recent.featured_image.url }}" class="mr-3 rounded" width="70" height="70" alt="{{ recent.title }}" style="object-fit: cover;">
                {% else %}
                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 70px; height: 70px;">
                  <i class="bi bi-file-text"></i>
                </div>
                {% endif %}
                <div class="ms-3">
                  <h6 class="mt-0 mb-1"><a href="{% url 'blog:detail' recent.slug %}" class="text-decoration-none">{{ recent.title }}</a></h6>
                  <small class="text-muted">{{ recent.published_date|date:"F j, Y" }}</small>
                </div>
              </div>
            </li>
            {% empty %}
            <li>{% trans "No recent posts." %}</li>
            {% endfor %}
          </ul>
        </div>
      </div>
      
      <!-- Newsletter Widget -->
      <div class="card mb-4 border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">{% trans "Newsletter" %}</h5>
        </div>
        <div class="card-body">
          <p>{% trans "Subscribe to our newsletter to stay updated with the latest news and articles." %}</p>
          <form action="{% url 'core:newsletter_subscribe' %}" method="post">
            {% csrf_token %}
            <div class="input-group mb-3">
              <input type="email" class="form-control" name="email" placeholder="{% trans 'Your email' %}" aria-label="Email" aria-describedby="button-newsletter">
              <button class="btn btn-primary" type="submit" id="button-newsletter">{% trans "Subscribe" %}</button>
            </div>
            <input type="hidden" name="source" value="blog">
            <input type="hidden" name="next" value="{{ request.path }}">
          </form>
        </div>
      </div>
      
      <!-- Social Media Widget -->
      <div class="card mb-4 border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">{% trans "Follow Us" %}</h5>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <a href="#" class="btn btn-outline-primary" target="_blank"><i class="bi bi-facebook"></i></a>
            <a href="#" class="btn btn-outline-info" target="_blank"><i class="bi bi-twitter"></i></a>
            <a href="#" class="btn btn-outline-danger" target="_blank"><i class="bi bi-instagram"></i></a>
            <a href="#" class="btn btn-outline-secondary" target="_blank"><i class="bi bi-linkedin"></i></a>
            <a href="#" class="btn btn-outline-success" target="_blank"><i class="bi bi-whatsapp"></i></a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  // Optional: Add JavaScript for enhanced comments functionality
  // For example, to handle comment editing in-place
</script>
{% endblock %} 