{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Blog" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <!-- Main Content - Blog Posts -->
    <div class="col-lg-8">
      <h1 class="display-4 mb-4">{% trans "Blog" %}</h1>
      
      <!-- Search Bar -->
      <div class="mb-4">
        <form action="{% url 'blog:list' %}" method="get" class="d-flex">
          <input type="text" name="query" value="{{ query }}" class="form-control" placeholder="{% trans 'Search blog posts...' %}">
          <button type="submit" class="btn btn-primary ms-2">
            <i class="bi bi-search"></i>
          </button>
        </form>
      </div>
      
      <!-- Blog Posts -->
      {% if posts %}
        <div class="row g-4">
          {% for post in posts %}
            <div class="col-md-6">
              <div class="card h-100 border-0 shadow-sm">
                {% if post.featured_image %}
                  <img src="{{ post.featured_image.url }}" class="card-img-top" alt="{{ post.title }}">
                {% else %}
                  <img src="{% static 'images/default-blog.jpg' %}" class="card-img-top" alt="{{ post.title }}">
                {% endif %}
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="badge bg-primary">{{ post.category.name }}</span>
                    <small class="text-muted">{{ post.published_at|date:"M d, Y" }}</small>
                  </div>
                  <h5 class="card-title">{{ post.title }}</h5>
                  {% if post.excerpt %}
                    <p class="card-text">{{ post.excerpt }}</p>
                  {% else %}
                    <p class="card-text">{{ post.content|truncatewords:25 }}</p>
                  {% endif %}
                  <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                      <small class="text-muted">
                        <i class="bi bi-person me-1"></i> {{ post.author.get_full_name|default:post.author.username }}
                      </small>
                    </div>
                    <a href="{% url 'blog:detail' post_slug=post.slug %}" class="btn btn-outline-primary btn-sm">Read More</a>
                  </div>
                </div>
              </div>
            </div>
          {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if posts.has_other_pages %}
          <nav aria-label="Page navigation" class="mt-5">
            <ul class="pagination justify-content-center">
              {% if posts.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ posts.previous_page_number }}{% if query %}&query={{ query }}{% endif %}{% if category_slug %}&category={{ category_slug }}{% endif %}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% endif %}
              
              {% for i in posts.paginator.page_range %}
                {% if posts.number == i %}
                  <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                {% else %}
                  <li class="page-item"><a class="page-link" href="?page={{ i }}{% if query %}&query={{ query }}{% endif %}{% if category_slug %}&category={{ category_slug }}{% endif %}">{{ i }}</a></li>
                {% endif %}
              {% endfor %}
              
              {% if posts.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ posts.next_page_number }}{% if query %}&query={{ query }}{% endif %}{% if category_slug %}&category={{ category_slug }}{% endif %}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
              {% endif %}
            </ul>
          </nav>
        {% endif %}
      {% else %}
        <div class="card shadow-sm border-0">
          <div class="card-body text-center py-5">
            <i class="bi bi-journal-x display-1 text-muted"></i>
            <h3 class="mt-3">No blog posts found</h3>
            {% if query %}
              <p class="text-muted">No results for "{{ query }}". Try a different search term.</p>
              <a href="{% url 'blog:list' %}" class="btn btn-outline-primary mt-3">View All Posts</a>
            {% elif category_slug %}
              <p class="text-muted">No posts in this category yet.</p>
              <a href="{% url 'blog:list' %}" class="btn btn-outline-primary mt-3">View All Posts</a>
            {% else %}
              <p class="text-muted">Check back soon for new content!</p>
            {% endif %}
          </div>
        </div>
      {% endif %}
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4 mt-5 mt-lg-0">
      <!-- Categories -->
      <div class="card shadow-sm border-0 mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Categories</h5>
        </div>
        <div class="card-body">
          <div class="list-group list-group-flush">
            <a href="{% url 'blog:list' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if not category_slug %}active{% endif %}">
              All Categories
              <span class="badge bg-primary rounded-pill">{{ posts.paginator.count }}</span>
            </a>
            {% for category in categories %}
              <a href="{% url 'blog:by_category' category_slug=category.slug %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if category_slug == category.slug %}active{% endif %}">
                {{ category.name }}
                <span class="badge bg-primary rounded-pill">{{ category.posts.count }}</span>
              </a>
            {% endfor %}
          </div>
        </div>
      </div>
      
      <!-- Recent Posts -->
      <div class="card shadow-sm border-0">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Recent Posts</h5>
        </div>
        <div class="card-body p-0">
          <ul class="list-group list-group-flush">
            {% for post in posts|slice:":5" %}
              <li class="list-group-item">
                <div class="d-flex">
                  {% if post.featured_image %}
                    <img src="{{ post.featured_image.url }}" alt="{{ post.title }}" class="img-fluid rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                  {% else %}
                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                      <i class="bi bi-file-earmark-text text-muted"></i>
                    </div>
                  {% endif %}
                  <div>
                    <a href="{% url 'blog:detail' post_slug=post.slug %}" class="text-decoration-none">
                      <h6 class="mb-1">{{ post.title|truncatechars:50 }}</h6>
                    </a>
                    <small class="text-muted">{{ post.published_at|date:"M d, Y" }}</small>
                  </div>
                </div>
              </li>
            {% empty %}
              <li class="list-group-item text-center py-4">
                <i class="bi bi-journal text-muted"></i>
                <p class="mb-0 mt-2">No recent posts</p>
              </li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} 