{% extends 'common/base.html' %}

{% block title %}Smarch - About Us{% endblock %}

{% block content %}
<div class="container mb-5">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4 text-center">About Smarch</h1>

            <!-- Info Section -->
            <div class="card mb-5">
                <div class="card-body">
                    <h2 class="card-title">Our Story</h2>
                    <p class="card-text">
                        Smarch was founded with a vision to revolutionize how applicants and service providers connect in the digital age.
                        We identified a gap in the market where traditional methods were failing to create meaningful matches between talented
                        individuals and the companies that need their skills.
                    </p>
                    <p class="card-text">
                        Our platform leverages advanced algorithms and human expertise to ensure the best possible outcomes for both parties.
                        We believe in creating a fair, transparent, and efficient ecosystem where merit is recognized and opportunities are accessible.
                    </p>
                </div>
            </div>

            <!-- Mission/Goals Section -->
            <div class="row mb-5">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h2 class="card-title">Our Mission</h2>
                            <p class="card-text">
                                To create a platform that democratizes access to opportunities by connecting the right people with the right
                                organizations based on merit, compatibility, and potential rather than traditional credentials alone.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h2 class="card-title">Our Goals</h2>
                            <ul class="card-text">
                                <li>Empower applicants to showcase their unique skills and potential</li>
                                <li>Help service providers find the perfect match for their needs</li>
                                <li>Reduce bias in the selection process</li>
                                <li>Create a community of growth and mutual benefit</li>
                                <li>Continuously improve our matching algorithms through data-driven insights</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Values Section -->
            <div class="card mb-5">
                <div class="card-body">
                    <h2 class="card-title">Our Values</h2>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h3 class="card-title">Transparency</h3>
                                    <p class="card-text">
                                        We believe in clear, honest communication with all stakeholders.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h3 class="card-title">Innovation</h3>
                                    <p class="card-text">
                                        We constantly seek better ways to connect people and organizations.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h3 class="card-title">Fairness</h3>
                                    <p class="card-text">
                                        We ensure equal opportunities and unbiased matching.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Collaboration Form -->
            <div class="card" id="collaboration-form">
                <div class="card-body">
                    <h2 class="card-title">Work With Us</h2>
                    <p class="card-text">
                        Interested in collaborating with Smarch? Fill out the form below and we'll get in touch with you.
                    </p>
                    <form id="contact-form" method="post" action="#">
                        {% csrf_token %}
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="company" class="form-label">Company</label>
                                <input type="text" class="form-control" id="company" name="company">
                            </div>
                            <div class="col-md-6">
                                <label for="inquiry_type" class="form-label">Inquiry Type</label>
                                <select class="form-select" id="inquiry_type" name="inquiry_type" required>
                                    <option value="">Select an option</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="partnership">Partnership Opportunity</option>
                                    <option value="enterprise">Enterprise Plan</option>
                                    <option value="support">Technical Support</option>
                                    <option value="feedback">Feedback</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">Message</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="privacy_policy" name="privacy_policy" required>
                            <label class="form-check-label" for="privacy_policy">I agree to the privacy policy and terms of service</label>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if URL has the enterprise parameter
        const urlParams = new URLSearchParams(window.location.search);
        const inquiryType = urlParams.get('inquiry_type');

        if (inquiryType) {
            // Set the inquiry type dropdown value
            const inquiryTypeSelect = document.getElementById('inquiry_type');
            if (inquiryTypeSelect) {
                inquiryTypeSelect.value = inquiryType;
            }

            // Scroll to the collaboration form
            const collaborationForm = document.getElementById('collaboration-form');
            if (collaborationForm) {
                collaborationForm.scrollIntoView({ behavior: 'smooth' });
            }

            // Add a message if it's an enterprise inquiry
            if (inquiryType === 'enterprise') {
                const messageField = document.getElementById('message');
                if (messageField && !messageField.value) {
                    messageField.value = 'I am interested in learning more about the Enterprise Plan for hiring partners.';
                }
            }
        }
    });
</script>
{% endblock %}