<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Smarch{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold d-flex align-items-center" href="{% url 'core:home' %}">
                <i class="bi bi-briefcase-fill me-2"></i>
                Smarch
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="{% url 'core:home' %}">
                            <i class="bi bi-house-door me-1"></i> Home
                        </a>
                    </li>
                    {% if user.is_authenticated %}
                        {% if user.is_superuser or user.user_type == 'applicant' or not user.user_type %}
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="{% url 'application:index' %}">
                                <i class="bi bi-file-earmark-text me-1"></i> Application
                            </a>
                        </li>
                        {% endif %}
                        {% if user.is_superuser or user.user_type == 'service' %}
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="{% url 'services:index' %}">
                                <i class="bi bi-gear me-1"></i> Services
                            </a>
                        </li>
                        {% endif %}
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="{% url 'application:index' %}">
                                <i class="bi bi-file-earmark-text me-1"></i> Application
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="{% url 'services:index' %}">
                                <i class="bi bi-gear me-1"></i> Services
                            </a>
                        </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="{% url 'blog:index' %}">
                            <i class="bi bi-newspaper me-1"></i> Blog
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="{% url 'core:about' %}">
                            <i class="bi bi-info-circle me-1"></i> About
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <!-- Messages Dropdown -->
                        <li class="nav-item dropdown me-2">
                            <a class="nav-link position-relative" href="#" id="messagesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-envelope-fill fs-5"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    {% with unread_count=user.conversations.all|length %}
                                    {{ unread_count|default:"0" }}
                                    {% endwith %}
                                    <span class="visually-hidden">unread messages</span>
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="messagesDropdown" style="width: 300px;">
                                <li><h6 class="dropdown-header">Messages</h6></li>
                                {% with conversations=user.conversations.all|slice:":5" %}
                                    {% if conversations %}
                                        {% for conversation in conversations %}
                                            <li>
                                                <a class="dropdown-item d-flex align-items-center py-2" href="{% url 'messaging:conversation_detail' conversation.id %}">
                                                    <div class="flex-shrink-0">
                                                        {% with other_user=conversation.get_other_participant %}
                                                            {% if other_user.user_type == 'applicant' and other_user.applicant_profile.image %}
                                                                <img src="{{ other_user.applicant_profile.image.url }}" alt="{{ other_user.get_full_name }}" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                                            {% elif other_user.user_type == 'service' and other_user.hiring_partner_profile.profile_image %}
                                                                <img src="{{ other_user.hiring_partner_profile.profile_image.url }}" alt="{{ other_user.get_full_name }}" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                                            {% else %}
                                                                <img src="{% static 'images/default-avatar.png' %}" alt="{{ other_user.get_full_name }}" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                                            {% endif %}
                                                        {% endwith %}
                                                    </div>
                                                    <div class="ms-3">
                                                        <p class="mb-0 fw-bold">{{ conversation.get_other_participant.username }}</p>
                                                        <p class="mb-0 text-muted small text-truncate" style="max-width: 200px;">
                                                            {% with last_message=conversation.messages.last %}
                                                                {% if last_message %}
                                                                    {{ last_message.content|truncatechars:30 }}
                                                                {% else %}
                                                                    No messages yet
                                                                {% endif %}
                                                            {% endwith %}
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                        {% endfor %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-center" href="{% url 'messaging:inbox' %}">View All Messages</a></li>
                                    {% else %}
                                        <li><a class="dropdown-item text-center py-3" href="#">No messages yet</a></li>
                                    {% endif %}
                                {% endwith %}
                            </ul>
                        </li>

                        <!-- Notifications Dropdown -->
                        <li class="nav-item dropdown me-2">
                            <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-bell-fill fs-5"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    {% with notification_count=user.notifications.all|length %}
                                    {{ notification_count|default:"0" }}
                                    {% endwith %}
                                    <span class="visually-hidden">unread notifications</span>
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown" style="width: 300px;">
                                <li>
                                    <div class="d-flex justify-content-between align-items-center px-3 py-2">
                                        <h6 class="dropdown-header p-0 m-0">Notifications</h6>
                                        <a href="{% url 'notifications:mark_all_as_read' %}" class="text-decoration-none small">Mark all as read</a>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider m-0"></li>
                                {% with notifications=user.notifications.all|slice:":5" %}
                                    {% if notifications %}
                                        {% for notification in notifications %}
                                            <li>
                                                <a class="dropdown-item d-flex py-2 {% if not notification.is_read %}bg-light{% endif %}" href="{% url 'notifications:mark_as_read' notification.id %}">
                                                    <div class="flex-shrink-0">
                                                        {% if notification.notification_type == 'message' %}
                                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                <i class="bi bi-envelope"></i>
                                                            </div>
                                                        {% elif notification.notification_type == 'contact_request' %}
                                                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                <i class="bi bi-person-plus"></i>
                                                            </div>
                                                        {% else %}
                                                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                <i class="bi bi-info-circle"></i>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                    <div class="ms-3">
                                                        <p class="mb-0 fw-bold">{{ notification.title }}</p>
                                                        <p class="mb-0 text-muted small">{{ notification.message|truncatechars:30 }}</p>
                                                        <p class="mb-0 text-muted small">{{ notification.created_at|timesince }} ago</p>
                                                    </div>
                                                </a>
                                            </li>
                                        {% endfor %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-center" href="#">View All Notifications</a></li>
                                    {% else %}
                                        <li><a class="dropdown-item text-center py-3" href="#">No notifications yet</a></li>
                                    {% endif %}
                                {% endwith %}
                            </ul>
                        </li>

                        {% if user.user_type == 'service' %}
                        <li class="nav-item dropdown me-2">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="servicesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-building me-1"></i> Hiring Partner
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="servicesDropdown">
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'services:profile' %}">
                                    <i class="bi bi-person-badge me-2"></i> Profile
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'contact:requests' %}">
                                    <i class="bi bi-envelope-paper me-2"></i> Contact Requests
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'services:browser' %}">
                                    <i class="bi bi-search me-2"></i> Browser
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'services:account' %}">
                                    <i class="bi bi-gear me-2"></i> Account Settings
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'insights:index' %}">
                                    <i class="bi bi-graph-up me-2"></i> Smarch Insights
                                </a></li>
                            </ul>
                        </li>
                        {% endif %}

                        {% if user.is_staff %}
                        <li class="nav-item dropdown me-2">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="staffDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-shield-check me-1"></i> Staff
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="staffDropdown">
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'staff:applicant_list' %}">
                                    <i class="bi bi-people me-2"></i> Applicants
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'staff:service_user_list' %}">
                                    <i class="bi bi-building me-2"></i> Hiring Partners
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'staff:smarch_panel' %}">
                                    <i class="bi bi-speedometer2 me-2"></i> Smarch Panel
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'contact:messages' %}">
                                    <i class="bi bi-chat-left-text me-2"></i> Contact Messages
                                </a></li>
                            </ul>
                        </li>
                        {% endif %}

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                {% if user.user_type == 'applicant' and user.applicant_profile.image %}
                                    <img src="{{ user.applicant_profile.image.url }}" alt="{{ user.get_full_name }}" class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;">
                                {% elif user.user_type == 'service' and user.hiring_partner_profile.profile_image %}
                                    <img src="{{ user.hiring_partner_profile.profile_image.url }}" alt="{{ user.get_full_name }}" class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;">
                                {% else %}
                                    <img src="{% static 'images/default-avatar.png' %}" alt="{{ user.get_full_name }}" class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;">
                                {% endif %}
                                <span>{{ user.username }}</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'users:profile' %}">
                                    <i class="bi bi-person me-2"></i> Profile
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'users:dashboard' %}">
                                    <i class="bi bi-speedometer me-2"></i> Dashboard
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'users:settings' %}">
                                    <i class="bi bi-gear me-2"></i> Settings
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'notifications:preferences' %}">
                                    <i class="bi bi-bell me-2"></i> Notification Preferences
                                </a></li>

                                {% if user.is_superuser %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'admin:index' %}">
                                    <i class="bi bi-database-gear me-2"></i> Django Admin
                                </a></li>
                                {% endif %}

                                {% if user.user_type == 'applicant' and not user.hiring_partner_profile %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'services:register' %}">
                                    <i class="bi bi-building-add me-2"></i> Register as Hiring Partner
                                </a></li>
                                {% endif %}

                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="{% url 'account_logout' %}">
                                    <i class="bi bi-box-arrow-right me-2"></i> Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item me-2">
                            <a class="nav-link d-flex align-items-center" href="{% url 'account_login' %}">
                                <i class="bi bi-box-arrow-in-right me-1"></i> Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="{% url 'account_signup' %}">
                                <i class="bi bi-person-plus me-1"></i> Sign Up
                            </a>
                        </li>
                    {% endif %}

                    <!-- Dark Mode Toggle -->
                    <li class="nav-item ms-2">
                        <div class="dark-mode-toggle nav-link d-flex align-items-center" id="darkModeToggle">
                            <i class="bi bi-sun-fill" id="lightModeIcon"></i>
                            <i class="bi bi-moon-fill d-none" id="darkModeIcon"></i>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
            <!-- Disable auto-dismissing alerts -->
            <script>
                // Remove the auto-dismissing behavior for alerts
                document.addEventListener('DOMContentLoaded', function() {
                    var alerts = document.querySelectorAll('.alert');
                    alerts.forEach(function(alert) {
                        // Prevent Bootstrap from auto-dismissing the alert
                        var bsAlert = new bootstrap.Alert(alert);
                        // Only dismiss when the close button is clicked
                    });
                });
            </script>
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto py-5 bg-dark text-white">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="mb-4">
                        <h5 class="text-uppercase fw-bold mb-4">
                            <i class="bi bi-briefcase-fill me-2"></i>
                            Smarch
                        </h5>
                        <p class="mb-3">Connecting talented applicants with the best hiring partners worldwide. Our platform makes job matching simple, efficient, and effective.</p>
                        <div class="d-flex gap-3 mt-4">
                            <a href="#" class="text-white fs-5"><i class="bi bi-facebook"></i></a>
                            <a href="#" class="text-white fs-5"><i class="bi bi-twitter-x"></i></a>
                            <a href="#" class="text-white fs-5"><i class="bi bi-linkedin"></i></a>
                            <a href="#" class="text-white fs-5"><i class="bi bi-instagram"></i></a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <h5 class="text-uppercase fw-bold mb-4">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="{% url 'core:home' %}" class="text-white text-decoration-none">
                                <i class="bi bi-chevron-right me-1 small"></i> Home
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{% url 'application:index' %}" class="text-white text-decoration-none">
                                <i class="bi bi-chevron-right me-1 small"></i> Application
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{% url 'services:index' %}" class="text-white text-decoration-none">
                                <i class="bi bi-chevron-right me-1 small"></i> Services
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{% url 'blog:index' %}" class="text-white text-decoration-none">
                                <i class="bi bi-chevron-right me-1 small"></i> Blog
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{% url 'core:about' %}" class="text-white text-decoration-none">
                                <i class="bi bi-chevron-right me-1 small"></i> About
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6">
                    <h5 class="text-uppercase fw-bold mb-4">Resources</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="#" class="text-white text-decoration-none">
                                <i class="bi bi-chevron-right me-1 small"></i> Help Center
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="#" class="text-white text-decoration-none">
                                <i class="bi bi-chevron-right me-1 small"></i> Privacy Policy
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="#" class="text-white text-decoration-none">
                                <i class="bi bi-chevron-right me-1 small"></i> Terms of Service
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{% url 'contact:contact_form' %}" class="text-white text-decoration-none">
                                <i class="bi bi-chevron-right me-1 small"></i> Contact Us
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="#" class="text-white text-decoration-none">
                                <i class="bi bi-chevron-right me-1 small"></i> FAQ
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6">
                    <h5 class="text-uppercase fw-bold mb-4">Contact</h5>
                    <p class="mb-2">
                        <i class="bi bi-geo-alt-fill me-2"></i>
                        123 Smarch Street, Suite 456<br>
                        San Francisco, CA 94107
                    </p>
                    <p class="mb-2">
                        <i class="bi bi-envelope-fill me-2"></i>
                        <a href="mailto:<EMAIL>" class="text-white text-decoration-none"><EMAIL></a>
                    </p>
                    <p class="mb-2">
                        <i class="bi bi-telephone-fill me-2"></i>
                        <a href="tel:+11234567890" class="text-white text-decoration-none">+****************</a>
                    </p>
                </div>
            </div>

            <hr class="my-4 bg-light">

            <div class="d-flex flex-column flex-md-row justify-content-between align-items-center">
                <p class="mb-md-0">&copy; {% now "Y" %} Smarch. All rights reserved.</p>
                <div>
                    <a href="#" class="text-white text-decoration-none me-3">Privacy Policy</a>
                    <a href="#" class="text-white text-decoration-none me-3">Terms of Service</a>
                    <a href="#" class="text-white text-decoration-none">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>