{% extends 'common/base.html' %}
{% load i18n %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Contact Us" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h1 class="h3 mb-0">{% trans "Contact Us" %}</h1>
        </div>
        <div class="card-body">
          <p class="lead mb-4">{% trans "Have questions or feedback? We'd love to hear from you. Fill out the form below and our team will get back to you as soon as possible." %}</p>

          <form method="post" action="{% url 'contact:contact_form' %}">
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
              {% for error in form.non_field_errors %}
                {{ error }}
              {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3">
              <label for="{{ form.name.id_for_label }}" class="form-label">{% trans "Your Name" %}</label>
              {{ form.name }}
              {% if form.name.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.name.errors %}
                  {{ error }}
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-3">
              <label for="{{ form.email.id_for_label }}" class="form-label">{% trans "Email Address" %}</label>
              {{ form.email }}
              {% if form.email.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.email.errors %}
                  {{ error }}
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-3">
              <label for="{{ form.subject.id_for_label }}" class="form-label">{% trans "Subject" %}</label>
              {{ form.subject }}
              {% if form.subject.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.subject.errors %}
                  {{ error }}
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-3">
              <label for="{{ form.message.id_for_label }}" class="form-label">{% trans "Message" %}</label>
              {{ form.message }}
              {% if form.message.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.message.errors %}
                  {{ error }}
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary">{% trans "Send Message" %}</button>
            </div>
          </form>

          <div class="mt-5">
            <h4>{% trans "Other Ways to Reach Us" %}</h4>
            <div class="row mt-3">
              <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                  <i class="bi bi-envelope-fill fs-4 me-3 text-primary"></i>
                  <div>
                    <h5 class="mb-0">{% trans "Email" %}</h5>
                    <p class="mb-0"><EMAIL></p>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                  <i class="bi bi-telephone-fill fs-4 me-3 text-primary"></i>
                  <div>
                    <h5 class="mb-0">{% trans "Phone" %}</h5>
                    <p class="mb-0">+****************</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
