{% extends 'common/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Contact Messages" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <div class="col-12">
      <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h1 class="h3 mb-0">{% trans "Contact Messages" %}</h1>
          <div>
            <a href="{% url 'staff_management:dashboard' %}" class="btn btn-light btn-sm">
              <i class="bi bi-arrow-left"></i> {% trans "Back to Dashboard" %}
            </a>
          </div>
        </div>
        <div class="card-body">
          {% if messages %}
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>{% trans "Name" %}</th>
                    <th>{% trans "Email" %}</th>
                    <th>{% trans "Subject" %}</th>
                    <th>{% trans "Date" %}</th>
                    <th>{% trans "Status" %}</th>
                    <th>{% trans "Actions" %}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for message in messages %}
                    <tr class="{% if not message.is_read %}table-primary{% endif %}">
                      <td>{{ message.name }}</td>
                      <td><a href="mailto:{{ message.email }}">{{ message.email }}</a></td>
                      <td>{{ message.subject }}</td>
                      <td>{{ message.created_at|date:"M d, Y" }}</td>
                      <td>
                        {% if message.is_read %}
                          <span class="badge bg-success">{% trans "Read" %}</span>
                        {% else %}
                          <span class="badge bg-warning text-dark">{% trans "Unread" %}</span>
                        {% endif %}
                        
                        {% if message.is_archived %}
                          <span class="badge bg-secondary">{% trans "Archived" %}</span>
                        {% endif %}
                      </td>
                      <td>
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#messageModal{{ message.id }}">
                          <i class="bi bi-eye"></i> {% trans "View" %}
                        </button>
                      </td>
                    </tr>
                    
                    <!-- Message Modal -->
                    <div class="modal fade" id="messageModal{{ message.id }}" tabindex="-1" aria-labelledby="messageModalLabel{{ message.id }}" aria-hidden="true">
                      <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                          <div class="modal-header">
                            <h5 class="modal-title" id="messageModalLabel{{ message.id }}">{{ message.subject }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                          </div>
                          <div class="modal-body">
                            <div class="mb-3">
                              <strong>{% trans "From:" %}</strong> {{ message.name }} &lt;{{ message.email }}&gt;
                            </div>
                            <div class="mb-3">
                              <strong>{% trans "Date:" %}</strong> {{ message.created_at|date:"F d, Y H:i" }}
                            </div>
                            <div class="mb-3">
                              <strong>{% trans "Message:" %}</strong>
                              <div class="p-3 bg-light rounded mt-2">
                                {{ message.message|linebreaks }}
                              </div>
                            </div>
                          </div>
                          <div class="modal-footer">
                            <a href="mailto:{{ message.email }}?subject=Re: {{ message.subject }}" class="btn btn-primary">
                              <i class="bi bi-reply"></i> {% trans "Reply" %}
                            </a>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="alert alert-info">
              <i class="bi bi-info-circle"></i> {% trans "No contact messages found." %}
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
