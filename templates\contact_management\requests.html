{% extends 'common/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Contact Requests" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <div class="col-12">
      <h1 class="mb-4">{% trans "Contact Requests" %}</h1>
      
      {% if requests %}
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th>{% trans "Applicant" %}</th>
                <th>{% trans "Message" %}</th>
                <th>{% trans "Date" %}</th>
                <th>{% trans "Status" %}</th>
                <th>{% trans "Actions" %}</th>
              </tr>
            </thead>
            <tbody>
              {% for request in requests %}
                <tr>
                  <td>{{ request.recipient.user.get_full_name }}</td>
                  <td>{{ request.message|truncatechars:50 }}</td>
                  <td>{{ request.sent_at|date:"M d, Y" }}</td>
                  <td>
                    <span class="badge {% if request.status == 'pending' %}bg-warning{% elif request.status == 'accepted' %}bg-success{% else %}bg-danger{% endif %}">
                      {{ request.status|title }}
                    </span>
                  </td>
                  <td>
                    <a href="{% url 'services:contact_request_detail' request.id %}" class="btn btn-sm btn-primary">{% trans "View" %}</a>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      {% else %}
        <div class="alert alert-info">
          <h4 class="alert-heading">{% trans "No Contact Requests" %}</h4>
          <p>{% trans "You haven't sent any contact requests to applicants yet." %}</p>
          <hr>
          <p class="mb-0">{% trans "Browse applicants and send contact requests to connect with potential candidates." %}</p>
        </div>
        
        <div class="text-center mt-4">
          <a href="{% url 'services:applicants' %}" class="btn btn-primary">{% trans "Browse Applicants" %}</a>
        </div>
      {% endif %}
      
      <div class="mt-5">
        <a href="{% url 'services:dashboard' %}" class="btn btn-outline-primary">{% trans "Back to Dashboard" %}</a>
      </div>
    </div>
  </div>
</div>
{% endblock %}
