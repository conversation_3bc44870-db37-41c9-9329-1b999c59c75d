{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Home" %} | Smarch{% endblock %}

{% block content %}
<!-- Hero Carousel Section -->
<div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
  <div class="carousel-indicators">
    {% for item in carousel_items %}
      <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="{{ forloop.counter0 }}"
        {% if forloop.first %}class="active" aria-current="true"{% endif %}
        aria-label="Slide {{ forloop.counter }}"></button>
    {% endfor %}
  </div>

  <div class="carousel-inner">
    {% for item in carousel_items %}
      <div class="carousel-item {% if forloop.first %}active{% endif %}">
        <img src="{{ item.image.url }}" class="d-block w-100" alt="{{ item.title }}">
        <div class="carousel-caption d-none d-md-block">
          <h2>{{ item.title }}</h2>
          <p>{{ item.description }}</p>
          {% if item.url %}
            <a href="{{ item.url }}" class="btn btn-primary">Learn More</a>
          {% endif %}
        </div>
      </div>
    {% empty %}
      <div class="carousel-item active">
        <img src="{% static 'images/default-banner.jpg' %}" class="d-block w-100" alt="Welcome to Smarch">
        <div class="carousel-caption d-none d-md-block">
          <h2>Welcome to Smarch</h2>
          <p>Your one-stop platform for job matching and services.</p>
        </div>
      </div>
    {% endfor %}
  </div>

  <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
    <span class="visually-hidden">Previous</span>
  </button>
  <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
    <span class="carousel-control-next-icon" aria-hidden="true"></span>
    <span class="visually-hidden">Next</span>
  </button>
</div>

<!-- Main Content Sections -->
<div class="container py-5">
  <!-- Services Overview -->
  <section class="mb-5">
    <div class="row">
      <div class="col-12 text-center mb-4 animate-on-scroll fade-in-element">
        <h2 class="display-4 fw-bold">Our Services</h2>
        <p class="lead">Connecting talent with opportunities worldwide</p>
      </div>
    </div>

    <div class="row g-4">
      <div class="col-md-4 animate-on-scroll slide-up-element" style="transition-delay: 0.1s;">
        <div class="card h-100 border-0 shadow-sm hover-lift">
          <div class="card-body text-center p-4">
            <div class="rounded-circle bg-primary text-white d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
              <i class="bi bi-person-check-fill fs-2"></i>
            </div>
            <h3 class="fw-bold mb-3">For Applicants</h3>
            <p class="mb-4">Register as an applicant and find the perfect job match for your skills and experience.</p>
            <a href="{% url 'application:apply' %}" class="btn btn-primary">
              <i class="bi bi-arrow-right-circle me-2"></i>Apply Now
            </a>
          </div>
        </div>
      </div>

      <div class="col-md-4 animate-on-scroll slide-up-element" style="transition-delay: 0.3s;">
        <div class="card h-100 border-0 shadow-sm hover-lift">
          <div class="card-body text-center p-4">
            <div class="rounded-circle bg-success text-white d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
              <i class="bi bi-building fs-2"></i>
            </div>
            <h3 class="fw-bold mb-3">For Companies</h3>
            <p class="mb-4">Register as a hiring partner and gain access to a pool of talented applicants.</p>
            <a href="{% url 'services:list' %}" class="btn btn-success">
              <i class="bi bi-arrow-right-circle me-2"></i>View Services
            </a>
          </div>
        </div>
      </div>

      <div class="col-md-4 animate-on-scroll slide-up-element" style="transition-delay: 0.5s;">
        <div class="card h-100 border-0 shadow-sm hover-lift">
          <div class="card-body text-center p-4">
            <div class="rounded-circle bg-info text-white d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
              <i class="bi bi-newspaper fs-2"></i>
            </div>
            <h3 class="fw-bold mb-3">Resources</h3>
            <p class="mb-4">Explore our blog and resources to help you navigate the job market successfully.</p>
            <a href="{% url 'blog:list' %}" class="btn btn-info text-white">
              <i class="bi bi-arrow-right-circle me-2"></i>Read Blog
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Upcoming Events -->
  {% if upcoming_events %}
  <section class="mb-5">
    <div class="row">
      <div class="col-12 mb-4">
        <h2 class="text-center">Upcoming Events</h2>
      </div>
    </div>

    <div class="row g-4">
      {% for event in upcoming_events %}
      <div class="col-md-4">
        <div class="card h-100 border-0 shadow-sm">
          {% if event.image %}
          <img src="{{ event.image.url }}" class="card-img-top" alt="{{ event.title }}">
          {% endif %}
          <div class="card-body">
            <h5 class="card-title">{{ event.title }}</h5>
            <p class="card-text small text-muted">{{ event.event_date|date:"F d, Y" }}</p>
            <p class="card-text">{{ event.description|truncatewords:20 }}</p>
            {% if event.link %}
            <a href="{{ event.link }}" class="btn btn-sm btn-outline-primary">Learn More</a>
            {% endif %}
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
  </section>
  {% endif %}

  <!-- Call to Action -->
  <section class="text-center py-5 bg-primary text-white rounded-4 mt-5 animate-on-scroll fade-in-element">
    <div class="py-5 px-4">
      <h2 class="display-4 fw-bold mb-4">Ready to get started?</h2>
      <p class="lead mb-4 px-md-5 mx-md-5">Join our platform today and connect with opportunities worldwide. Whether you're an applicant or a hiring partner, Smarch is here to help you succeed.</p>
      <div class="d-grid gap-3 d-sm-flex justify-content-sm-center mt-4">
        <a href="{% url 'account_signup' %}" class="btn btn-light btn-lg px-5 py-3 fw-bold">
          <i class="bi bi-person-plus me-2"></i>Sign Up
        </a>
        <a href="{% url 'account_login' %}" class="btn btn-outline-light btn-lg px-5 py-3">
          <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
        </a>
      </div>
    </div>
  </section>
</div>
{% endblock %}