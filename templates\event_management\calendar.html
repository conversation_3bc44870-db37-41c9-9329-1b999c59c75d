{% extends "common/base.html" %}
{% load static %}

{% block title %}Calendar - Smarch{% endblock %}

{% block extra_css %}
<style>
.calendar-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.calendar-nav {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.calendar-grid {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.event-card {
    border-left: 4px solid #007bff;
    background: #f8f9fa;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
}

.event-card:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.event-time {
    font-weight: 600;
    color: #007bff;
}

.event-title {
    font-weight: 500;
    margin: 0.25rem 0;
}

.event-meta {
    font-size: 0.875rem;
    color: #6c757d;
}

.view-toggle {
    border-radius: 25px;
    padding: 0.5rem 1rem;
    border: 2px solid #007bff;
    background: white;
    color: #007bff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.view-toggle.active {
    background: #007bff;
    color: white;
}

.view-toggle:hover {
    background: #007bff;
    color: white;
    text-decoration: none;
}

.upcoming-events {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.meeting-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

@media (max-width: 768px) {
    .calendar-header {
        padding: 1rem 0;
    }
    
    .calendar-nav {
        padding: 0.75rem;
    }
    
    .calendar-grid {
        padding: 1rem;
    }
    
    .view-toggle {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="calendar-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="bi bi-calendar3 me-2"></i>
                    Calendar
                </h1>
                <p class="mb-0 mt-2 opacity-75">{{ date_range }}</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'event_management:create_event' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-lg me-2"></i>
                    Schedule Event
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Calendar Navigation -->
    <div class="calendar-nav">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="btn-group" role="group">
                    <a href="?view=day&date={{ current_date|date:'Y-m-d' }}" 
                       class="view-toggle {% if view_type == 'day' %}active{% endif %}">
                        Day
                    </a>
                    <a href="?view=week&date={{ current_date|date:'Y-m-d' }}" 
                       class="view-toggle {% if view_type == 'week' %}active{% endif %}">
                        Week
                    </a>
                    <a href="?view=month&date={{ current_date|date:'Y-m-d' }}" 
                       class="view-toggle {% if view_type == 'month' %}active{% endif %}">
                        Month
                    </a>
                </div>
            </div>
            <div class="col-md-6 text-md-end mt-3 mt-md-0">
                <div class="btn-group" role="group">
                    {% if view_type == 'day' %}
                        <a href="?view=day&date={{ current_date|date:'Y-m-d'|add_days:-1 }}" class="btn btn-outline-primary">
                            <i class="bi bi-chevron-left"></i> Previous
                        </a>
                        <a href="?view=day&date={{ current_date|date:'Y-m-d'|add_days:1 }}" class="btn btn-outline-primary">
                            Next <i class="bi bi-chevron-right"></i>
                        </a>
                    {% elif view_type == 'week' %}
                        <a href="?view=week&date={{ week_start|date:'Y-m-d'|add_days:-7 }}" class="btn btn-outline-primary">
                            <i class="bi bi-chevron-left"></i> Previous
                        </a>
                        <a href="?view=week&date={{ week_end|date:'Y-m-d'|add_days:1 }}" class="btn btn-outline-primary">
                            Next <i class="bi bi-chevron-right"></i>
                        </a>
                    {% else %}
                        <a href="?view=month&date={{ month_start|date:'Y-m-d'|add_days:-1 }}" class="btn btn-outline-primary">
                            <i class="bi bi-chevron-left"></i> Previous
                        </a>
                        <a href="?view=month&date={{ month_end|date:'Y-m-d'|add_days:1 }}" class="btn btn-outline-primary">
                            Next <i class="bi bi-chevron-right"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Calendar View -->
        <div class="col-lg-8">
            <div class="calendar-grid">
                {% if events %}
                    {% for event in events %}
                        <div class="event-card" style="border-left-color: {{ event.meeting_type.color|default:'#007bff' }};">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="event-time">
                                        {{ event.start_datetime|date:"M d, Y g:i A" }}
                                        {% if not event.all_day %}
                                            - {{ event.end_datetime|date:"g:i A" }}
                                        {% endif %}
                                    </div>
                                    <div class="event-title">
                                        <a href="{% url 'event_management:event_detail' event.id %}" class="text-decoration-none text-dark">
                                            {{ event.title }}
                                        </a>
                                    </div>
                                    <div class="event-meta">
                                        {% if event.meeting_type %}
                                            <span class="meeting-type-badge text-white me-2" 
                                                  style="background-color: {{ event.meeting_type.color }};">
                                                {{ event.meeting_type.name }}
                                            </span>
                                        {% endif %}
                                        <i class="bi bi-geo-alt me-1"></i>
                                        {{ event.get_location_type_display }}
                                        {% if event.related_candidate %}
                                            <br>
                                            <i class="bi bi-person me-1"></i>
                                            {{ event.related_candidate.user.get_full_name|default:event.related_candidate.user.username }}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-{{ event.status|default:'primary' }}">
                                        {{ event.get_status_display }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-calendar-x display-1 text-muted mb-3"></i>
                        <h4 class="text-muted">No events scheduled</h4>
                        <p class="text-muted">
                            {% if view_type == 'day' %}
                                No events scheduled for today.
                            {% elif view_type == 'week' %}
                                No events scheduled for this week.
                            {% else %}
                                No events scheduled for this month.
                            {% endif %}
                        </p>
                        <a href="{% url 'event_management:create_event' %}" class="btn btn-primary">
                            <i class="bi bi-plus-lg me-2"></i>
                            Schedule Your First Event
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="upcoming-events">
                <h5 class="mb-3">
                    <i class="bi bi-clock me-2"></i>
                    Upcoming Events
                </h5>
                {% if upcoming_events %}
                    {% for event in upcoming_events %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="fw-bold">{{ event.title }}</div>
                            <div class="text-muted small">
                                {{ event.start_datetime|date:"M d, g:i A" }}
                            </div>
                            {% if event.meeting_type %}
                                <span class="meeting-type-badge text-white" 
                                      style="background-color: {{ event.meeting_type.color }};">
                                    {{ event.meeting_type.name }}
                                </span>
                            {% endif %}
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No upcoming events.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
