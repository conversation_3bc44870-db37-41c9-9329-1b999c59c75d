{% extends "common/base.html" %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}{{ title }} - Smarch{% endblock %}

{% block extra_css %}
<style>
.form-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.form-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-section {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items-center;
}

.section-title i {
    margin-right: 0.5rem;
    color: #007bff;
}

.datetime-input {
    position: relative;
}

.datetime-input input {
    padding-right: 2.5rem;
}

.datetime-input::after {
    content: '\f073';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
}

.participants-section {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    background: #f8f9fa;
}

.participant-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.participant-item:last-child {
    margin-bottom: 0;
}

.btn-submit {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

@media (max-width: 768px) {
    .form-header {
        padding: 1rem 0;
    }
    
    .form-container {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .section-title {
        font-size: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="form-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="bi bi-calendar-plus me-2"></i>
                    {{ title }}
                </h1>
                <p class="mb-0 mt-2 opacity-75">Schedule a new meeting or interview</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'event_management:calendar' %}" class="btn btn-light">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to Calendar
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            Basic Information
                        </h5>
                        <div class="row">
                            <div class="col-md-8">
                                {{ form.title|as_crispy_field }}
                            </div>
                            <div class="col-md-4">
                                {{ form.meeting_type|as_crispy_field }}
                            </div>
                        </div>
                        {{ form.description|as_crispy_field }}
                    </div>

                    <!-- Date & Time Section -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-clock"></i>
                            Date & Time
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="datetime-input">
                                    {{ form.start_datetime|as_crispy_field }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="datetime-input">
                                    {{ form.end_datetime|as_crispy_field }}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.all_day|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Location Section -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-geo-alt"></i>
                            Location
                        </h5>
                        <div class="row">
                            <div class="col-md-4">
                                {{ form.location_type|as_crispy_field }}
                            </div>
                            <div class="col-md-8">
                                {{ form.location|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Related Records Section -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-link-45deg"></i>
                            Related Records
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.related_candidate|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.related_job_offer|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Participants Section -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-people"></i>
                            Additional Participants
                        </h5>
                        <div class="participants-section">
                            {{ form.participants_list|as_crispy_field }}
                        </div>
                        <small class="text-muted">
                            You will be automatically added as the organizer. Select additional participants who should be invited to this event.
                        </small>
                    </div>

                    <!-- Privacy Section -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="bi bi-shield-lock"></i>
                            Privacy Settings
                        </h5>
                        {{ form.is_private|as_crispy_field }}
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-submit">
                            <i class="bi bi-calendar-check me-2"></i>
                            Schedule Event
                        </button>
                        <a href="{% url 'event_management:calendar' %}" class="btn btn-outline-secondary ms-3">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-calculate end time when start time changes
    const startInput = document.querySelector('input[name="start_datetime"]');
    const endInput = document.querySelector('input[name="end_datetime"]');
    
    if (startInput && endInput) {
        startInput.addEventListener('change', function() {
            if (this.value && !endInput.value) {
                const startDate = new Date(this.value);
                const endDate = new Date(startDate.getTime() + (60 * 60 * 1000)); // Add 1 hour
                
                // Format for datetime-local input
                const year = endDate.getFullYear();
                const month = String(endDate.getMonth() + 1).padStart(2, '0');
                const day = String(endDate.getDate()).padStart(2, '0');
                const hours = String(endDate.getHours()).padStart(2, '0');
                const minutes = String(endDate.getMinutes()).padStart(2, '0');
                
                endInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
            }
        });
    }
    
    // Meeting type color preview
    const meetingTypeSelect = document.querySelector('select[name="meeting_type"]');
    if (meetingTypeSelect) {
        meetingTypeSelect.addEventListener('change', function() {
            // This could be enhanced to show color preview
            console.log('Meeting type changed:', this.value);
        });
    }
});
</script>
{% endblock %}
