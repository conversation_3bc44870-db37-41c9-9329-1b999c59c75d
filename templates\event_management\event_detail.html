{% extends "common/base.html" %}
{% load static %}

{% block title %}{{ event.title }} - Smarch{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            {% if event.image %}
            <img src="{{ event.image.url }}" class="img-fluid rounded mb-4" alt="{{ event.title }}">
            {% endif %}

            <h1 class="mb-3">{{ event.title }}</h1>

            <div class="mb-4">
                <p class="text-muted">
                    <i class="fas fa-calendar"></i> {{ event.date|date:"F d, Y" }}
                    {% if event.location %}
                    <br>
                    <i class="fas fa-map-marker-alt"></i> {{ event.location }}
                    {% endif %}
                </p>
            </div>

            <div class="event-description mb-4">
                {{ event.description|linebreaks }}
            </div>

            {% if event.registration_required %}
            <div class="registration-section mb-4">
                <h3>Registration</h3>
                <p>{{ event.registration_info }}</p>
                <a href="#" class="btn btn-primary">Register Now</a>
            </div>
            {% endif %}
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Event Details</h5>
                    <ul class="list-unstyled">
                        <li><strong>Date:</strong> {{ event.date|date:"F d, Y" }}</li>
                        {% if event.time %}
                        <li><strong>Time:</strong> {{ event.time }}</li>
                        {% endif %}
                        {% if event.location %}
                        <li><strong>Location:</strong> {{ event.location }}</li>
                        {% endif %}
                        {% if event.capacity %}
                        <li><strong>Capacity:</strong> {{ event.capacity }} people</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}