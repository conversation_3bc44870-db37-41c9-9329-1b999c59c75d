{% extends "common/base.html" %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}{{ title }} - Smarch{% endblock %}

{% block extra_css %}
<style>
.quick-schedule-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.candidate-info {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    border-left: 5px solid #28a745;
}

.form-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.candidate-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.quick-form .form-group {
    margin-bottom: 1.5rem;
}

.datetime-input {
    position: relative;
}

.datetime-input input {
    padding-right: 2.5rem;
}

.meeting-type-preview {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 0.5rem;
    vertical-align: middle;
}

.btn-schedule {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-schedule:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.quick-tips {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 2rem;
    border-left: 4px solid #17a2b8;
}

@media (max-width: 768px) {
    .quick-schedule-header {
        padding: 1rem 0;
    }
    
    .candidate-info, .form-container {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .candidate-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="quick-schedule-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="bi bi-calendar-plus me-2"></i>
                    Quick Interview Scheduling
                </h1>
                <p class="mb-0 mt-2 opacity-75">Schedule an interview with your candidate</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'services:pipeline' %}" class="btn btn-light">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to Pipeline
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Candidate Information -->
            <div class="candidate-info">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="candidate-avatar">
                            {{ candidate.user.first_name|first|default:candidate.user.username|first }}{{ candidate.user.last_name|first|default:'' }}
                        </div>
                    </div>
                    <div class="col">
                        <h4 class="mb-1">
                            {{ candidate.user.get_full_name|default:candidate.user.username }}
                        </h4>
                        <p class="text-muted mb-1">
                            <i class="bi bi-envelope me-2"></i>
                            {{ candidate.user.email }}
                        </p>
                        {% if candidate.phone_number %}
                            <p class="text-muted mb-0">
                                <i class="bi bi-telephone me-2"></i>
                                {{ candidate.phone_number }}
                            </p>
                        {% endif %}
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-success fs-6">
                            <i class="bi bi-person-check me-1"></i>
                            Candidate
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Schedule Form -->
            <div class="form-container">
                <h5 class="mb-4">
                    <i class="bi bi-clock me-2"></i>
                    Interview Details
                </h5>
                
                <form method="post" class="quick-form" novalidate>
                    {% csrf_token %}
                    
                    {{ form.title|as_crispy_field }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="datetime-input">
                                {{ form.start_datetime|as_crispy_field }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            {{ form.meeting_type|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            {{ form.location_type|as_crispy_field }}
                        </div>
                        <div class="col-md-8">
                            {{ form.location|as_crispy_field }}
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-success btn-schedule">
                            <i class="bi bi-calendar-check me-2"></i>
                            Schedule Interview
                        </button>
                        <a href="{% url 'services:pipeline' %}" class="btn btn-outline-secondary ms-3">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>

            <!-- Quick Tips -->
            <div class="quick-tips">
                <h6 class="mb-3">
                    <i class="bi bi-lightbulb me-2"></i>
                    Quick Tips for Interview Scheduling
                </h6>
                <ul class="mb-0">
                    <li><strong>Video Interviews:</strong> Include the meeting link in the location field</li>
                    <li><strong>Phone Interviews:</strong> Add the phone number you'll call from</li>
                    <li><strong>In-Person:</strong> Provide the complete office address and any special instructions</li>
                    <li><strong>Timing:</strong> Consider the candidate's time zone and availability</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-populate location based on location type
    const locationTypeSelect = document.querySelector('select[name="location_type"]');
    const locationInput = document.querySelector('input[name="location"]');
    
    if (locationTypeSelect && locationInput) {
        locationTypeSelect.addEventListener('change', function() {
            if (!locationInput.value) {
                switch(this.value) {
                    case 'video_call':
                        locationInput.placeholder = 'e.g., Zoom meeting link, Teams link';
                        break;
                    case 'phone_call':
                        locationInput.placeholder = 'e.g., I will call you at your provided number';
                        break;
                    case 'in_person':
                        locationInput.placeholder = 'e.g., Office address, meeting room';
                        break;
                    case 'online':
                        locationInput.placeholder = 'e.g., Google Meet, Skype';
                        break;
                    default:
                        locationInput.placeholder = 'Meeting location or details';
                }
            }
        });
    }
    
    // Set minimum date to today
    const dateInput = document.querySelector('input[name="start_datetime"]');
    if (dateInput) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        
        dateInput.min = `${year}-${month}-${day}T${hours}:${minutes}`;
    }
    
    // Form validation
    const form = document.querySelector('.quick-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const startDateTime = document.querySelector('input[name="start_datetime"]').value;
            const location = document.querySelector('input[name="location"]').value;
            
            if (!startDateTime) {
                e.preventDefault();
                alert('Please select a date and time for the interview.');
                return;
            }
            
            const selectedDate = new Date(startDateTime);
            const now = new Date();
            
            if (selectedDate <= now) {
                e.preventDefault();
                alert('Please select a future date and time for the interview.');
                return;
            }
            
            if (!location.trim()) {
                e.preventDefault();
                alert('Please provide location details for the interview.');
                return;
            }
        });
    }
});
</script>
{% endblock %}
