{% extends 'common/base.html' %}
{% load static %}

{% block title %}Conversation - Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row mb-4">
    <div class="col-md-8">
      <h1 class="mb-3">
        {% if other_participant.get_full_name %}
          Conversation with {{ other_participant.get_full_name }}
        {% else %}
          Conversation with {{ other_participant.username }}
        {% endif %}
      </h1>
    </div>
    <div class="col-md-4 text-md-end">
      <a href="{% url 'messaging:inbox' %}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> Back to Messages
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8">
      <!-- Messages Card -->
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <div class="d-flex align-items-center">
            {% if other_participant.profile_image %}
              <img src="{{ other_participant.profile_image.url }}" alt="{{ other_participant.get_full_name }}" class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">
            {% else %}
              <div class="bg-light rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                <i class="bi bi-person-fill fs-4"></i>
              </div>
            {% endif %}
            <h5 class="mb-0">
              {% if other_participant.get_full_name %}
                {{ other_participant.get_full_name }}
              {% else %}
                {{ other_participant.username }}
              {% endif %}
            </h5>
          </div>
        </div>
        <div class="card-body" style="height: 400px; overflow-y: auto;" id="messages-container">
          {% if conversation.messages.all %}
            <div class="d-flex flex-column">
              {% for message in conversation.messages.all %}
                <div class="message mb-3 {% if message.sender == user %}align-self-end{% else %}align-self-start{% endif %}">
                  <div class="card {% if message.sender == user %}bg-primary text-white{% else %}bg-light{% endif %}" style="max-width: 75%;">
                    <div class="card-body py-2 px-3">
                      <p class="mb-0">{{ message.content }}</p>
                    </div>
                  </div>
                  <small class="text-muted d-block mt-1">
                    {{ message.timestamp|date:"M d, Y" }} at {{ message.timestamp|time:"g:i A" }}
                    {% if message.sender == user and message.is_read %}
                      <span class="ms-1"><i class="bi bi-check-all"></i></span>
                    {% elif message.sender == user %}
                      <span class="ms-1"><i class="bi bi-check"></i></span>
                    {% endif %}
                  </small>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i> No messages yet. Start the conversation by sending a message below.
            </div>
          {% endif %}
        </div>
        <div class="card-footer">
          <form method="post" action="{% url 'messaging:send_message' conversation.id %}">
            {% csrf_token %}
            <div class="input-group">
              <input type="text" name="content" class="form-control" placeholder="Type your message..." required>
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-send"></i> Send
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <!-- User Info Card -->
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">User Information</h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            {% if other_participant.profile_image %}
              <img src="{{ other_participant.profile_image.url }}" alt="{{ other_participant.get_full_name }}" class="rounded-circle mb-3" style="width: 100px; height: 100px; object-fit: cover;">
            {% else %}
              <div class="bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                <i class="bi bi-person-fill fs-1"></i>
              </div>
            {% endif %}
            <h5>
              {% if other_participant.get_full_name %}
                {{ other_participant.get_full_name }}
              {% else %}
                {{ other_participant.username }}
              {% endif %}
            </h5>
            <p class="text-muted">
              {% if hasattr(other_participant, 'applicant_profile') %}
                Applicant
              {% elif hasattr(other_participant, 'hiring_partner_profile') %}
                Hiring Partner
              {% elif other_participant.is_staff %}
                Staff Member
              {% else %}
                User
              {% endif %}
            </p>
          </div>

          <ul class="list-group list-group-flush">
            {% if hasattr(other_participant, 'applicant_profile') %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span><i class="bi bi-briefcase me-2"></i> Industry</span>
                <span class="text-muted">{{ other_participant.applicant_profile.industry|default:"Not specified" }}</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span><i class="bi bi-person-badge me-2"></i> Role</span>
                <span class="text-muted">{{ other_participant.applicant_profile.role|default:"Not specified" }}</span>
              </li>
            {% elif hasattr(other_participant, 'hiring_partner_profile') %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span><i class="bi bi-building me-2"></i> Company</span>
                <span class="text-muted">{{ other_participant.hiring_partner_profile.company_name }}</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span><i class="bi bi-geo-alt me-2"></i> Location</span>
                <span class="text-muted">{{ other_participant.hiring_partner_profile.primary_location|default:"Not specified" }}</span>
              </li>
            {% endif %}
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span><i class="bi bi-calendar me-2"></i> Joined</span>
              <span class="text-muted">{{ other_participant.date_joined|date:"M d, Y" }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

{% block extra_js %}
<script>
  // Scroll to the bottom of the messages container when the page loads
  document.addEventListener('DOMContentLoaded', function() {
    const messagesContainer = document.getElementById('messages-container');
    if (messagesContainer) {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  });
</script>
{% endblock %}
{% endblock %}
