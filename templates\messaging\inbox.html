{% extends 'common/base.html' %}
{% load static %}

{% block title %}Messages - Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row mb-4">
    <div class="col-md-8">
      <h1 class="mb-3">Messages</h1>
      <p class="text-muted">Manage your conversations with other users.</p>
    </div>
    <div class="col-md-4 text-md-end">
      <a href="{% url 'users:dashboard' %}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> Back to Dashboard
      </a>
    </div>
  </div>

  <!-- Conversations List -->
  <div class="card">
    <div class="card-body">
      {% if page_obj %}
        <div class="list-group">
          {% for conversation in page_obj %}
            {% with other_participant=conversation.get_other_participant %}
              <a href="{% url 'messaging:conversation_detail' conversation.id %}" class="list-group-item list-group-item-action {% if conversation.unread_count > 0 %}bg-light{% endif %}">
                <div class="d-flex w-100 justify-content-between align-items-center">
                  <div class="d-flex align-items-center">
                    {% if other_participant.profile_image %}
                      <img src="{{ other_participant.profile_image.url }}" alt="{{ other_participant.get_full_name }}" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                    {% else %}
                      <div class="bg-secondary rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                        <i class="bi bi-person-fill text-white fs-4"></i>
                      </div>
                    {% endif %}
                    <div>
                      <h5 class="mb-1">
                        {% if other_participant.get_full_name %}
                          {{ other_participant.get_full_name }}
                        {% else %}
                          {{ other_participant.username }}
                        {% endif %}
                      </h5>
                      <p class="mb-1 text-muted">
                        {% with last_message=conversation.get_last_message %}
                          {% if last_message %}
                            {% if last_message.sender == user %}
                              <span class="text-muted">You: </span>
                            {% endif %}
                            {{ last_message.content|truncatechars:50 }}
                          {% else %}
                            <span class="text-muted">No messages yet</span>
                          {% endif %}
                        {% endwith %}
                      </p>
                    </div>
                  </div>
                  <div class="text-end">
                    {% with last_message=conversation.get_last_message %}
                      {% if last_message %}
                        <small class="text-muted">{{ last_message.timestamp|date:"M d, Y" }}</small>
                      {% endif %}
                    {% endwith %}
                    {% if conversation.unread_count > 0 %}
                      <span class="badge bg-primary rounded-pill ms-2">{{ conversation.unread_count }}</span>
                    {% endif %}
                  </div>
                </div>
              </a>
            {% endwith %}
          {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
          <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
              {% if page_obj.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% endif %}

              {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                  <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                  <li class="page-item">
                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                  </li>
                {% endif %}
              {% endfor %}

              {% if page_obj.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
              {% endif %}
            </ul>
          </nav>
        {% endif %}
      {% else %}
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i> You don't have any conversations yet.
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
