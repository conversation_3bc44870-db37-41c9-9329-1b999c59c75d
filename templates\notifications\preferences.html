{% extends 'common/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Notification Preferences" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h1 class="h3 mb-0">{% trans "Notification Preferences" %}</h1>
        </div>
        <div class="card-body">
          <p class="lead mb-4">{% trans "Manage your notification preferences below. You can choose which types of notifications you want to receive via email." %}</p>
          
          <form method="post">
            {% csrf_token %}
            
            <div class="mb-4">
              <h4>{% trans "Email Notifications" %}</h4>
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="email_new_messages" name="email_new_messages" {% if preferences.email_new_messages %}checked{% endif %}>
                <label class="form-check-label" for="email_new_messages">
                  {% trans "New Messages" %}
                  <small class="text-muted d-block">{% trans "Receive email notifications when you get a new message." %}</small>
                </label>
              </div>
              
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="email_contact_requests" name="email_contact_requests" {% if preferences.email_contact_requests %}checked{% endif %}>
                <label class="form-check-label" for="email_contact_requests">
                  {% trans "Contact Requests" %}
                  <small class="text-muted d-block">{% trans "Receive email notifications when you get a new contact request." %}</small>
                </label>
              </div>
              
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="email_application_updates" name="email_application_updates" {% if preferences.email_application_updates %}checked{% endif %}>
                <label class="form-check-label" for="email_application_updates">
                  {% trans "Application Updates" %}
                  <small class="text-muted d-block">{% trans "Receive email notifications when there are updates to your applications." %}</small>
                </label>
              </div>
              
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="email_subscription_updates" name="email_subscription_updates" {% if preferences.email_subscription_updates %}checked{% endif %}>
                <label class="form-check-label" for="email_subscription_updates">
                  {% trans "Subscription Updates" %}
                  <small class="text-muted d-block">{% trans "Receive email notifications about your subscription status." %}</small>
                </label>
              </div>
            </div>
            
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary">{% trans "Save Preferences" %}</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
