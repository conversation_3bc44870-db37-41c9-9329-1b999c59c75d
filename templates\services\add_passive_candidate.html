{% extends 'common/base.html' %}
{% load i18n %}

{% block title %}Add Passive Candidate - Smarch{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .form-section h5 {
        color: #495057;
        border-bottom: 2px solid #007bff;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }
    .skills-input {
        border: 1px dashed #dee2e6;
        border-radius: 8px;
        padding: 10px;
        background-color: #f8f9fa;
    }
    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-plus text-primary"></i>
                        {% trans "Add Passive Candidate" %}
                    </h1>
                    <p class="text-muted mb-0">{% trans "Add a new candidate to your passive sourcing pipeline" %}</p>
                </div>
                <div>
                    <a href="{% url 'services:passive_candidates_list' %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> {% trans "Back to Candidates" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="post">
        {% csrf_token %}
        
        <!-- Basic Information -->
        <div class="form-section">
            <h5><i class="bi bi-person"></i> {% trans "Basic Information" %}</h5>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="first_name" class="form-label required-field">{% trans "First Name" %}</label>
                    <input type="text" class="form-control" id="first_name" name="first_name" 
                           value="{{ form_data.first_name|default:'' }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="last_name" class="form-label required-field">{% trans "Last Name" %}</label>
                    <input type="text" class="form-control" id="last_name" name="last_name" 
                           value="{{ form_data.last_name|default:'' }}" required>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label required-field">{% trans "Email Address" %}</label>
                    <input type="email" class="form-control" id="email" name="email" 
                           value="{{ form_data.email|default:'' }}" required>
                    <div class="help-text">{% trans "Primary contact email for this candidate" %}</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">{% trans "Phone Number" %}</label>
                    <input type="tel" class="form-control" id="phone" name="phone" 
                           value="{{ form_data.phone|default:'' }}" placeholder="+****************">
                </div>
            </div>
        </div>

        <!-- Professional Information -->
        <div class="form-section">
            <h5><i class="bi bi-briefcase"></i> {% trans "Professional Information" %}</h5>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="current_company" class="form-label">{% trans "Current Company" %}</label>
                    <input type="text" class="form-control" id="current_company" name="current_company" 
                           value="{{ form_data.current_company|default:'' }}" placeholder="Google, Microsoft, etc.">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="current_position" class="form-label">{% trans "Current Position" %}</label>
                    <input type="text" class="form-control" id="current_position" name="current_position" 
                           value="{{ form_data.current_position|default:'' }}" placeholder="Senior Software Engineer">
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="industry" class="form-label">{% trans "Industry" %}</label>
                    <input type="text" class="form-control" id="industry" name="industry" 
                           value="{{ form_data.industry|default:'' }}" placeholder="Technology, Finance, etc.">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="location" class="form-label">{% trans "Location" %}</label>
                    <input type="text" class="form-control" id="location" name="location" 
                           value="{{ form_data.location|default:'' }}" placeholder="San Francisco, CA">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="experience_years" class="form-label">{% trans "Years of Experience" %}</label>
                    <input type="number" class="form-control" id="experience_years" name="experience_years" 
                           value="{{ form_data.experience_years|default:'0' }}" min="0" max="50">
                </div>
            </div>
        </div>

        <!-- Skills & Profile -->
        <div class="form-section">
            <h5><i class="bi bi-tools"></i> {% trans "Skills & Profile" %}</h5>
            <div class="mb-3">
                <label for="skills" class="form-label">{% trans "Skills" %}</label>
                <textarea class="form-control skills-input" id="skills" name="skills" rows="3" 
                          placeholder="Python, JavaScript, React, AWS, Project Management">{{ form_data.skills|join:", "|default:'' }}</textarea>
                <div class="help-text">{% trans "Separate skills with commas. Include both technical and soft skills." %}</div>
            </div>
            <div class="mb-3">
                <label for="bio" class="form-label">{% trans "Bio / Summary" %}</label>
                <textarea class="form-control" id="bio" name="bio" rows="4" 
                          placeholder="Brief professional summary, achievements, or notable experience...">{{ form_data.bio|default:'' }}</textarea>
                <div class="help-text">{% trans "Optional professional summary or notes about this candidate" %}</div>
            </div>
        </div>

        <!-- Online Presence -->
        <div class="form-section">
            <h5><i class="bi bi-globe"></i> {% trans "Online Presence" %}</h5>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="linkedin_url" class="form-label">{% trans "LinkedIn Profile" %}</label>
                    <input type="url" class="form-control" id="linkedin_url" name="linkedin_url" 
                           value="{{ form_data.linkedin_url|default:'' }}" placeholder="https://linkedin.com/in/username">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="github_url" class="form-label">{% trans "GitHub Profile" %}</label>
                    <input type="url" class="form-control" id="github_url" name="github_url" 
                           value="{{ form_data.github_url|default:'' }}" placeholder="https://github.com/username">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="portfolio_url" class="form-label">{% trans "Portfolio / Website" %}</label>
                    <input type="url" class="form-control" id="portfolio_url" name="portfolio_url" 
                           value="{{ form_data.portfolio_url|default:'' }}" placeholder="https://portfolio.com">
                </div>
            </div>
        </div>

        <!-- Discovery Information -->
        <div class="form-section">
            <h5><i class="bi bi-search"></i> {% trans "Discovery Information" %}</h5>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="discovery_source" class="form-label">{% trans "Discovery Source" %}</label>
                    <select class="form-select" id="discovery_source" name="discovery_source">
                        {% for value, label in source_choices %}
                        <option value="{{ value }}" {% if form_data.discovery_source == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                    <div class="help-text">{% trans "How did you discover this candidate?" %}</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="discovery_notes" class="form-label">{% trans "Discovery Notes" %}</label>
                    <textarea class="form-control" id="discovery_notes" name="discovery_notes" rows="3" 
                              placeholder="Notes about how you found this candidate, mutual connections, etc.">{{ form_data.discovery_notes|default:'' }}</textarea>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                        <i class="bi bi-arrow-clockwise"></i> {% trans "Clear Form" %}
                    </button>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i> {% trans "Add Candidate" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearForm() {
    if (confirm('Are you sure you want to clear all form data?')) {
        document.querySelector('form').reset();
    }
}

// Auto-format phone number
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length >= 10) {
        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    }
    e.target.value = value;
});

// Skills input enhancement
document.getElementById('skills').addEventListener('blur', function(e) {
    let skills = e.target.value.split(',').map(s => s.trim()).filter(s => s);
    e.target.value = skills.join(', ');
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const email = document.getElementById('email').value;
    const firstName = document.getElementById('first_name').value;
    const lastName = document.getElementById('last_name').value;
    
    if (!email || !firstName || !lastName) {
        e.preventDefault();
        alert('Please fill in all required fields (marked with *)');
        return false;
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        e.preventDefault();
        alert('Please enter a valid email address');
        return false;
    }
});

document.addEventListener('DOMContentLoaded', function() {
    console.log('Add passive candidate form loaded');
});
</script>
{% endblock %}
