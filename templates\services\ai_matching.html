{% extends 'common/base.html' %}
{% load i18n %}

{% block title %}AI Candidate Matching - {{ offer.title }} - Smarch{% endblock %}

{% block extra_css %}
<style>
    .candidate-card {
        transition: transform 0.2s, box-shadow 0.2s;
        border-left: 4px solid #dee2e6;
    }
    .candidate-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .candidate-card.excellent { border-left-color: #28a745; }
    .candidate-card.good { border-left-color: #17a2b8; }
    .candidate-card.fair { border-left-color: #ffc107; }
    .candidate-card.poor { border-left-color: #dc3545; }
    
    .score-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
    }
    .score-excellent { background: linear-gradient(135deg, #28a745, #20c997); }
    .score-good { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
    .score-fair { background: linear-gradient(135deg, #ffc107, #fd7e14); }
    .score-poor { background: linear-gradient(135deg, #dc3545, #e83e8c); }
    
    .skill-tag {
        display: inline-block;
        padding: 2px 8px;
        margin: 2px;
        border-radius: 12px;
        font-size: 0.75rem;
    }
    .skill-matched { background-color: #d4edda; color: #155724; }
    .skill-missing { background-color: #f8d7da; color: #721c24; }
    
    .progress-thin {
        height: 8px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-robot text-primary"></i>
                        {% trans "AI Candidate Matching" %}
                    </h1>
                    <p class="text-muted mb-0">{{ offer.title }} - {{ matched_candidates|length }} {% trans "candidates found" %}</p>
                </div>
                <div>
                    <a href="{% url 'services:setup_job_requirements' offer.id %}" class="btn btn-outline-primary me-2">
                        <i class="bi bi-gear"></i> {% trans "Configure Requirements" %}
                    </a>
                    <a href="{% url 'services:offers' %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> {% trans "Back to Offers" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="card-title">{{ offer.title }}</h5>
                            <p class="card-text">{{ offer.description|truncatewords:30 }}</p>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-2">${{ offer.price }}</span>
                                <span class="text-muted">{{ offer.hiring_partner.company_name }}</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            {% if requirements %}
                            <h6>{% trans "Matching Criteria" %}</h6>
                            <div class="small">
                                <div><strong>{% trans "Required Skills:" %}</strong> {{ requirements.required_skills|length }}</div>
                                <div><strong>{% trans "Min Experience:" %}</strong> {{ requirements.min_experience_years }} {% trans "years" %}</div>
                                <div><strong>{% trans "Remote Work:" %}</strong> 
                                    {% if requirements.remote_work_allowed %}{% trans "Yes" %}{% else %}{% trans "No" %}{% endif %}
                                </div>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <small>
                                    <i class="bi bi-info-circle"></i>
                                    {% trans "Configure job requirements for better matching" %}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Matching Results -->
    <div class="row">
        {% for candidate_data in matched_candidates %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card candidate-card {{ candidate_data.quality_rating }} h-100">
                <div class="card-body">
                    <!-- Header with Score -->
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">
                                {{ candidate_data.candidate.user.get_full_name|default:candidate_data.candidate.user.username }}
                            </h6>
                            <p class="text-muted small mb-0">
                                {{ candidate_data.candidate.role|default:"No role specified" }}
                            </p>
                            <p class="text-muted small">
                                <i class="bi bi-geo-alt"></i> {{ candidate_data.candidate.location|default:"Location not specified" }}
                            </p>
                        </div>
                        <div class="score-circle score-{{ candidate_data.quality_rating }}">
                            {{ candidate_data.overall_score }}%
                        </div>
                    </div>

                    <!-- Score Breakdown -->
                    <div class="mb-3">
                        <div class="row text-center">
                            <div class="col-3">
                                <div class="small text-muted">Skills</div>
                                <div class="fw-bold">{{ candidate_data.skills_score }}%</div>
                            </div>
                            <div class="col-3">
                                <div class="small text-muted">Exp.</div>
                                <div class="fw-bold">{{ candidate_data.experience_score }}%</div>
                            </div>
                            <div class="col-3">
                                <div class="small text-muted">Location</div>
                                <div class="fw-bold">{{ candidate_data.location_score }}%</div>
                            </div>
                            <div class="col-3">
                                <div class="small text-muted">Lang.</div>
                                <div class="fw-bold">{{ candidate_data.language_score }}%</div>
                            </div>
                        </div>
                    </div>

                    <!-- Skills -->
                    <div class="mb-3">
                        <h6 class="small fw-bold">{% trans "Skills Match" %}</h6>
                        <div>
                            {% for skill in candidate_data.matched_skills %}
                                <span class="skill-tag skill-matched">{{ skill }}</span>
                            {% endfor %}
                            {% for skill in candidate_data.missing_skills|slice:":3" %}
                                <span class="skill-tag skill-missing">{{ skill }}</span>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Experience -->
                    {% if candidate_data.candidate.position_experience_years %}
                    <div class="mb-3">
                        <h6 class="small fw-bold">{% trans "Experience" %}</h6>
                        <p class="small mb-0">{{ candidate_data.candidate.position_experience_years }} {% trans "years" %}</p>
                    </div>
                    {% endif %}

                    <!-- Actions -->
                    <div class="d-flex gap-2">
                        <a href="{% url 'services:applicant_detail' candidate_data.candidate.id %}" 
                           class="btn btn-primary btn-sm flex-grow-1">
                            <i class="bi bi-eye"></i> {% trans "View Profile" %}
                        </a>
                        <button class="btn btn-outline-success btn-sm" 
                                onclick="addToPipeline({{ candidate_data.candidate.id }})">
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-search display-1 text-muted"></i>
                    <h4 class="mt-3">{% trans "No matching candidates found" %}</h4>
                    <p class="text-muted">
                        {% trans "Try adjusting your job requirements or check back later for new candidates." %}
                    </p>
                    <a href="{% url 'services:setup_job_requirements' offer.id %}" class="btn btn-primary">
                        {% trans "Configure Requirements" %}
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Skill Gaps Analysis -->
    {% if matched_candidates %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-graph-up"></i> {% trans "Skills Gap Analysis" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>{% trans "Most Common Missing Skills" %}</h6>
                            <div id="missingSkillsChart">
                                <!-- Skills gap analysis would go here -->
                                <p class="text-muted">{% trans "Analysis based on candidate pool" %}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>{% trans "Recommendations" %}</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-lightbulb text-warning"></i> {% trans "Consider remote candidates to expand your pool" %}</li>
                                <li><i class="bi bi-lightbulb text-warning"></i> {% trans "Review experience requirements - junior candidates may be trainable" %}</li>
                                <li><i class="bi bi-lightbulb text-warning"></i> {% trans "Consider candidates with transferable skills" %}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function addToPipeline(candidateId) {
    fetch('{% url "services:add_candidate_to_pipeline" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `candidate_id=${candidateId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Candidate added to pipeline successfully!');
        } else {
            alert(data.error || 'Failed to add candidate to pipeline');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Network error. Please try again.');
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Matching interface loaded');
});
</script>
{% endblock %}
