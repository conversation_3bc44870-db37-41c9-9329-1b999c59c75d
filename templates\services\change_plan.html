{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Change Subscription Plan" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h1 class="h3 mb-0">{% trans "Change Your Subscription Plan" %}</h1>
        </div>
        <div class="card-body">
          <div class="alert alert-info mb-4">
            <div class="d-flex">
              <div class="me-3">
                <i class="bi bi-info-circle-fill fs-3"></i>
              </div>
              <div>
                <h5 class="alert-heading">{% trans "Current Plan" %}</h5>
                <p class="mb-0">
                  {% trans "You are currently subscribed to the" %} <strong>{{ current_subscription.plan|title }}</strong> {% trans "plan." %}
                  {% trans "Your subscription is active until" %} <strong>{{ current_subscription.end_date|date:"F d, Y" }}</strong>.
                </p>
              </div>
            </div>
          </div>
          
          <form method="post" action="{% url 'services:change_plan' %}">
            {% csrf_token %}
            
            <h5 class="border-bottom pb-2 mb-3">{% trans "Select Your New Plan" %}</h5>
            
            <div class="row mb-4">
              <!-- Basic Plan -->
              <div class="col-md-4 mb-3">
                <div class="card h-100 {% if current_subscription.plan == 'basic' %}border-primary{% endif %}">
                  <div class="card-header text-center {% if current_subscription.plan == 'basic' %}bg-primary text-white{% else %}bg-light{% endif %}">
                    <h5 class="mb-0">{% trans "Basic" %}</h5>
                  </div>
                  <div class="card-body d-flex flex-column">
                    <div class="text-center mb-3">
                      <h3 class="mb-0">$99<small class="text-muted">/month</small></h3>
                    </div>
                    <ul class="list-unstyled mb-4">
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Browse up to 10 applicants" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "5 contact requests per month" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Basic company profile" %}
                      </li>
                    </ul>
                    <div class="mt-auto d-grid">
                      {% if current_subscription.plan == 'basic' %}
                        <button type="button" class="btn btn-primary" disabled>
                          <i class="bi bi-check-circle-fill me-1"></i> {% trans "Current Plan" %}
                        </button>
                      {% else %}
                        <button type="submit" name="plan" value="basic" class="btn btn-outline-primary">
                          {% trans "Select Basic" %}
                        </button>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Standard Plan -->
              <div class="col-md-4 mb-3">
                <div class="card h-100 {% if current_subscription.plan == 'standard' %}border-primary{% endif %}">
                  <div class="card-header text-center {% if current_subscription.plan == 'standard' %}bg-primary text-white{% else %}bg-light{% endif %}">
                    <h5 class="mb-0">{% trans "Standard" %}</h5>
                  </div>
                  <div class="card-body d-flex flex-column">
                    <div class="text-center mb-3">
                      <h3 class="mb-0">$199<small class="text-muted">/month</small></h3>
                    </div>
                    <ul class="list-unstyled mb-4">
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Browse up to 50 applicants" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "20 contact requests per month" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Enhanced company profile" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Basic filtering" %}
                      </li>
                    </ul>
                    <div class="mt-auto d-grid">
                      {% if current_subscription.plan == 'standard' %}
                        <button type="button" class="btn btn-primary" disabled>
                          <i class="bi bi-check-circle-fill me-1"></i> {% trans "Current Plan" %}
                        </button>
                      {% else %}
                        <button type="submit" name="plan" value="standard" class="btn btn-outline-primary">
                          {% trans "Select Standard" %}
                        </button>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Premium Plan -->
              <div class="col-md-4 mb-3">
                <div class="card h-100 {% if current_subscription.plan == 'premium' %}border-primary{% endif %}">
                  <div class="card-header text-center {% if current_subscription.plan == 'premium' %}bg-primary text-white{% else %}bg-light{% endif %}">
                    <h5 class="mb-0">{% trans "Premium" %}</h5>
                  </div>
                  <div class="card-body d-flex flex-column">
                    <div class="text-center mb-3">
                      <h3 class="mb-0">$299<small class="text-muted">/month</small></h3>
                    </div>
                    <ul class="list-unstyled mb-4">
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Unlimited applicant browsing" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Unlimited contact requests" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Premium company profile" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Advanced filtering & analytics" %}
                      </li>
                    </ul>
                    <div class="mt-auto d-grid">
                      {% if current_subscription.plan == 'premium' %}
                        <button type="button" class="btn btn-primary" disabled>
                          <i class="bi bi-check-circle-fill me-1"></i> {% trans "Current Plan" %}
                        </button>
                      {% else %}
                        <button type="submit" name="plan" value="premium" class="btn btn-outline-primary">
                          {% trans "Select Premium" %}
                        </button>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="alert alert-warning">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>
              <p class="mb-0">
                {% trans "Changing your plan will take effect immediately. You will be charged the difference if upgrading, or credited for the remaining time on your current plan if downgrading." %}
              </p>
            </div>
            
            <div class="d-flex justify-content-end mt-4">
              <a href="{% url 'services:subscription' %}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
