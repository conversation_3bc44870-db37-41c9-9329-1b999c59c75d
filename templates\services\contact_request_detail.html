{% extends 'common/base.html' %}
{% load static %}

{% block title %}Contact Request Details - Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row mb-4">
    <div class="col-md-8">
      <h1 class="mb-3">Contact Request Details</h1>
    </div>
    <div class="col-md-4 text-md-end">
      <a href="{% url 'services:contact_requests' %}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> Back to Contact Requests
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8">
      <!-- Contact Request Card -->
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Request Information</h5>
          <span class="badge {% if contact_request.status == 'pending' %}bg-warning text-dark{% elif contact_request.status == 'accepted' %}bg-success{% elif contact_request.status == 'declined' %}bg-danger{% endif %}">
            {{ contact_request.status|title }}
          </span>
        </div>
        <div class="card-body">
          <div class="mb-4">
            <h6 class="text-muted mb-2">Sent to:</h6>
            <div class="d-flex align-items-center">
              {% if contact_request.recipient.image %}
                <img src="{{ contact_request.recipient.image.url }}" alt="{{ contact_request.recipient.user.get_full_name }}" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
              {% else %}
                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                  <i class="bi bi-person text-secondary" style="font-size: 1.5rem;"></i>
                </div>
              {% endif %}
              <div>
                <h5 class="mb-0">{{ contact_request.recipient.user.get_full_name }}</h5>
                <p class="text-muted mb-0">{{ contact_request.recipient.role|default:"Role not specified" }}</p>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <h6 class="text-muted mb-2">Date Sent:</h6>
            <p>{{ contact_request.sent_at|date:"F d, Y - h:i A" }}</p>
          </div>

          {% if contact_request.responded_at %}
            <div class="mb-4">
              <h6 class="text-muted mb-2">Response Date:</h6>
              <p>{{ contact_request.responded_at|date:"F d, Y - h:i A" }}</p>
            </div>
          {% endif %}

          <div class="mb-4">
            <h6 class="text-muted mb-2">Your Message:</h6>
            <div class="card bg-light">
              <div class="card-body">
                <p class="mb-0">{{ contact_request.message }}</p>
              </div>
            </div>
          </div>

          {% if contact_request.status == 'accepted' %}
            <div class="alert alert-success">
              <i class="bi bi-check-circle me-2"></i> This applicant has accepted your contact request. You can now communicate directly.
            </div>
            <div class="card border-success mb-4">
              <div class="card-header bg-success text-white">
                <h6 class="mb-0">Contact Information</h6>
              </div>
              <div class="card-body">
                <p class="mb-2"><strong>Email:</strong> {{ contact_request.recipient.user.email }}</p>
                {% if contact_request.recipient.phone_number %}
                  <p class="mb-0"><strong>Phone:</strong> {{ contact_request.recipient.phone_number }}</p>
                {% endif %}

                <div class="mt-3">
                  <a href="{% url 'messaging:new_conversation' contact_request.recipient.user.id %}" class="btn btn-primary">
                    <i class="bi bi-chat-dots me-2"></i> Start Conversation
                  </a>
                </div>
              </div>
            </div>
          {% elif contact_request.status == 'declined' %}
            <div class="alert alert-danger">
              <i class="bi bi-x-circle me-2"></i> This applicant has declined your contact request.
            </div>
          {% else %}
            <div class="alert alert-warning">
              <i class="bi bi-hourglass-split me-2"></i> This contact request is still pending. You'll be notified when the applicant responds.
            </div>
          {% endif %}
        </div>
        <div class="card-footer">
          <div class="d-flex justify-content-between">
            <a href="{% url 'services:applicant_detail' contact_request.recipient.id %}" class="btn btn-outline-primary">
              <i class="bi bi-person"></i> View Applicant Profile
            </a>
            {% if contact_request.status == 'pending' %}
              <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelRequestModal">
                <i class="bi bi-x-circle"></i> Cancel Request
              </button>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <!-- Applicant Summary Card -->
      <div class="card mb-4">
        <div class="card-header bg-light">
          <h5 class="mb-0">Applicant Summary</h5>
        </div>
        <div class="card-body">
          <p class="mb-2">
            <strong>Industry:</strong><br>
            {{ contact_request.recipient.industry|default:"Not specified" }}
          </p>
          <p class="mb-2">
            <strong>Experience:</strong><br>
            {{ contact_request.recipient.position_experience_years }} years
          </p>
          <p class="mb-2">
            <strong>Location:</strong><br>
            {{ contact_request.recipient.location|default:"Not specified" }}
          </p>
          <p class="mb-0">
            <strong>Case Number:</strong><br>
            {{ contact_request.recipient.case_number }}
          </p>
        </div>
      </div>

      <!-- Similar Applicants Card -->
      <div class="card">
        <div class="card-header bg-light">
          <h5 class="mb-0">Similar Applicants</h5>
        </div>
        <div class="card-body">
          <p class="text-muted small mb-3">Here are some similar applicants you might be interested in:</p>

          <!-- This would be populated with actual similar applicants in a real implementation -->
          <div class="list-group">
            <a href="#" class="list-group-item list-group-item-action">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">John Doe</h6>
                <small>5 years exp.</small>
              </div>
              <p class="mb-1">Software Developer</p>
              <small>Stockholm</small>
            </a>
            <a href="#" class="list-group-item list-group-item-action">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">Jane Smith</h6>
                <small>3 years exp.</small>
              </div>
              <p class="mb-1">Web Developer</p>
              <small>Gothenburg</small>
            </a>
            <a href="#" class="list-group-item list-group-item-action">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">Alex Johnson</h6>
                <small>4 years exp.</small>
              </div>
              <p class="mb-1">Frontend Developer</p>
              <small>Malmö</small>
            </a>
          </div>

          <div class="d-grid mt-3">
            <a href="{% url 'services:applicants' %}" class="btn btn-outline-primary btn-sm">
              Browse More Applicants
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Cancel Request Modal -->
<div class="modal fade" id="cancelRequestModal" tabindex="-1" aria-labelledby="cancelRequestModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="cancelRequestModalLabel">Cancel Contact Request</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to cancel this contact request? This action cannot be undone.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <form method="post" action="{% url 'services:contact_request_cancel' contact_request.id %}">
          {% csrf_token %}
          <button type="submit" class="btn btn-danger">Cancel Request</button>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}
