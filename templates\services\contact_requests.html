{% extends 'common/base.html' %}
{% load static %}

{% block title %}Contact Requests - Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row mb-4">
    <div class="col-md-8">
      <h1 class="mb-3">Contact Requests</h1>
      <p class="text-muted">Manage your contact requests to applicants.</p>
    </div>
    <div class="col-md-4 text-md-end">
      <a href="{% url 'services:dashboard' %}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> Back to Dashboard
      </a>
    </div>
  </div>

  <!-- Status Filters -->
  <div class="card mb-4">
    <div class="card-body">
      <div class="d-flex flex-wrap">
        <a href="{% url 'services:contact_requests' %}" class="btn {% if not request.GET.status %}btn-primary{% else %}btn-outline-primary{% endif %} me-2 mb-2">
          All Requests
        </a>
        <a href="{% url 'services:contact_requests' %}?status=pending" class="btn {% if request.GET.status == 'pending' %}btn-primary{% else %}btn-outline-primary{% endif %} me-2 mb-2">
          Pending
        </a>
        <a href="{% url 'services:contact_requests' %}?status=accepted" class="btn {% if request.GET.status == 'accepted' %}btn-primary{% else %}btn-outline-primary{% endif %} me-2 mb-2">
          Accepted
        </a>
        <a href="{% url 'services:contact_requests' %}?status=declined" class="btn {% if request.GET.status == 'declined' %}btn-primary{% else %}btn-outline-primary{% endif %} me-2 mb-2">
          Declined
        </a>
      </div>
    </div>
  </div>

  <!-- Contact Requests Table -->
  <div class="card">
    <div class="card-body">
      {% if page_obj %}
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Date</th>
                <th>Applicant</th>
                <th>Message</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for request in page_obj %}
                <tr>
                  <td>{{ request.sent_at|date:"M d, Y" }}</td>
                  <td>
                    <a href="{% url 'services:applicant_detail' request.recipient.id %}">
                      {{ request.recipient.user.get_full_name }}
                    </a>
                  </td>
                  <td>{{ request.message|truncatechars:50 }}</td>
                  <td>
                    {% if request.status == 'pending' %}
                      <span class="badge bg-warning text-dark">Pending</span>
                    {% elif request.status == 'accepted' %}
                      <span class="badge bg-success">Accepted</span>
                    {% elif request.status == 'declined' %}
                      <span class="badge bg-danger">Declined</span>
                    {% endif %}
                  </td>
                  <td>
                    <a href="{% url 'services:contact_request_detail' request.id %}" class="btn btn-sm btn-outline-primary">
                      View
                    </a>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
          <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
              {% if page_obj.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                  </a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                  </a>
                </li>
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% endif %}

              {% for i in page_obj.paginator.page_range %}
                {% if page_obj.number == i %}
                  <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                  <li class="page-item">
                    <a class="page-link" href="?page={{ i }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{{ i }}</a>
                  </li>
                {% endif %}
              {% endfor %}

              {% if page_obj.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                  </a>
                </li>
              {% endif %}
            </ul>
          </nav>
        {% endif %}
      {% else %}
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i> You haven't sent any contact requests yet.
        </div>
        <div class="text-center mt-4">
          <a href="{% url 'services:applicants' %}" class="btn btn-primary">
            <i class="bi bi-search"></i> Browse Applicants
          </a>
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
