{% extends 'common/base.html' %}
{% load i18n %}

{% block title %}Job Requirements - {{ offer.title }} - Smarch{% endblock %}

{% block extra_css %}
<style>
    .weight-slider {
        width: 100%;
    }
    .weight-display {
        font-weight: bold;
        color: #007bff;
    }
    .skill-input {
        border: 1px dashed #dee2e6;
        border-radius: 8px;
        padding: 10px;
        background-color: #f8f9fa;
    }
    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-gear text-primary"></i>
                        {% trans "Job Requirements" %}
                    </h1>
                    <p class="text-muted mb-0">{{ offer.title }}</p>
                </div>
                <div>
                    <a href="{% url 'services:ai_candidate_matching' offer.id %}" class="btn btn-outline-primary me-2">
                        <i class="bi bi-robot"></i> {% trans "View Matches" %}
                    </a>
                    <a href="{% url 'services:offers' %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> {% trans "Back to Offers" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="post">
        {% csrf_token %}
        
        <div class="row">
            <!-- Skills Requirements -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-tools"></i> {% trans "Skills Requirements" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="required_skills" class="form-label">{% trans "Required Skills" %}</label>
                            <textarea class="form-control skill-input" id="required_skills" name="required_skills" 
                                      rows="3" placeholder="Python, Django, JavaScript, React">{{ requirements.required_skills|join:", " }}</textarea>
                            <div class="help-text">{% trans "Separate skills with commas. These are must-have skills." %}</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="preferred_skills" class="form-label">{% trans "Preferred Skills" %}</label>
                            <textarea class="form-control skill-input" id="preferred_skills" name="preferred_skills" 
                                      rows="3" placeholder="AWS, Docker, PostgreSQL">{{ requirements.preferred_skills|join:", " }}</textarea>
                            <div class="help-text">{% trans "Nice-to-have skills that would be a bonus." %}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Experience & Location -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-briefcase"></i> {% trans "Experience & Location" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6">
                                <label for="min_experience_years" class="form-label">{% trans "Min Experience (years)" %}</label>
                                <input type="number" class="form-control" id="min_experience_years" 
                                       name="min_experience_years" value="{{ requirements.min_experience_years }}" min="0" max="20">
                            </div>
                            <div class="col-6">
                                <label for="max_experience_years" class="form-label">{% trans "Max Experience (years)" %}</label>
                                <input type="number" class="form-control" id="max_experience_years" 
                                       name="max_experience_years" value="{{ requirements.max_experience_years|default:'' }}" min="0" max="30">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="preferred_locations" class="form-label">{% trans "Preferred Locations" %}</label>
                            <textarea class="form-control" id="preferred_locations" name="preferred_locations" 
                                      rows="2" placeholder="New York, San Francisco, Remote">{{ requirements.preferred_locations|join:", " }}</textarea>
                            <div class="help-text">{% trans "Separate locations with commas. Leave empty for any location." %}</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remote_work_allowed" 
                                       name="remote_work_allowed" {% if requirements.remote_work_allowed %}checked{% endif %}>
                                <label class="form-check-label" for="remote_work_allowed">
                                    {% trans "Remote work allowed" %}
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="required_languages" class="form-label">{% trans "Required Languages" %}</label>
                            <input type="text" class="form-control" id="required_languages" name="required_languages" 
                                   value="{{ requirements.required_languages|join:', ' }}" placeholder="English, Spanish">
                            <div class="help-text">{% trans "Separate languages with commas." %}</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="education_level" class="form-label">{% trans "Education Level" %}</label>
                            <select class="form-select" id="education_level" name="education_level">
                                <option value="">{% trans "Any" %}</option>
                                <option value="high_school" {% if requirements.education_level == 'high_school' %}selected{% endif %}>{% trans "High School" %}</option>
                                <option value="bachelor" {% if requirements.education_level == 'bachelor' %}selected{% endif %}>{% trans "Bachelor's Degree" %}</option>
                                <option value="master" {% if requirements.education_level == 'master' %}selected{% endif %}>{% trans "Master's Degree" %}</option>
                                <option value="phd" {% if requirements.education_level == 'phd' %}selected{% endif %}>{% trans "PhD" %}</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scoring Weights -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-sliders"></i> {% trans "Scoring Weights" %}
                        </h5>
                        <small class="text-muted">{% trans "Adjust how important each factor is in candidate matching" %}</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="skills_weight" class="form-label">
                                    {% trans "Skills" %} (<span id="skills_weight_display" class="weight-display">{{ requirements.skills_weight }}%</span>)
                                </label>
                                <input type="range" class="form-range weight-slider" id="skills_weight" 
                                       name="skills_weight" min="0" max="100" value="{{ requirements.skills_weight }}"
                                       oninput="updateWeightDisplay('skills_weight', this.value)">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="experience_weight" class="form-label">
                                    {% trans "Experience" %} (<span id="experience_weight_display" class="weight-display">{{ requirements.experience_weight }}%</span>)
                                </label>
                                <input type="range" class="form-range weight-slider" id="experience_weight" 
                                       name="experience_weight" min="0" max="100" value="{{ requirements.experience_weight }}"
                                       oninput="updateWeightDisplay('experience_weight', this.value)">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="location_weight" class="form-label">
                                    {% trans "Location" %} (<span id="location_weight_display" class="weight-display">{{ requirements.location_weight }}%</span>)
                                </label>
                                <input type="range" class="form-range weight-slider" id="location_weight" 
                                       name="location_weight" min="0" max="100" value="{{ requirements.location_weight }}"
                                       oninput="updateWeightDisplay('location_weight', this.value)">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="language_weight" class="form-label">
                                    {% trans "Languages" %} (<span id="language_weight_display" class="weight-display">{{ requirements.language_weight }}%</span>)
                                </label>
                                <input type="range" class="form-range weight-slider" id="language_weight" 
                                       name="language_weight" min="0" max="100" value="{{ requirements.language_weight }}"
                                       oninput="updateWeightDisplay('language_weight', this.value)">
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>{% trans "Total Weight:" %}</strong> <span id="total_weight" class="weight-display">100%</span>
                            <div class="help-text">{% trans "Weights don't need to add up to 100%. They represent relative importance." %}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-outline-secondary" onclick="resetToDefaults()">
                        <i class="bi bi-arrow-clockwise"></i> {% trans "Reset to Defaults" %}
                    </button>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i> {% trans "Save Requirements" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateWeightDisplay(fieldName, value) {
    document.getElementById(fieldName + '_display').textContent = value + '%';
    updateTotalWeight();
}

function updateTotalWeight() {
    const skills = parseInt(document.getElementById('skills_weight').value);
    const experience = parseInt(document.getElementById('experience_weight').value);
    const location = parseInt(document.getElementById('location_weight').value);
    const language = parseInt(document.getElementById('language_weight').value);
    
    const total = skills + experience + location + language;
    document.getElementById('total_weight').textContent = total + '%';
}

function resetToDefaults() {
    document.getElementById('skills_weight').value = 40;
    document.getElementById('experience_weight').value = 30;
    document.getElementById('location_weight').value = 20;
    document.getElementById('language_weight').value = 10;
    
    updateWeightDisplay('skills_weight', 40);
    updateWeightDisplay('experience_weight', 30);
    updateWeightDisplay('location_weight', 20);
    updateWeightDisplay('language_weight', 10);
}

document.addEventListener('DOMContentLoaded', function() {
    updateTotalWeight();
});
</script>
{% endblock %}
