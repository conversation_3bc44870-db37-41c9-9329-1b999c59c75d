{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Services" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <!-- Hero Section -->
  <div class="bg-light rounded-3 p-5 mb-5 text-center">
    <h1 class="display-4 fw-bold">{% trans "Our Services" %}</h1>
    <p class="lead mb-4">{% trans "Discover how Smarch can help your business connect with talent" %}</p>
    <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
      <a href="{% url 'services:dashboard' %}" class="btn btn-primary btn-lg px-4 gap-3">{% trans "Hiring Partner Dashboard" %}</a>
      <a href="{% url 'users:service_signup' %}" class="btn btn-outline-secondary btn-lg px-4">{% trans "Register as Hiring Partner" %}</a>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="card mb-4">
    <div class="card-body">
      <form method="get" action="{% url 'services:list' %}" class="row g-3">
        <div class="col-md-4">
          <label for="q" class="form-label">{% trans "Search" %}</label>
          <input type="text" class="form-control" id="q" name="q" placeholder="{% trans 'Search by title, description, etc.' %}" value="{{ search_query }}">
        </div>
        <div class="col-md-3">
          <label for="industry" class="form-label">{% trans "Industry" %}</label>
          <select class="form-select" id="industry" name="industry">
            <option value="">{% trans "All Industries" %}</option>
            {% for industry in industries %}
              <option value="{{ industry }}" {% if selected_industry == industry %}selected{% endif %}>{{ industry }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-3">
          <label for="location" class="form-label">{% trans "Location" %}</label>
          <select class="form-select" id="location" name="location">
            <option value="">{% trans "All Locations" %}</option>
            {% for location in locations %}
              <option value="{{ location }}" {% if selected_location == location %}selected{% endif %}>{{ location }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-2">
          <label for="is_premium" class="form-label">{% trans "Premium Only" %}</label>
          <div class="form-check form-switch mt-2">
            <input class="form-check-input" type="checkbox" id="is_premium" name="is_premium" value="true" {% if is_premium %}checked{% endif %}>
            <label class="form-check-label" for="is_premium">{% trans "Show Premium" %}</label>
          </div>
        </div>
        <div class="col-md-6">
          <label class="form-label">{% trans "Price Range" %}</label>
          <div class="row">
            <div class="col">
              <div class="input-group">
                <span class="input-group-text">$</span>
                <input type="number" class="form-control" id="price_min" name="price_min" placeholder="{% trans 'Min' %}" value="{{ price_min }}">
              </div>
            </div>
            <div class="col">
              <div class="input-group">
                <span class="input-group-text">$</span>
                <input type="number" class="form-control" id="price_max" name="price_max" placeholder="{% trans 'Max' %}" value="{{ price_max }}">
              </div>
            </div>
          </div>
        </div>
        <div class="col-12 text-end">
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-search"></i> {% trans "Search" %}
          </button>
          <a href="{% url 'services:list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-x-circle"></i> {% trans "Clear Filters" %}
          </a>
        </div>
      </form>
    </div>
  </div>

  <!-- Results Summary -->
  <div class="row mb-3">
    <div class="col">
      <h5>{{ total_offers }} {% trans "Services Found" %}</h5>
    </div>
  </div>

  <!-- Service Plans -->
  <div class="row mb-5">
    <div class="col-12 text-center mb-4">
      <h2 class="display-5">{% trans "Subscription Plans" %}</h2>
      <p class="lead">{% trans "Choose the plan that best suits your needs" %}</p>
    </div>
  </div>

  <div class="row row-cols-1 row-cols-md-3 g-4 mb-5">
    <!-- Basic Plan -->
    <div class="col">
      <div class="card h-100 border-0 shadow-sm">
        <div class="card-header text-center bg-light py-3">
          <h3 class="my-0 fw-normal">{% trans "Basic" %}</h3>
        </div>
        <div class="card-body">
          <h1 class="card-title pricing-card-title text-center">$99<small class="text-muted fw-light">/mo</small></h1>
          <ul class="list-unstyled mt-3 mb-4">
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Access to basic applicant search" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Up to 10 contact requests per month" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Post up to 5 job ads" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Basic company profile" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-x-circle-fill text-danger me-2"></i>
              {% trans "Advanced filtering" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-x-circle-fill text-danger me-2"></i>
              {% trans "Priority matching" %}
            </li>
          </ul>
          <div class="d-grid">
            {% if user.is_authenticated and user.user_type == 'service' %}
              <a href="{% url 'services:payment' %}?plan=basic" class="btn btn-outline-primary">{% trans "Subscribe" %}</a>
            {% else %}
              <a href="{% url 'users:service_signup' %}" class="btn btn-outline-primary">{% trans "Sign Up & Subscribe" %}</a>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Standard Plan -->
    <div class="col">
      <div class="card h-100 border-primary border-2 shadow">
        <div class="card-header text-center bg-primary text-white py-3">
          <h3 class="my-0 fw-bold">{% trans "Standard" %}</h3>
          <span class="badge bg-warning text-dark position-absolute top-0 start-50 translate-middle">{% trans "Most Popular" %}</span>
        </div>
        <div class="card-body">
          <h1 class="card-title pricing-card-title text-center">$199<small class="text-muted fw-light">/mo</small></h1>
          <ul class="list-unstyled mt-3 mb-4">
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Full applicant database access" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Up to 30 contact requests per month" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Post up to 15 job ads" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Enhanced company profile" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Advanced filtering" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-x-circle-fill text-danger me-2"></i>
              {% trans "Priority matching" %}
            </li>
          </ul>
          <div class="d-grid">
            {% if user.is_authenticated and user.user_type == 'service' %}
              <a href="{% url 'services:payment' %}?plan=standard" class="btn btn-primary">{% trans "Subscribe" %}</a>
            {% else %}
              <a href="{% url 'users:service_signup' %}" class="btn btn-primary">{% trans "Sign Up & Subscribe" %}</a>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Premium Plan -->
    <div class="col">
      <div class="card h-100 border-0 shadow-sm">
        <div class="card-header text-center bg-light py-3">
          <h3 class="my-0 fw-normal">{% trans "Premium" %}</h3>
        </div>
        <div class="card-body">
          <h1 class="card-title pricing-card-title text-center">$299<small class="text-muted fw-light">/mo</small></h1>
          <ul class="list-unstyled mt-3 mb-4">
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Full applicant database access" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Unlimited contact requests" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Post up to 50 job ads" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Premium company profile" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Advanced filtering & analytics" %}
            </li>
            <li class="mb-2">
              <i class="bi bi-check-circle-fill text-success me-2"></i>
              {% trans "Priority matching & support" %}
            </li>
          </ul>
          <div class="d-grid">
            {% if user.is_authenticated and user.user_type == 'service' %}
              <a href="{% url 'services:payment' %}?plan=premium" class="btn btn-outline-primary">{% trans "Subscribe" %}</a>
            {% else %}
              <a href="{% url 'users:service_signup' %}" class="btn btn-outline-primary">{% trans "Sign Up & Subscribe" %}</a>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enterprise Plan -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card border-0 shadow-sm bg-light">
        <div class="card-body p-4">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h3 class="mb-2">{% trans "Enterprise Plan" %}</h3>
              <p class="mb-0">{% trans "For larger organizations with custom needs. Includes unlimited job postings, dedicated account manager, custom integrations, and more." %}</p>
            </div>
            <div class="col-md-4 text-md-end mt-3 mt-md-0">
              <a href="{% url 'core:about' %}?inquiry_type=enterprise" class="btn btn-dark">{% trans "Contact Us" %}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Active Offers -->
  <div class="row mb-4">
    <div class="col-12">
      <h2 class="border-bottom pb-2">{% trans "Active Offers" %}</h2>
    </div>
  </div>

  <div class="row row-cols-1 row-cols-md-2 g-4">
    {% for offer in page_obj %}
      <div class="col">
        <div class="card h-100 border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <h5 class="card-title">{{ offer.title }}</h5>
              <h6>
                {% if offer.is_premium %}
                  <span class="badge bg-warning text-dark">{% trans "Premium" %}</span>
                {% endif %}
              </h6>
            </div>
            <h6 class="card-subtitle mb-2 text-muted">{{ offer.hiring_partner.company_name }}</h6>
            <p class="card-text">{{ offer.description }}</p>
            <div class="d-flex justify-content-between align-items-center mt-3">
              <div>
                <span class="fs-5 fw-bold">${{ offer.price }}</span>
              </div>
              <div>
                <a href="#" class="btn btn-sm btn-outline-primary">{% trans "View Details" %}</a>
                <a href="{% url 'services:contact_request' %}?offer={{ offer.id }}" class="btn btn-sm btn-primary">{% trans "Contact" %}</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% empty %}
      <div class="col-12">
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i>
          {% trans "No offers are currently available. Please check back later." %}
        </div>
      </div>
    {% endfor %}
  </div>

  <!-- Pagination -->
  {% if page_obj.has_other_pages %}
    <div class="row mt-4">
      <div class="col-12">
        <nav aria-label="Page navigation">
          <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
              <li class="page-item">
                <a class="page-link" href="?{% if search_query %}q={{ search_query }}&{% endif %}{% if selected_industry %}industry={{ selected_industry }}&{% endif %}{% if selected_location %}location={{ selected_location }}&{% endif %}{% if price_min %}price_min={{ price_min }}&{% endif %}{% if price_max %}price_max={{ price_max }}&{% endif %}{% if is_premium %}is_premium=true&{% endif %}page={{ page_obj.previous_page_number }}" aria-label="Previous">
                  <span aria-hidden="true">&laquo;</span>
                </a>
              </li>
            {% else %}
              <li class="page-item disabled">
                <a class="page-link" href="#" aria-label="Previous">
                  <span aria-hidden="true">&laquo;</span>
                </a>
              </li>
            {% endif %}

            {% for num in page_obj.paginator.page_range %}
              {% if page_obj.number == num %}
                <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
              {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                  <a class="page-link" href="?{% if search_query %}q={{ search_query }}&{% endif %}{% if selected_industry %}industry={{ selected_industry }}&{% endif %}{% if selected_location %}location={{ selected_location }}&{% endif %}{% if price_min %}price_min={{ price_min }}&{% endif %}{% if price_max %}price_max={{ price_max }}&{% endif %}{% if is_premium %}is_premium=true&{% endif %}page={{ num }}">{{ num }}</a>
                </li>
              {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
              <li class="page-item">
                <a class="page-link" href="?{% if search_query %}q={{ search_query }}&{% endif %}{% if selected_industry %}industry={{ selected_industry }}&{% endif %}{% if selected_location %}location={{ selected_location }}&{% endif %}{% if price_min %}price_min={{ price_min }}&{% endif %}{% if price_max %}price_max={{ price_max }}&{% endif %}{% if is_premium %}is_premium=true&{% endif %}page={{ page_obj.next_page_number }}" aria-label="Next">
                  <span aria-hidden="true">&raquo;</span>
                </a>
              </li>
            {% else %}
              <li class="page-item disabled">
                <a class="page-link" href="#" aria-label="Next">
                  <span aria-hidden="true">&raquo;</span>
                </a>
              </li>
            {% endif %}
          </ul>
        </nav>
      </div>
    </div>
  {% endif %}

  <!-- Testimonials -->
  <div class="row mt-5">
    <div class="col-12 text-center mb-4">
      <h2 class="display-5">{% trans "What Our Clients Say" %}</h2>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4 mb-4">
      <div class="card h-100 border-0 shadow-sm">
        <div class="card-body">
          <div class="mb-3 text-warning">
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
          </div>
          <p class="card-text">"Smarch has revolutionized our recruitment process. We've been able to find qualified candidates quickly and efficiently."</p>
          <div class="d-flex align-items-center mt-3">
            <div class="flex-shrink-0">
              <img src="{% static 'images/testimonial1.jpg' %}" alt="Testimonial" class="rounded-circle" width="50" height="50">
            </div>
            <div class="flex-grow-1 ms-3">
              <h6 class="mb-0">Jane Smith</h6>
              <p class="text-muted mb-0">HR Director, TechCorp</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4 mb-4">
      <div class="card h-100 border-0 shadow-sm">
        <div class="card-body">
          <div class="mb-3 text-warning">
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-half"></i>
          </div>
          <p class="card-text">"The quality of candidates we've met through Smarch has exceeded our expectations. The platform is intuitive and powerful."</p>
          <div class="d-flex align-items-center mt-3">
            <div class="flex-shrink-0">
              <img src="{% static 'images/testimonial2.jpg' %}" alt="Testimonial" class="rounded-circle" width="50" height="50">
            </div>
            <div class="flex-grow-1 ms-3">
              <h6 class="mb-0">Michael Johnson</h6>
              <p class="text-muted mb-0">CEO, InnovateCo</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4 mb-4">
      <div class="card h-100 border-0 shadow-sm">
        <div class="card-body">
          <div class="mb-3 text-warning">
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
          </div>
          <p class="card-text">"Since partnering with Smarch, we've reduced our time-to-hire by 45%. Their service is worth every penny."</p>
          <div class="d-flex align-items-center mt-3">
            <div class="flex-shrink-0">
              <img src="{% static 'images/testimonial3.jpg' %}" alt="Testimonial" class="rounded-circle" width="50" height="50">
            </div>
            <div class="flex-grow-1 ms-3">
              <h6 class="mb-0">Sarah Williams</h6>
              <p class="text-muted mb-0">Recruitment Manager, Global Services</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- CTA Section -->
  <div class="p-5 bg-light rounded-3 text-center mt-5">
    <div class="container">
      <h2 class="display-5 fw-bold">{% trans "Ready to get started?" %}</h2>
      <p class="lead mb-4">{% trans "Join Smarch today and connect with the talent your business needs." %}</p>
      <a href="{% url 'users:service_signup' %}" class="btn btn-primary btn-lg px-4">{% trans "Sign Up Now" %}</a>
    </div>
  </div>
</div>
{% endblock %}