{% extends 'common/base.html' %}
{% load i18n %}

{% block title %}Passive Candidates - Smarch{% endblock %}

{% block extra_css %}
<style>
    .candidate-card {
        transition: transform 0.2s, box-shadow 0.2s;
        border-left: 4px solid #dee2e6;
    }
    .candidate-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .candidate-card.discovered { border-left-color: #6c757d; }
    .candidate-card.contacted { border-left-color: #17a2b8; }
    .candidate-card.engaged { border-left-color: #ffc107; }
    .candidate-card.qualified { border-left-color: #28a745; }
    .candidate-card.converted { border-left-color: #007bff; }
    .candidate-card.not_responsive { border-left-color: #dc3545; }
    .candidate-card.declined { border-left-color: #6f42c1; }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
    }
    .status-discovered { background-color: #6c757d; color: white; }
    .status-contacted { background-color: #17a2b8; color: white; }
    .status-engaged { background-color: #ffc107; color: #212529; }
    .status-qualified { background-color: #28a745; color: white; }
    .status-converted { background-color: #007bff; color: white; }
    .status-not_responsive { background-color: #dc3545; color: white; }
    .status-declined { background-color: #6f42c1; color: white; }
    
    .interest-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.5rem;
    }
    .interest-unknown { background-color: #6c757d; }
    .interest-not_interested { background-color: #dc3545; }
    .interest-slightly_interested { background-color: #ffc107; }
    .interest-interested { background-color: #28a745; }
    .interest-very_interested { background-color: #007bff; }
    
    .source-icon {
        width: 20px;
        height: 20px;
        margin-right: 0.5rem;
    }
    
    .filter-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-people text-primary"></i>
                        {% trans "Passive Candidates" %}
                    </h1>
                    <p class="text-muted mb-0">{{ candidates.paginator.count }} {% trans "candidates discovered" %}</p>
                </div>
                <div>
                    <a href="{% url 'services:add_passive_candidate' %}" class="btn btn-primary">
                        <i class="bi bi-plus-lg"></i> {% trans "Add Candidate" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filter-card">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">{% trans "Search" %}</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ current_filters.search }}" placeholder="Name, email, company...">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">{% trans "Status" %}</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">{% trans "All Statuses" %}</option>
                            {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if current_filters.status == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="source" class="form-label">{% trans "Source" %}</label>
                        <select class="form-select" id="source" name="source">
                            <option value="">{% trans "All Sources" %}</option>
                            {% for value, label in source_choices %}
                            <option value="{{ value }}" {% if current_filters.source == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="interest" class="form-label">{% trans "Interest Level" %}</label>
                        <select class="form-select" id="interest" name="interest">
                            <option value="">{% trans "All Levels" %}</option>
                            {% for value, label in interest_choices %}
                            <option value="{{ value }}" {% if current_filters.interest == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="bi bi-search"></i> {% trans "Filter" %}
                        </button>
                        <a href="{% url 'services:passive_candidates_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> {% trans "Clear" %}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Candidates Grid -->
    <div class="row">
        {% for candidate in candidates %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card candidate-card {{ candidate.status }} h-100">
                <div class="card-body">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">
                                {{ candidate.full_name }}
                            </h6>
                            <p class="text-muted small mb-0">
                                {{ candidate.current_position|default:"Position not specified" }}
                            </p>
                            <p class="text-muted small">
                                <i class="bi bi-building"></i> {{ candidate.current_company|default:"Company not specified" }}
                            </p>
                        </div>
                        <div class="text-end">
                            <span class="status-badge status-{{ candidate.status }}">
                                {{ candidate.get_status_display }}
                            </span>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div class="mb-3">
                        <div class="small">
                            <div><i class="bi bi-envelope"></i> {{ candidate.email }}</div>
                            {% if candidate.phone %}
                            <div><i class="bi bi-telephone"></i> {{ candidate.phone }}</div>
                            {% endif %}
                            {% if candidate.location %}
                            <div><i class="bi bi-geo-alt"></i> {{ candidate.location }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Interest & Experience -->
                    <div class="row mb-3">
                        <div class="col-6">
                            <div class="small text-muted">{% trans "Interest Level" %}</div>
                            <div>
                                <span class="interest-indicator interest-{{ candidate.interest_level }}"></span>
                                {{ candidate.get_interest_level_display }}
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="small text-muted">{% trans "Experience" %}</div>
                            <div>{{ candidate.experience_years }} {% trans "years" %}</div>
                        </div>
                    </div>

                    <!-- Skills -->
                    {% if candidate.skills %}
                    <div class="mb-3">
                        <div class="small text-muted mb-1">{% trans "Skills" %}</div>
                        <div>
                            {% for skill in candidate.skills|slice:":3" %}
                                <span class="badge bg-light text-dark me-1">{{ skill }}</span>
                            {% endfor %}
                            {% if candidate.skills|length > 3 %}
                                <span class="small text-muted">+{{ candidate.skills|length|add:"-3" }} more</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Source & Discovery -->
                    <div class="mb-3">
                        <div class="small text-muted">
                            {% trans "Discovered via" %} {{ candidate.get_discovery_source_display }}
                            {% if candidate.created_at %}
                                • {{ candidate.created_at|date:"M d, Y" }}
                            {% endif %}
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-sm flex-grow-1" onclick="viewCandidate({{ candidate.id }})">
                            <i class="bi bi-eye"></i> {% trans "View" %}
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="startOutreach({{ candidate.id }})">
                            <i class="bi bi-envelope"></i>
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="updateInterest({{ candidate.id }})">
                            <i class="bi bi-heart"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-people display-1 text-muted"></i>
                    <h4 class="mt-3">{% trans "No passive candidates found" %}</h4>
                    <p class="text-muted">
                        {% trans "Start building your passive candidate pipeline by adding candidates you discover." %}
                    </p>
                    <a href="{% url 'services:add_passive_candidate' %}" class="btn btn-primary">
                        <i class="bi bi-plus-lg"></i> {% trans "Add Your First Candidate" %}
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if candidates.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Candidates pagination">
                <ul class="pagination justify-content-center">
                    {% if candidates.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ candidates.previous_page_number }}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.source %}&source={{ current_filters.source }}{% endif %}{% if current_filters.interest %}&interest={{ current_filters.interest }}{% endif %}">
                                {% trans "Previous" %}
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for num in candidates.paginator.page_range %}
                        {% if candidates.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > candidates.number|add:'-3' and num < candidates.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.source %}&source={{ current_filters.source }}{% endif %}{% if current_filters.interest %}&interest={{ current_filters.interest }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if candidates.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ candidates.next_page_number }}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.source %}&source={{ current_filters.source }}{% endif %}{% if current_filters.interest %}&interest={{ current_filters.interest }}{% endif %}">
                                {% trans "Next" %}
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewCandidate(candidateId) {
    // TODO: Implement candidate detail view
    alert('Candidate detail view - Coming soon!');
}

function startOutreach(candidateId) {
    // TODO: Implement outreach sequence
    alert('Outreach sequence - Coming soon!');
}

function updateInterest(candidateId) {
    // TODO: Implement interest level update
    alert('Interest level update - Coming soon!');
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Passive candidates interface loaded');
});
</script>
{% endblock %}
