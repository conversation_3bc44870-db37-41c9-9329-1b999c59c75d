{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Payment" %} | Smarch{% endblock %}

{% block extra_css %}
<style>
  .StripeElement {
    box-sizing: border-box;
    height: 40px;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: white;
    transition: box-shadow 150ms ease, border 150ms ease;
  }

  .StripeElement--focus {
    box-shadow: 0 1px 3px 0 #cfd7df;
  }

  .StripeElement--invalid {
    border-color: #fa755a;
  }

  .StripeElement--webkit-autofill {
    background-color: #fefde5 !important;
  }

  #payment-form .form-control {
    height: 40px;
    padding: 10px 12px;
  }

  #payment-form .form-group {
    margin-bottom: 16px;
  }

  #card-errors {
    color: #fa755a;
    text-align: left;
    font-size: 13px;
    line-height: 17px;
    margin-top: 12px;
  }

  #spinner {
    display: none;
  }

  .payment-selected {
    border-color: #0d6efd !important;
    background-color: rgba(13, 110, 253, 0.05);
  }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <!-- Header -->
      <div class="text-center mb-5">
        <h1 class="display-4 fw-bold">{% trans "Complete Your Hiring Partner Registration" %}</h1>
        <p class="lead">{% trans "Choose your subscription plan to access our hiring tools" %}</p>
        <div class="alert alert-success">
          <p class="mb-0"><i class="bi bi-check-circle-fill me-2"></i> {% trans "Your hiring partner profile has been created successfully!" %}</p>
          <p class="mb-0 mt-2">{% trans "Now select a subscription plan to start connecting with applicants." %}</p>
        </div>
      </div>

      <!-- Subscription Selection -->
      <div class="card mb-5 border-0 shadow-sm">
        <div class="card-header bg-light">
          <h4 class="mb-0">{% trans "1. Select Your Plan" %}</h4>
        </div>
        <div class="card-body">
          <div class="row">
            <!-- Basic Plan -->
            <div class="col-md-4 mb-3">
              <div id="plan-basic" class="card h-100 border {% if selected_plan == 'basic' %}payment-selected{% endif %}" data-plan="basic">
                <div class="card-header text-center bg-light py-3">
                  <h5 class="my-0 fw-normal">{% trans "Basic" %}</h5>
                </div>
                <div class="card-body text-center">
                  <h2 class="card-title pricing-card-title">$99<small class="text-muted fw-light">/mo</small></h2>
                  <ul class="list-unstyled mt-3 mb-4 text-start">
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Basic applicant search" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "10 contact requests/month" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Post up to 5 job ads" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Basic company profile" %}
                    </li>
                  </ul>
                </div>
                <div class="card-footer bg-transparent border-top-0 text-center">
                  <button type="button" class="btn btn-outline-primary select-plan w-100" data-plan="basic">
                    {% if selected_plan == 'basic' %}
                      <i class="bi bi-check-circle-fill me-2"></i>{% trans "Selected" %}
                    {% else %}
                      {% trans "Select" %}
                    {% endif %}
                  </button>
                </div>
              </div>
            </div>

            <!-- Standard Plan -->
            <div class="col-md-4 mb-3">
              <div id="plan-standard" class="card h-100 border-primary {% if selected_plan == 'standard' %}payment-selected{% endif %}" data-plan="standard">
                <div class="card-header text-center bg-primary text-white py-3">
                  <h5 class="my-0 fw-bold">{% trans "Standard" %}</h5>
                  <span class="badge bg-warning text-dark position-absolute top-0 start-50 translate-middle">{% trans "Popular" %}</span>
                </div>
                <div class="card-body text-center">
                  <h2 class="card-title pricing-card-title">$199<small class="text-muted fw-light">/mo</small></h2>
                  <ul class="list-unstyled mt-3 mb-4 text-start">
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Full applicant database" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "30 contact requests/month" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Post up to 15 job ads" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Enhanced company profile" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Advanced filtering" %}
                    </li>
                  </ul>
                </div>
                <div class="card-footer bg-transparent border-top-0 text-center">
                  <button type="button" class="btn btn-primary select-plan w-100" data-plan="standard">
                    {% if selected_plan == 'standard' %}
                      <i class="bi bi-check-circle-fill me-2"></i>{% trans "Selected" %}
                    {% else %}
                      {% trans "Select" %}
                    {% endif %}
                  </button>
                </div>
              </div>
            </div>

            <!-- Premium Plan -->
            <div class="col-md-4 mb-3">
              <div id="plan-premium" class="card h-100 border {% if selected_plan == 'premium' %}payment-selected{% endif %}" data-plan="premium">
                <div class="card-header text-center bg-light py-3">
                  <h5 class="my-0 fw-normal">{% trans "Premium" %}</h5>
                </div>
                <div class="card-body text-center">
                  <h2 class="card-title pricing-card-title">$299<small class="text-muted fw-light">/mo</small></h2>
                  <ul class="list-unstyled mt-3 mb-4 text-start">
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Full applicant database" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Unlimited contact requests" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Post up to 50 job ads" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Premium company profile" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Advanced filtering & analytics" %}
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle-fill text-success me-2"></i>
                      {% trans "Priority matching & support" %}
                    </li>
                  </ul>
                </div>
                <div class="card-footer bg-transparent border-top-0 text-center">
                  <button type="button" class="btn btn-outline-primary select-plan w-100" data-plan="premium">
                    {% if selected_plan == 'premium' %}
                      <i class="bi bi-check-circle-fill me-2"></i>{% trans "Selected" %}
                    {% else %}
                      {% trans "Select" %}
                    {% endif %}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Enterprise Plan -->
          <div class="mt-4 p-4 bg-light rounded">
            <div class="row align-items-center">
              <div class="col-md-8">
                <h4>{% trans "Enterprise Plan" %}</h4>
                <p class="mb-0">{% trans "For larger organizations with custom needs. Includes unlimited job postings, dedicated account manager, custom integrations, and more." %}</p>
              </div>
              <div class="col-md-4 text-md-end mt-3 mt-md-0">
                <a href="mailto:<EMAIL>" class="btn btn-outline-dark">{% trans "Contact Us" %}</a>
              </div>
            </div>
          </div>

          <input type="hidden" id="selected-plan" name="selected_plan" value="{{ selected_plan }}">
        </div>
      </div>

      <!-- Billing Details -->
      <div class="card mb-5 border-0 shadow-sm">
        <div class="card-header bg-light">
          <h4 class="mb-0">{% trans "2. Billing Information" %}</h4>
        </div>
        <div class="card-body">
          <form id="payment-form" class="needs-validation" novalidate>
            {% csrf_token %}

            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="first_name" class="form-label">{% trans "First Name" %}</label>
                <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user.first_name }}" required>
                <div class="invalid-feedback">
                  {% trans "First name is required" %}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <label for="last_name" class="form-label">{% trans "Last Name" %}</label>
                <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user.last_name }}" required>
                <div class="invalid-feedback">
                  {% trans "Last name is required" %}
                </div>
              </div>
            </div>

            <div class="mb-3">
              <label for="email" class="form-label">{% trans "Email" %}</label>
              <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
              <div class="invalid-feedback">
                {% trans "Valid email is required" %}
              </div>
            </div>

            <div class="mb-3">
              <label for="company" class="form-label">{% trans "Company Name" %}</label>
              <input type="text" class="form-control" id="company" name="company" value="{{ service_profile.company_name|default:'' }}" required>
              <div class="invalid-feedback">
                {% trans "Company name is required" %}
              </div>
            </div>

            <div class="mb-3">
              <label for="address" class="form-label">{% trans "Billing Address" %}</label>
              <input type="text" class="form-control" id="address" name="address" required>
              <div class="invalid-feedback">
                {% trans "Billing address is required" %}
              </div>
            </div>

            <div class="row">
              <div class="col-md-5 mb-3">
                <label for="country" class="form-label">{% trans "Country" %}</label>
                <select class="form-select" id="country" name="country" required>
                  <option value="">{% trans "Choose..." %}</option>
                  <option value="US">{% trans "United States" %}</option>
                  <option value="CA">{% trans "Canada" %}</option>
                  <option value="GB">{% trans "United Kingdom" %}</option>
                  <!-- Add more countries as needed -->
                </select>
                <div class="invalid-feedback">
                  {% trans "Please select a valid country" %}
                </div>
              </div>

              <div class="col-md-4 mb-3">
                <label for="state" class="form-label">{% trans "State/Province" %}</label>
                <input type="text" class="form-control" id="state" name="state" required>
                <div class="invalid-feedback">
                  {% trans "State/Province is required" %}
                </div>
              </div>

              <div class="col-md-3 mb-3">
                <label for="zip" class="form-label">{% trans "Zip/Postal Code" %}</label>
                <input type="text" class="form-control" id="zip" name="zip" required>
                <div class="invalid-feedback">
                  {% trans "Zip code is required" %}
                </div>
              </div>
            </div>

            <hr class="my-4">

            <h5 class="mb-3">{% trans "3. Payment Method" %}</h5>

            <div class="mb-3">
              <label for="card-element" class="form-label">{% trans "Credit or Debit Card" %}</label>
              <div id="card-element" class="form-control">
                <!-- Stripe Element will be inserted here -->
              </div>
              <div id="card-errors" role="alert"></div>
            </div>

            <div class="form-check mb-4">
              <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
              <label class="form-check-label" for="terms">
                {% blocktrans %}I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>{% endblocktrans %}
              </label>
              <div class="invalid-feedback">
                {% trans "You must agree before submitting" %}
              </div>
            </div>

            <div class="alert alert-primary" role="alert">
              <div class="d-flex align-items-center">
                <i class="bi bi-info-circle-fill me-2"></i>
                <div>
                  <strong>{% trans "Subscription Summary" %}:</strong>
                  <span id="plan-summary">
                    {% if selected_plan == 'basic' %}
                      {% blocktrans %}Basic Plan - $99/month{% endblocktrans %}
                    {% elif selected_plan == 'standard' %}
                      {% blocktrans %}Standard Plan - $199/month{% endblocktrans %}
                    {% elif selected_plan == 'premium' %}
                      {% blocktrans %}Premium Plan - $299/month{% endblocktrans %}
                    {% else %}
                      {% trans "Please select a plan" %}
                    {% endif %}
                  </span>
                </div>
              </div>
            </div>

            <hr class="my-4">

            <button id="submit-button" class="btn btn-primary btn-lg w-100" type="submit">
              <span id="spinner" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              {% trans "Subscribe Now" %}
            </button>

            <p class="text-muted text-center mt-3">
              <small>
                <i class="bi bi-lock-fill me-1"></i>
                {% trans "Your payment is processed securely through Stripe" %}
              </small>
            </p>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Stripe JS -->
<script src="https://js.stripe.com/v3/"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize Stripe
    const stripe = Stripe('{{ stripe_public_key }}');
    const elements = stripe.elements();

    // Create an instance of the card Element
    const cardElement = elements.create('card', {
      style: {
        base: {
          color: '#32325d',
          fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
          fontSmoothing: 'antialiased',
          fontSize: '16px',
          '::placeholder': {
            color: '#aab7c4'
          }
        },
        invalid: {
          color: '#fa755a',
          iconColor: '#fa755a'
        }
      }
    });

    // Add an instance of the card Element into the `card-element` div
    cardElement.mount('#card-element');

    // Handle real-time validation errors from the card Element
    cardElement.addEventListener('change', function(event) {
      const displayError = document.getElementById('card-errors');
      if (event.error) {
        displayError.textContent = event.error.message;
      } else {
        displayError.textContent = '';
      }
    });

    // Handle form submission
    const form = document.getElementById('payment-form');
    form.addEventListener('submit', function(event) {
      event.preventDefault();

      // Validate the form
      if (!form.checkValidity()) {
        event.stopPropagation();
        form.classList.add('was-validated');
        return;
      }

      // Check if a plan is selected
      const selectedPlan = document.getElementById('selected-plan').value;
      if (!selectedPlan) {
        alert('{% trans "Please select a subscription plan" %}');
        return;
      }

      // Disable the submit button to prevent repeated clicks
      document.getElementById('submit-button').disabled = true;
      document.getElementById('spinner').style.display = 'inline-block';

      // Create a token
      stripe.createToken(cardElement).then(function(result) {
        if (result.error) {
          // Inform the user if there was an error
          const errorElement = document.getElementById('card-errors');
          errorElement.textContent = result.error.message;
          document.getElementById('submit-button').disabled = false;
          document.getElementById('spinner').style.display = 'none';
        } else {
          // Send the token to your server
          stripeTokenHandler(result.token);
        }
      });
    });

    // Submit the form with the token ID
    function stripeTokenHandler(token) {
      // Insert the token ID into the form so it gets submitted to the server
      const form = document.getElementById('payment-form');
      const hiddenInput = document.createElement('input');
      hiddenInput.setAttribute('type', 'hidden');
      hiddenInput.setAttribute('name', 'stripeToken');
      hiddenInput.setAttribute('value', token.id);
      form.appendChild(hiddenInput);

      // Add selected plan to the form
      const planInput = document.createElement('input');
      planInput.setAttribute('type', 'hidden');
      planInput.setAttribute('name', 'plan');
      planInput.setAttribute('value', document.getElementById('selected-plan').value);
      form.appendChild(planInput);

      // Submit the form
      form.submit();
    }

    // Plan selection
    const planButtons = document.querySelectorAll('.select-plan');
    planButtons.forEach(button => {
      button.addEventListener('click', function() {
        const plan = this.getAttribute('data-plan');
        document.getElementById('selected-plan').value = plan;

        // Update UI
        document.querySelectorAll('[data-plan]').forEach(element => {
          if (element.getAttribute('data-plan') === plan) {
            element.classList.add('payment-selected');
          } else {
            element.classList.remove('payment-selected');
          }
        });

        // Update buttons
        planButtons.forEach(btn => {
          const btnPlan = btn.getAttribute('data-plan');
          if (btnPlan === plan) {
            if (btnPlan === 'standard') {
              btn.classList.remove('btn-outline-primary');
              btn.classList.add('btn-primary');
            } else {
              btn.classList.remove('btn-outline-primary');
              btn.classList.add('btn-primary');
            }
            btn.innerHTML = '<i class="bi bi-check-circle-fill me-2"></i>{% trans "Selected" %}';
          } else {
            if (btnPlan === 'standard') {
              btn.classList.remove('btn-primary');
              btn.classList.add('btn-outline-primary');
            } else {
              btn.classList.remove('btn-primary');
              btn.classList.add('btn-outline-primary');
            }
            btn.textContent = '{% trans "Select" %}';
          }
        });

        // Update subscription summary
        const planSummary = document.getElementById('plan-summary');
        if (plan === 'basic') {
          planSummary.textContent = '{% trans "Basic Plan - $99/month" %}';
        } else if (plan === 'standard') {
          planSummary.textContent = '{% trans "Standard Plan - $199/month" %}';
        } else if (plan === 'premium') {
          planSummary.textContent = '{% trans "Premium Plan - $299/month" %}';
        }
      });
    });
  });
</script>
{% endblock %}