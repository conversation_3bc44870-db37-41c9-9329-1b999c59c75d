{% extends "common/base.html" %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Hiring Partner Profile" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <!-- Sidebar -->
    <div class="col-lg-3 mb-4">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">{% trans "Profile Completion" %}</h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            <div class="position-relative d-inline-block">
              <svg width="120" height="120" viewBox="0 0 120 120">
                <circle cx="60" cy="60" r="54" fill="none" stroke="#e9ecef" stroke-width="12" />
                <circle cx="60" cy="60" r="54" fill="none" stroke="#0d6efd" stroke-width="12"
                  stroke-dasharray="339.292" stroke-dashoffset="{{ 339.292|add:(-339.292|mul:completion_percentage|div:100) }}" />
              </svg>
              <div class="position-absolute top-50 start-50 translate-middle">
                <h2 class="mb-0">{{ completion_percentage }}%</h2>
              </div>
            </div>
          </div>
          
          <div class="mb-3">
            <h6>{% trans "Profile Status" %}</h6>
            {% if completion_percentage < 50 %}
              <div class="alert alert-warning mb-0">
                <i class="bi bi-exclamation-triangle me-2"></i>
                {% trans "Your profile needs more information to attract applicants." %}
              </div>
            {% elif completion_percentage < 80 %}
              <div class="alert alert-info mb-0">
                <i class="bi bi-info-circle me-2"></i>
                {% trans "Your profile is looking good, but could use more details." %}
              </div>
            {% else %}
              <div class="alert alert-success mb-0">
                <i class="bi bi-check-circle me-2"></i>
                {% trans "Your profile is complete and ready to attract applicants!" %}
              </div>
            {% endif %}
          </div>
          
          <div class="d-grid">
            <a href="{% url 'services:dashboard' %}" class="btn btn-outline-primary">
              <i class="bi bi-speedometer2 me-2"></i>{% trans "Back to Dashboard" %}
            </a>
          </div>
        </div>
      </div>
      
      <div class="card shadow mt-4">
        <div class="card-header bg-light">
          <h5 class="mb-0">{% trans "Quick Links" %}</h5>
        </div>
        <div class="card-body p-0">
          <div class="list-group list-group-flush">
            <a href="{% url 'services:dashboard' %}" class="list-group-item list-group-item-action">
              <i class="bi bi-speedometer2 me-2"></i>{% trans "Dashboard" %}
            </a>
            <a href="{% url 'services:subscription' %}" class="list-group-item list-group-item-action">
              <i class="bi bi-credit-card me-2"></i>{% trans "Subscription" %}
            </a>
            <a href="{% url 'services:offers' %}" class="list-group-item list-group-item-action">
              <i class="bi bi-briefcase me-2"></i>{% trans "My Offers" %}
            </a>
            <a href="{% url 'services:contact_requests' %}" class="list-group-item list-group-item-action">
              <i class="bi bi-envelope me-2"></i>{% trans "Contact Requests" %}
            </a>
            <a href="{% url 'services:account' %}" class="list-group-item list-group-item-action">
              <i class="bi bi-gear me-2"></i>{% trans "Account Settings" %}
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Main Content -->
    <div class="col-lg-9">
      <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h1 class="h3 mb-0">{% trans "Hiring Partner Profile" %}</h1>
          <a href="{% url 'users:service_profile_detail' profile.id %}" class="btn btn-light btn-sm">
            <i class="bi bi-eye me-1"></i>{% trans "View Public Profile" %}
          </a>
        </div>
        <div class="card-body">
          <div class="alert alert-info mb-4">
            <i class="bi bi-info-circle me-2"></i>
            <p>{% trans "Keep your profile up to date to attract the right candidates. A complete profile increases your visibility in search results." %}</p>
          </div>
          
          <form method="POST" enctype="multipart/form-data" class="hiring-partner-form">
            {% csrf_token %}
            
            <div class="row mb-4">
              <div class="col-md-3">
                <div class="text-center mb-3">
                  {% if profile.profile_image %}
                    <img src="{{ profile.profile_image.url }}" alt="Company Logo" class="img-fluid rounded mb-2" style="max-height: 150px;">
                  {% else %}
                    <div class="bg-light rounded d-flex align-items-center justify-content-center mb-2" style="height: 150px; width: 100%;">
                      <i class="bi bi-building fs-1 text-secondary"></i>
                    </div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-9">
                <h4 class="mb-3">{% trans "Basic Information" %}</h4>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    {{ form.company_name|as_crispy_field }}
                  </div>
                  <div class="col-md-6 mb-3">
                    {{ form.company_size|as_crispy_field }}
                  </div>
                </div>
                <div class="mb-3">
                  {{ form.profile_image|as_crispy_field }}
                </div>
              </div>
            </div>
            
            <h4 class="border-bottom pb-2 mb-3">{% trans "Location Information" %}</h4>
            <div class="row mb-4">
              <div class="col-md-6 mb-3">
                {{ form.primary_location|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.other_locations|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.company_address|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.company_city|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.company_state|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.company_zip|as_crispy_field }}
              </div>
            </div>
            
            <h4 class="border-bottom pb-2 mb-3">{% trans "Company Details" %}</h4>
            <div class="row mb-4">
              <div class="col-md-6 mb-3">
                {{ form.branch|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.industry|as_crispy_field }}
              </div>
              <div class="col-12 mb-3">
                {{ form.company_description|as_crispy_field }}
              </div>
              <div class="col-12 mb-3">
                {{ form.company_needs|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.company_website|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.phone_number|as_crispy_field }}
              </div>
            </div>
            
            <h4 class="border-bottom pb-2 mb-3">{% trans "Employment Information" %}</h4>
            <div class="row mb-4">
              <div class="col-md-6 mb-3">
                {{ form.average_wages|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.contract_types|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.working_hours|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.work_location|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.job_title|as_crispy_field }}
              </div>
              <div class="col-md-6 mb-3">
                {{ form.services_offered|as_crispy_field }}
              </div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
              <a href="{% url 'services:dashboard' %}" class="btn btn-outline-secondary me-md-2">
                <i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-save me-1"></i> {% trans "Save Profile" %}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add any JavaScript for form validation or dynamic behavior here
  });
</script>
{% endblock %}
