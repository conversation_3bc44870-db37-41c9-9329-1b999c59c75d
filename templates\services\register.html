{% extends "common/base.html" %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Hiring Partner Registration" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <!-- Sidebar - Registration Steps -->
    <div class="col-lg-3 mb-4">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h5 class="card-title mb-0">Registration Process</h5>
        </div>
        <div class="card-body">
          <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex align-items-center">
              <span class="badge bg-primary rounded-circle me-2">1</span>
              Create Account
              <i class="bi bi-check-circle-fill text-success ms-auto"></i>
            </li>
            <li class="list-group-item d-flex align-items-center active">
              <span class="badge bg-primary rounded-circle me-2">2</span>
              Company Details
              <i class="bi bi-arrow-right ms-auto"></i>
            </li>
            <li class="list-group-item d-flex align-items-center text-muted">
              <span class="badge bg-secondary rounded-circle me-2">3</span>
              Subscription Plan
            </li>
            <li class="list-group-item d-flex align-items-center text-muted">
              <span class="badge bg-secondary rounded-circle me-2">4</span>
              Verification
            </li>
          </ul>

          <div class="mt-4">
            <p class="text-muted small">Need help? Contact our support team</p>
            <a href="#" class="btn btn-outline-primary btn-sm w-100">
              <i class="bi bi-question-circle me-1"></i> Get Help
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Registration Form -->
    <div class="col-lg-9">
      <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h1 class="h3 mb-0">{% trans "Hiring Partner Registration" %}</h1>
        </div>
        <div class="card-body">
          <div class="alert alert-info mb-4">
            <i class="bi bi-info-circle me-2"></i>
            <p>Welcome to SMARCH Hiring Partner Registration!</p>
            <p>Please provide your company details below. This information will help us understand your needs and match you with suitable candidates.</p>
            <p>All fields marked with an asterisk (*) are required.</p>
          </div>

          <form method="POST" enctype="multipart/form-data" class="hiring-partner-form">
            {% csrf_token %}

            <div class="accordion" id="registrationAccordion">
              <!-- Company Information -->
              <div class="accordion-item mb-3 border">
                <h2 class="accordion-header" id="headingCompany">
                  <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseCompany" aria-expanded="true" aria-controls="collapseCompany">
                    <i class="bi bi-building me-2"></i> Company Information
                  </button>
                </h2>
                <div id="collapseCompany" class="accordion-collapse collapse show" aria-labelledby="headingCompany" data-bs-parent="#registrationAccordion">
                  <div class="accordion-body">
                    <div class="row">
                      <div class="col-md-6">
                        {{ form.company_name|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.company_size|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.profile_image|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.company_description|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.company_needs|as_crispy_field }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Location & Contact -->
              <div class="accordion-item mb-3 border">
                <h2 class="accordion-header" id="headingLocation">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseLocation" aria-expanded="false" aria-controls="collapseLocation">
                    <i class="bi bi-geo-alt me-2"></i> Location & Contact
                  </button>
                </h2>
                <div id="collapseLocation" class="accordion-collapse collapse" aria-labelledby="headingLocation" data-bs-parent="#registrationAccordion">
                  <div class="accordion-body">
                    <div class="row">
                      <div class="col-md-12">
                        {{ form.primary_location|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.other_locations|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.phone_number|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.company_website|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.company_address|as_crispy_field }}
                      </div>
                      <div class="col-md-4">
                        {{ form.company_city|as_crispy_field }}
                      </div>
                      <div class="col-md-4">
                        {{ form.company_state|as_crispy_field }}
                      </div>
                      <div class="col-md-4">
                        {{ form.company_zip|as_crispy_field }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Industry & Services -->
              <div class="accordion-item mb-3 border">
                <h2 class="accordion-header" id="headingIndustry">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseIndustry" aria-expanded="false" aria-controls="collapseIndustry">
                    <i class="bi bi-briefcase me-2"></i> Industry & Services
                  </button>
                </h2>
                <div id="collapseIndustry" class="accordion-collapse collapse" aria-labelledby="headingIndustry" data-bs-parent="#registrationAccordion">
                  <div class="accordion-body">
                    <div class="row">
                      <div class="col-md-6">
                        {{ form.branch|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.industry|as_crispy_field }}
                      </div>
                      <div class="col-md-12">
                        {{ form.services_offered|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.contract_types|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.working_hours|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.work_location|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.average_wages|as_crispy_field }}
                      </div>
                      <div class="col-md-6">
                        {{ form.job_title|as_crispy_field }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
              <button type="button" class="btn btn-outline-secondary me-md-2" id="saveDraftBtn">
                <i class="bi bi-save me-1"></i> Save as Draft
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-arrow-right me-1"></i> Continue to Subscription
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Save as draft functionality
    document.getElementById('saveDraftBtn').addEventListener('click', function() {
      // Add logic to save form as draft
      alert('Form saved as draft. You can continue later.');
    });
  });
</script>
{% endblock %}