{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Subscription Management" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <!-- Sidebar -->
    <div class="col-lg-3 mb-4">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">{% trans "Quick Links" %}</h5>
        </div>
        <div class="card-body p-0">
          <div class="list-group list-group-flush">
            <a href="{% url 'services:dashboard' %}" class="list-group-item list-group-item-action">
              <i class="bi bi-speedometer2 me-2"></i>{% trans "Dashboard" %}
            </a>
            <a href="{% url 'services:profile' %}" class="list-group-item list-group-item-action">
              <i class="bi bi-person-circle me-2"></i>{% trans "Company Profile" %}
            </a>
            <a href="{% url 'services:offers' %}" class="list-group-item list-group-item-action">
              <i class="bi bi-briefcase me-2"></i>{% trans "My Offers" %}
            </a>
            <a href="{% url 'services:contact_requests' %}" class="list-group-item list-group-item-action">
              <i class="bi bi-envelope me-2"></i>{% trans "Contact Requests" %}
            </a>
            <a href="{% url 'services:account' %}" class="list-group-item list-group-item-action">
              <i class="bi bi-gear me-2"></i>{% trans "Account Settings" %}
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Main Content -->
    <div class="col-lg-9">
      <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
          <h1 class="h3 mb-0">{% trans "Subscription Management" %}</h1>
        </div>
        <div class="card-body">
          {% if subscriptions %}
            {% with current_subscription=subscriptions.first %}
              <div class="alert alert-info mb-4">
                <div class="d-flex">
                  <div class="me-3">
                    <i class="bi bi-info-circle-fill fs-3"></i>
                  </div>
                  <div>
                    <h5 class="alert-heading">{% trans "Current Subscription" %}</h5>
                    <p class="mb-0">
                      {% trans "You are currently subscribed to the" %} <strong>{{ current_subscription.plan|title }}</strong> {% trans "plan." %}
                      {% if current_subscription.status == 'active' %}
                        {% trans "Your subscription is active until" %} <strong>{{ current_subscription.end_date|date:"F d, Y" }}</strong>.
                      {% elif current_subscription.status == 'cancelled' %}
                        {% trans "Your subscription has been cancelled but will remain active until" %} <strong>{{ current_subscription.end_date|date:"F d, Y" }}</strong>.
                      {% elif current_subscription.status == 'expired' %}
                        {% trans "Your subscription has expired. Please renew to continue using our services." %}
                      {% elif current_subscription.status == 'pending' %}
                        {% trans "Your subscription is pending activation." %}
                      {% endif %}
                    </p>
                  </div>
                </div>
              </div>
              
              <div class="card mb-4">
                <div class="card-header bg-light">
                  <h5 class="mb-0">{% trans "Subscription Details" %}</h5>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <p><strong>{% trans "Plan:" %}</strong> {{ current_subscription.plan|title }}</p>
                      <p><strong>{% trans "Status:" %}</strong> 
                        {% if current_subscription.status == 'active' %}
                          <span class="badge bg-success">{% trans "Active" %}</span>
                        {% elif current_subscription.status == 'cancelled' %}
                          <span class="badge bg-warning">{% trans "Cancelled" %}</span>
                        {% elif current_subscription.status == 'expired' %}
                          <span class="badge bg-danger">{% trans "Expired" %}</span>
                        {% elif current_subscription.status == 'pending' %}
                          <span class="badge bg-secondary">{% trans "Pending" %}</span>
                        {% endif %}
                      </p>
                      <p><strong>{% trans "Auto-renew:" %}</strong> 
                        {% if current_subscription.is_auto_renew %}
                          <span class="text-success">{% trans "Enabled" %}</span>
                        {% else %}
                          <span class="text-danger">{% trans "Disabled" %}</span>
                        {% endif %}
                      </p>
                    </div>
                    <div class="col-md-6">
                      <p><strong>{% trans "Start Date:" %}</strong> {{ current_subscription.start_date|date:"F d, Y" }}</p>
                      <p><strong>{% trans "End Date:" %}</strong> {{ current_subscription.end_date|date:"F d, Y" }}</p>
                      <p><strong>{% trans "Payment Method:" %}</strong> {{ current_subscription.payment_method|default:"Credit Card" }}</p>
                    </div>
                  </div>
                  
                  <div class="d-flex justify-content-end mt-3">
                    {% if current_subscription.status == 'active' %}
                      <form method="post" action="{% url 'services:cancel_subscription' current_subscription.id %}" class="me-2">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-outline-danger">
                          <i class="bi bi-x-circle me-1"></i> {% trans "Cancel Subscription" %}
                        </button>
                      </form>
                      <a href="{% url 'services:change_plan' %}" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-repeat me-1"></i> {% trans "Change Plan" %}
                      </a>
                    {% elif current_subscription.status == 'cancelled' or current_subscription.status == 'expired' %}
                      <a href="{% url 'services:renew_subscription' current_subscription.id %}" class="btn btn-success">
                        <i class="bi bi-arrow-clockwise me-1"></i> {% trans "Renew Subscription" %}
                      </a>
                    {% elif current_subscription.status == 'pending' %}
                      <a href="{% url 'services:payment' %}?plan={{ current_subscription.plan }}" class="btn btn-primary">
                        <i class="bi bi-credit-card me-1"></i> {% trans "Complete Payment" %}
                      </a>
                    {% endif %}
                  </div>
                </div>
              </div>
              
              <div class="card">
                <div class="card-header bg-light">
                  <h5 class="mb-0">{% trans "Billing History" %}</h5>
                </div>
                <div class="card-body">
                  <div class="alert alert-secondary">
                    <i class="bi bi-info-circle me-2"></i>
                    {% trans "Billing history will be available once payment processing is implemented." %}
                  </div>
                </div>
              </div>
            {% endwith %}
          {% else %}
            <div class="text-center py-5">
              <div class="mb-4">
                <i class="bi bi-credit-card fs-1 text-muted"></i>
              </div>
              <h4 class="mb-3">{% trans "No Active Subscription" %}</h4>
              <p class="mb-4">{% trans "You don't have an active subscription. Subscribe to a plan to access all features." %}</p>
              <a href="{% url 'services:index' %}" class="btn btn-primary btn-lg">
                <i class="bi bi-plus-circle me-1"></i> {% trans "Choose a Plan" %}
              </a>
            </div>
          {% endif %}
        </div>
      </div>
      
      {% if not subscriptions %}
        <div class="card shadow">
          <div class="card-header bg-light">
            <h5 class="mb-0">{% trans "Available Plans" %}</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Basic Plan -->
              <div class="col-md-4 mb-4">
                <div class="card h-100 border-primary">
                  <div class="card-header bg-primary text-white text-center">
                    <h5 class="mb-0">{% trans "Basic" %}</h5>
                  </div>
                  <div class="card-body d-flex flex-column">
                    <div class="text-center mb-4">
                      <h3 class="mb-0">$99<small class="text-muted">/month</small></h3>
                    </div>
                    <ul class="list-unstyled mb-4">
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Browse up to 10 applicants" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "5 contact requests per month" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Basic company profile" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-x-circle-fill text-danger me-2"></i>
                        {% trans "Advanced filtering" %}
                      </li>
                    </ul>
                    <div class="mt-auto d-grid">
                      <a href="{% url 'services:payment' %}?plan=basic" class="btn btn-primary">
                        {% trans "Subscribe" %}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Standard Plan -->
              <div class="col-md-4 mb-4">
                <div class="card h-100 border-success">
                  <div class="card-header bg-success text-white text-center">
                    <h5 class="mb-0">{% trans "Standard" %}</h5>
                  </div>
                  <div class="card-body d-flex flex-column">
                    <div class="text-center mb-4">
                      <h3 class="mb-0">$199<small class="text-muted">/month</small></h3>
                    </div>
                    <ul class="list-unstyled mb-4">
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Browse up to 50 applicants" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "20 contact requests per month" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Enhanced company profile" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Basic filtering" %}
                      </li>
                    </ul>
                    <div class="mt-auto d-grid">
                      <a href="{% url 'services:payment' %}?plan=standard" class="btn btn-success">
                        {% trans "Subscribe" %}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Premium Plan -->
              <div class="col-md-4 mb-4">
                <div class="card h-100 border-info">
                  <div class="card-header bg-info text-white text-center">
                    <h5 class="mb-0">{% trans "Premium" %}</h5>
                  </div>
                  <div class="card-body d-flex flex-column">
                    <div class="text-center mb-4">
                      <h3 class="mb-0">$299<small class="text-muted">/month</small></h3>
                    </div>
                    <ul class="list-unstyled mb-4">
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Unlimited applicant browsing" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Unlimited contact requests" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Premium company profile" %}
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        {% trans "Advanced filtering & analytics" %}
                      </li>
                    </ul>
                    <div class="mt-auto d-grid">
                      <a href="{% url 'services:payment' %}?plan=premium" class="btn btn-info text-white">
                        {% trans "Subscribe" %}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
