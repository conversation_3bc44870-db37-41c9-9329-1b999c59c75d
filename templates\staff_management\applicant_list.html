{% extends 'common/base.html' %}
{% load static %}

{% block title %}Applicant Management{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1>Applicant Management</h1>
            <a href="{% url 'staff:smarch_panel' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Admin Panel
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <form id="filterForm">
                        <div class="mb-3">
                            <label class="form-label">Industry</label>
                            <select class="form-select">
                                <option value="">All Industries</option>
                                <option value="tech">Technology</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="finance">Finance</option>
                                <option value="education">Education</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="activeApplicants" checked>
                                <label class="form-check-label" for="activeApplicants">
                                    Active
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="inactiveApplicants">
                                <label class="form-check-label" for="inactiveApplicants">
                                    Inactive
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Experience Level</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="juniorLevel" checked>
                                <label class="form-check-label" for="juniorLevel">
                                    0-2 years
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="midLevel" checked>
                                <label class="form-check-label" for="midLevel">
                                    3-5 years
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="seniorLevel" checked>
                                <label class="form-check-label" for="seniorLevel">
                                    6+ years
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">Registered Applicants</h5>
                        </div>
                        <div class="col-auto">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Search applicants..." id="applicantSearchInput">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Case Number</th>
                                    <th>Industry</th>
                                    <th>Location</th>
                                    <th>Experience</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if applicants %}
                                    {% for applicant in applicants %}
                                    <tr>
                                        <td>{{ applicant.user.get_full_name|default:applicant.user.username }}</td>
                                        <td>{{ applicant.case_number }}</td>
                                        <td>{{ applicant.industry|default:"Not specified" }}</td>
                                        <td>{{ applicant.location|default:"Not specified" }}</td>
                                        <td>{{ applicant.position_experience_years|default:"0" }} years</td>
                                        <td>
                                            {% if applicant.form_responses.exists %}
                                                <span class="badge bg-success">Active</span>
                                            {% else %}
                                                <span class="badge bg-warning text-dark">No Application</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="#" class="btn btn-sm btn-outline-primary">View</a>
                                                <a href="#" class="btn btn-sm btn-outline-secondary">Edit</a>
                                                <button type="button" class="btn btn-sm btn-outline-danger">Disable</button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <p class="text-muted mb-0">No applicants found.</p>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <p class="mb-0">Showing {{ applicants.count|default:"0" }} applicants</p>
                        <nav aria-label="Page navigation">
                            <ul class="pagination mb-0">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">Next</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 