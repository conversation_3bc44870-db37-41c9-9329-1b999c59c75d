{% extends 'common/base.html' %}
{% load static %}

{% block title %}Application Details{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1>Application Details</h1>
            <div>
                <a href="{% url 'staff:application_list' %}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-left"></i> Back to Applications
                </a>
                <a href="{% url 'staff:smarch_panel' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-house"></i> Admin Panel
                </a>
            </div>
        </div>
    </div>

    {% if application %}
    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Applicant Information</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        {% if application.applicant.image %}
                        <img src="{{ application.applicant.image.url }}" alt="Profile Image" class="rounded-circle me-3" width="64" height="64">
                        {% else %}
                        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 64px; height: 64px;">
                            <i class="bi bi-person fs-1 text-secondary"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h5 class="mb-1">{{ application.applicant.user.get_full_name|default:application.applicant.user.username }}</h5>
                            <p class="text-muted mb-0">Case #{{ application.applicant.case_number }}</p>
                        </div>
                    </div>

                    <div class="mb-3">
                        <p class="mb-1"><strong>Email:</strong> {{ application.applicant.user.email }}</p>
                        <p class="mb-1"><strong>Phone:</strong> {{ application.applicant.phone_number|default:"Not provided" }}</p>
                        <p class="mb-1"><strong>Age:</strong> {{ application.applicant.age|default:"Not provided" }}</p>
                        <p class="mb-1"><strong>Location:</strong> {{ application.applicant.location|default:"Not provided" }}</p>
                        <p class="mb-1"><strong>Work Permit:</strong> {% if application.applicant.work_permit %}Yes{% else %}No{% endif %}</p>
                    </div>

                    <div class="mb-3">
                        <h6>Bio</h6>
                        <p>{{ application.applicant.bio|default:"No bio provided"|linebreaksbr }}</p>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="#" class="btn btn-outline-primary btn-sm w-100">View Full Profile</a>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Application Status</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="mb-2">Current Status</h6>
                        <div class="d-flex align-items-center">
                            {% if application.status == 'draft' %}
                                <span class="badge bg-secondary p-2 me-2">Draft</span>
                            {% elif application.status == 'submitted' %}
                                <span class="badge bg-primary p-2 me-2">Submitted</span>
                            {% elif application.status == 'in_review' %}
                                <span class="badge bg-info p-2 me-2">In Review</span>
                            {% elif application.status == 'approved' %}
                                <span class="badge bg-success p-2 me-2">Approved</span>
                            {% elif application.status == 'rejected' %}
                                <span class="badge bg-danger p-2 me-2">Rejected</span>
                            {% endif %}
                            <span>since {{ application.updated_at|date:"M d, Y" }}</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="mb-2">Timeline</h6>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Created
                                <span class="badge bg-light text-dark">{{ application.submitted_at|date:"M d, Y" }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Submitted
                                <span class="badge bg-light text-dark">{{ application.submission_date|date:"M d, Y"|default:"Pending" }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Reviewed
                                <span class="badge bg-light text-dark">{{ application.review_date|date:"M d, Y"|default:"Pending" }}</span>
                            </li>
                        </ul>
                    </div>

                    <div class="mb-0">
                        <h6 class="mb-2">Update Status</h6>
                        <form action="{% url 'staff:application_detail' application.id %}" method="post" class="d-grid gap-2">
                            {% csrf_token %}
                            <input type="hidden" name="update_status" value="1">
                            <select class="form-select mb-2" name="status">
                                <option value="in_review" {% if application.status == 'in_review' %}selected{% endif %}>Mark as In Review</option>
                                <option value="approved" {% if application.status == 'approved' %}selected{% endif %}>Approve Application</option>
                                <option value="rejected" {% if application.status == 'rejected' %}selected{% endif %}>Reject Application</option>
                            </select>
                            <button type="submit" class="btn btn-primary">Update Status</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Application Details</h5>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="applicationTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab" aria-controls="basic" aria-selected="true">Basic Info</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="dream-job-tab" data-bs-toggle="tab" data-bs-target="#dream-job" type="button" role="tab" aria-controls="dream-job" aria-selected="false">Dream Job</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="experience-tab" data-bs-toggle="tab" data-bs-target="#experience" type="button" role="tab" aria-controls="experience" aria-selected="false">Experience</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="additional-tab" data-bs-toggle="tab" data-bs-target="#additional" type="button" role="tab" aria-controls="additional" aria-selected="false">Additional</button>
                        </li>
                    </ul>
                    <div class="tab-content p-3" id="applicationTabsContent">
                        <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <p><strong>Full Name:</strong> {{ application.raw_data.full_name }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Date of Birth:</strong> {{ application.raw_data.date_of_birth }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Location:</strong> {{ application.raw_data.location }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Arbetsförmedlingen:</strong> {% if application.raw_data.arbetsformedlingen %}Yes{% else %}No{% endif %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="dream-job" role="tabpanel" aria-labelledby="dream-job-tab">
                            <div class="row g-3">
                                <div class="col-12">
                                    <p><strong>Dream Job Description:</strong></p>
                                    <p>{{ application.raw_data.dream_job_description }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Industry:</strong> {{ application.raw_data.industry }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Job Category:</strong> {{ application.raw_data.job_category }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="experience" role="tabpanel" aria-labelledby="experience-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <p><strong>Years of Experience:</strong> {{ application.raw_data.years_of_experience }}</p>
                                </div>
                                <div class="col-12">
                                    <p><strong>Current Skills:</strong></p>
                                    <p>{{ application.raw_data.current_skills }}</p>
                                </div>
                                <div class="col-12">
                                    <p><strong>Skills to Acquire:</strong></p>
                                    <p>{{ application.raw_data.additional_skills }}</p>
                                </div>
                                <div class="col-12">
                                    <p><strong>Challenges:</strong></p>
                                    <p>{{ application.raw_data.challenges }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="additional" role="tabpanel" aria-labelledby="additional-tab">
                            <div class="row g-3">
                                <div class="col-12">
                                    <p><strong>Additional Comments:</strong></p>
                                    <p>{{ application.raw_data.comments|default:"No comments provided" }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Pilot Program Interest:</strong> {% if application.raw_data.pilot_interest %}Yes{% else %}No{% endif %}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>NDA Accepted:</strong> {% if application.raw_data.accept_nda %}Yes{% else %}No{% endif %}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">Notes</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Staff Notes</h6>
                        <div class="bg-light p-3 rounded mb-3">
                            {{ application.notes|default:"No staff notes available"|linebreaksbr }}
                        </div>
                    </div>
                    <div>
                        <h6>Add Note</h6>
                        <form action="{% url 'staff:application_detail' application.id %}" method="post">
                            {% csrf_token %}
                            <input type="hidden" name="add_note" value="1">
                            <div class="mb-3">
                                <textarea class="form-control" name="note" rows="3" placeholder="Enter your notes here" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Save Note</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-warning">
        <h4 class="alert-heading">Application Not Found</h4>
        <p>The requested application could not be found or you don't have permission to view it.</p>
        <hr>
        <p class="mb-0">Please check the application ID and try again, or return to the <a href="{% url 'staff:application_list' %}" class="alert-link">applications list</a>.</p>
    </div>
    {% endif %}
</div>
{% endblock %}