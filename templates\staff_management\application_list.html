{% extends 'common/base.html' %}
{% load static %}

{% block title %}Application Management{% endblock %}

{% block extra_css %}
<style>
    .dropdown-item-form {
        display: block;
        width: 100%;
        padding: 0;
        margin: 0;
    }
    .dropdown-item-form button {
        text-align: left;
        width: 100%;
        border: none;
        background: none;
    }
    .dropdown-item-form button:hover {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1>Application Management</h1>
            <a href="{% url 'staff:smarch_panel' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Admin Panel
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <form id="filterForm" method="get" action="{% url 'staff:application_list' %}">
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="status" value="draft" id="statusDraft" {% if 'draft' in status_filter %}checked{% endif %}>
                                <label class="form-check-label" for="statusDraft">
                                    Draft
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="status" value="submitted" id="statusSubmitted" {% if 'submitted' in status_filter %}checked{% endif %}>
                                <label class="form-check-label" for="statusSubmitted">
                                    Submitted
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="status" value="in_review" id="statusInReview" {% if 'in_review' in status_filter %}checked{% endif %}>
                                <label class="form-check-label" for="statusInReview">
                                    In Review
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="status" value="approved" id="statusApproved" {% if 'approved' in status_filter %}checked{% endif %}>
                                <label class="form-check-label" for="statusApproved">
                                    Approved
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="status" value="rejected" id="statusRejected" {% if 'rejected' in status_filter %}checked{% endif %}>
                                <label class="form-check-label" for="statusRejected">
                                    Rejected
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Search</label>
                            <input type="text" class="form-control" name="search" placeholder="Search applications..." value="{{ search_query }}">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                        {% if status_filter or search_query %}
                            <a href="{% url 'staff:application_list' %}" class="btn btn-outline-secondary w-100 mt-2">Clear Filters</a>
                        {% endif %}
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">Applications</h5>
                        </div>
                        <div class="col-auto">
                            <form method="get" action="{% url 'staff:application_list' %}" class="d-flex">
                                {% for status in status_filter %}
                                    <input type="hidden" name="status" value="{{ status }}">
                                {% endfor %}
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Search applications..." name="search" value="{{ search_query }}">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Applicant</th>
                                    <th>Case Number</th>
                                    <th>Submission Date</th>
                                    <th>Status</th>
                                    <th>Reviewer</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if applications %}
                                    {% for application in applications %}
                                    <tr>
                                        <td>{{ application.applicant.user.get_full_name|default:application.applicant.user.username }}</td>
                                        <td>{{ application.applicant.case_number }}</td>
                                        <td>{{ application.submission_date|date:"M d, Y" }}</td>
                                        <td>
                                            {% if application.status == 'draft' %}
                                                <span class="badge bg-secondary">Draft</span>
                                            {% elif application.status == 'submitted' %}
                                                <span class="badge bg-primary">Submitted</span>
                                            {% elif application.status == 'in_review' %}
                                                <span class="badge bg-info">In Review</span>
                                            {% elif application.status == 'approved' %}
                                                <span class="badge bg-success">Approved</span>
                                            {% elif application.status == 'rejected' %}
                                                <span class="badge bg-danger">Rejected</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ application.reviewed_by.get_full_name|default:"-" }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{% url 'staff:application_detail' application.id %}" class="btn btn-sm btn-outline-primary">View</a>
                                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <span class="visually-hidden">Toggle Dropdown</span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <form action="{% url 'staff:application_detail' application.id %}" method="post" class="dropdown-item-form">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="update_status" value="1">
                                                            <input type="hidden" name="status" value="in_review">
                                                            <button type="submit" class="dropdown-item">Mark as In Review</button>
                                                        </form>
                                                    </li>
                                                    <li>
                                                        <form action="{% url 'staff:application_detail' application.id %}" method="post" class="dropdown-item-form">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="update_status" value="1">
                                                            <input type="hidden" name="status" value="approved">
                                                            <button type="submit" class="dropdown-item">Approve</button>
                                                        </form>
                                                    </li>
                                                    <li>
                                                        <form action="{% url 'staff:application_detail' application.id %}" method="post" class="dropdown-item-form">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="update_status" value="1">
                                                            <input type="hidden" name="status" value="rejected">
                                                            <button type="submit" class="dropdown-item">Reject</button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <p class="text-muted mb-0">No applications found.</p>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <p class="mb-0">Showing {{ applications.count|default:"0" }} applications</p>
                        <nav aria-label="Page navigation">
                            <ul class="pagination mb-0">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">Next</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}