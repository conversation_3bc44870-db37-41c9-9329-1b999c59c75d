{% extends 'common/base.html' %}
{% load static %}

{% block title %}Hiring Partners Management{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1>Hiring Partners Management</h1>
            <a href="{% url 'staff:smarch_panel' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Admin Panel
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">Hiring Partner Accounts</h5>
                        </div>
                        <div class="col-auto">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Search hiring partners..." id="serviceSearchInput">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Company</th>
                                    <th>Contact Person</th>
                                    <th>Industry</th>
                                    <th>Size</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if service_users %}
                                    {% for provider in service_users %}
                                    <tr>
                                        <td>{{ provider.company_name }}</td>
                                        <td>{{ provider.user.get_full_name|default:provider.user.username }}</td>
                                        <td>{{ provider.industry|default:"Not specified" }}</td>
                                        <td>{{ provider.company_size }}</td>
                                        <td>{{ provider.primary_location|default:"Not specified" }}</td>
                                        <td>
                                            {% if provider.subscriptions.filter.exists %}
                                                <span class="badge bg-success">Active</span>
                                            {% else %}
                                                <span class="badge bg-warning text-dark">No Subscription</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="#" class="btn btn-sm btn-outline-primary">View</a>
                                                <a href="#" class="btn btn-sm btn-outline-secondary">Edit</a>
                                                <button type="button" class="btn btn-sm btn-outline-danger">Suspend</button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <p class="text-muted mb-0">No service providers found.</p>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <p class="mb-0">Showing {{ service_users.count|default:"0" }} service providers</p>
                        <nav aria-label="Page navigation">
                            <ul class="pagination mb-0">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">Next</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}