{% extends 'common/base.html' %}
{% load static %}

{% block title %}Smarch Admin Panel{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">Smarch Admin Panel</h1>
            <div class="alert alert-info">Welcome to the Smarch administrative panel. From here you can manage all aspects of the platform.</div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Applicants Management</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">View and manage applicant profiles, applications, and status updates.</p>
                    <a href="{% url 'staff:applicant_list' %}" class="btn btn-outline-primary">Manage Applicants</a>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Hiring Partners</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">View and manage hiring partner profiles, subscriptions, and offers.</p>
                    <a href="{% url 'staff:service_user_list' %}" class="btn btn-outline-success">Manage Hiring Partners</a>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Applications</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Review pending applications and process them efficiently.</p>
                    <a href="{% url 'staff:application_list' %}" class="btn btn-outline-info">Manage Applications</a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">Content Management</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Manage blog posts, events, and other content on the platform.</p>
                    <a href="{% url 'admin:index' %}" class="btn btn-outline-warning">Manage Content</a>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">System Management</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Access system settings, user management, and platform configuration.</p>
                    <a href="{% url 'admin:index' %}" class="btn btn-outline-danger">Django Admin</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}