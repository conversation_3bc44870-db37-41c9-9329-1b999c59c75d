{% extends "common/base.html" %}
{% load i18n %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Smarch - Coming Soon" %}{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 text-center">
            <div class="mb-5">
                <h1 class="display-4 mb-3">{% trans "Smarch" %}</h1>
                <p class="lead mb-4">{% trans "Connecting applicants with hiring partners" %}</p>
                <p class="mb-5">
                    {% trans "Smarch is a revolutionary recruitment platform designed to streamline the job search process. We're building a better way for applicants to find their dream jobs and for employers to discover perfect candidates." %}
                </p>
                <div class="alert alert-info">
                    <h4 class="alert-heading">{% trans "Coming Soon!" %}</h4>
                    <p>{% trans "We're working hard to launch Smarch. Subscribe to get notified when we go live." %}</p>
                </div>
            </div>

            <div class="card shadow-sm mb-5">
                <div class="card-body p-4">
                    <h3 class="card-title mb-4">{% trans "Get Notified at Launch" %}</h3>
                    <form method="post" action="{% url 'subscription:subscribe' %}" id="subscription-form">
                        {% csrf_token %}
                        <div class="row g-3">
                            <div class="col-md-6">
                                {{ form.name|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.email|as_crispy_field }}
                            </div>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                {% trans "Subscribe" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="mt-5">
                <h4>{% trans "What to Expect" %}</h4>
                <div class="row mt-4">
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="mb-3">
                                    <i class="bi bi-search fs-1 text-primary"></i>
                                </div>
                                <h5>{% trans "Smart Job Matching" %}</h5>
                                <p class="card-text">{% trans "Our intelligent algorithm matches applicants with the most suitable job opportunities." %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="mb-3">
                                    <i class="bi bi-people fs-1 text-primary"></i>
                                </div>
                                <h5>{% trans "Streamlined Hiring" %}</h5>
                                <p class="card-text">{% trans "Employers can easily browse qualified candidates and manage the hiring process." %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="mb-3">
                                    <i class="bi bi-graph-up fs-1 text-primary"></i>
                                </div>
                                <h5>{% trans "Career Growth" %}</h5>
                                <p class="card-text">{% trans "Access resources and tools to help advance your career and professional development." %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('subscription-form');

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);

            fetch('{% url "subscription:subscribe" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                // Create alert element
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${data.success ? 'success' : 'danger'} alert-dismissible fade show`;
                alertDiv.role = 'alert';
                alertDiv.innerHTML = `
                    ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;

                // Prevent Bootstrap from auto-dismissing the alert
                alertDiv.addEventListener('DOMNodeInserted', function() {
                    const bsAlert = new bootstrap.Alert(alertDiv);
                    // Override the close method to only work with the close button
                });

                // Insert alert before the form
                form.parentNode.insertBefore(alertDiv, form);

                // Reset form if successful
                if (data.success) {
                    form.reset();
                }

                // Scroll to the alert
                alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
    });
</script>
{% endblock %}
