{% extends 'common/base.html' %}

{% block title %}Applicant Dashboard | Smarch{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1>Applicant Dashboard</h1>
            <p class="lead">Welcome back, {{ user.get_full_name }}!</p>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Status Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Your Status</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="p-3 bg-light rounded-circle me-3">
                            <i class="bi bi-person-check fs-3"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Current Status</h6>
                            <h4 class="mb-0">{{ user.applicantprofile.get_status_display }}</h4>
                        </div>
                    </div>
                    <div class="mb-3">
                        <p class="mb-1"><strong>Case Number:</strong> {{ user.applicant_profile.case_number }}</p>
                        <p class="mb-0"><strong>Queue Position:</strong> {{ user.applicant_profile.get_display_queue_position }}</p>
                    </div>
                    <div class="d-grid">
                        <a href="{% url 'users:profile' %}" class="btn btn-outline-primary"><i class="bi bi-person-badge me-1"></i> View Full Profile</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Application Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Your Application</h5>
                </div>
                <div class="card-body">
                    {% if user.applicant_profile.form_responses.exists %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="p-3 bg-light rounded-circle me-3">
                                <i class="bi bi-file-earmark-check fs-3"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Application Status</h6>
                                <h4 class="mb-0">{{ user.applicant_profile.form_responses.first.get_status_display }}</h4>
                            </div>
                        </div>
                        <p>Submitted on: <strong>
                            {{ user.applicant_profile.form_responses.first.submission_date|date:"F d, Y" }}
                        </strong></p>
                        <div class="d-grid gap-2">
                            <a href="{% url 'application:status' %}" class="btn btn-outline-success">View Application</a>
                            <a href="{% url 'application:update' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-pencil me-1"></i> Update Application
                            </a>
                        </div>
                    {% else %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="p-3 bg-light rounded-circle me-3">
                                <i class="bi bi-file-earmark-x fs-3"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Application Status</h6>
                                <h4 class="mb-0">Not Submitted</h4>
                            </div>
                        </div>
                        <p>You haven't submitted your application form yet. Start your application process now!</p>
                        <div class="d-grid">
                            <a href="{% url 'application:apply' %}" class="btn btn-success">Start Application</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Contact Requests Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Contact Requests</h5>
                </div>
                <div class="card-body">
                    {% with request_count=user.applicantprofile.contact_requests_received.count %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="p-3 bg-light rounded-circle me-3">
                                <i class="bi bi-envelope fs-3"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Pending Requests</h6>
                                <h4 class="mb-0">{{ request_count }}</h4>
                            </div>
                        </div>
                        {% if request_count > 0 %}
                            <p>You have {{ request_count }} pending contact requests from service providers.</p>
                        {% else %}
                            <p>No pending contact requests at the moment.</p>
                        {% endif %}
                        <div class="d-grid">
                            <a href="{% url 'application:contact_requests' %}" class="btn btn-outline-info">View Requests</a>
                        </div>
                    {% endwith %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Messages Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Messages</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="p-3 bg-light rounded-circle me-3">
                            <i class="bi bi-chat-dots fs-3"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Your Messages</h6>
                            <h4 class="mb-0">Inbox</h4>
                        </div>
                    </div>
                    <p>Check your messages from hiring partners and staff members.</p>
                    <div class="d-grid">
                        <a href="{% url 'messaging:inbox' %}" class="btn btn-outline-primary">View Messages</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calendar Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Upcoming Events</h5>
                </div>
                <div class="card-body">
                    {% if user.event_set.exists %}
                        <ul class="list-group list-group-flush">
                            {% for event in user.event_set.all|dictsortreversed:"start_time"|slice:":5" %}
                                <li class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">{{ event.title }}</h6>
                                            <small class="text-muted">{{ event.start_time|date:"F d, Y - g:i A" }}</small>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">{{ event.get_event_type_display }}</span>
                                    </div>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="mb-0">No upcoming events.</p>
                        </div>
                    {% endif %}
                    <div class="d-grid mt-3">
                        <a href="{% url 'event_management:event_list' %}" class="btn btn-outline-primary">View Calendar</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Latest Blog Posts -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Latest Blog Posts</h5>
                </div>
                <div class="card-body">
                    {% if blog_posts %}
                        <ul class="list-group list-group-flush">
                            {% for post in blog_posts %}
                                <li class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">{{ post.title }}</h6>
                                            <small class="text-muted">{{ post.published_at|date:"F d, Y" }}</small>
                                        </div>
                                        <a href="{% url 'blog:detail' post.slug %}" class="btn btn-sm btn-outline-primary">Read</a>
                                    </div>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="mb-0">No blog posts available.</p>
                        </div>
                    {% endif %}
                    <div class="d-grid mt-3">
                        <a href="{% url 'blog:index' %}" class="btn btn-outline-primary">View All Posts</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}