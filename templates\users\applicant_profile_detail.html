{% extends 'common/base.html' %}

{% block title %}{{ profile.user.get_full_name }} - Applicant Profile{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-12 mb-4">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'core:home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Applicant Profile</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Profile Header -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            {% if profile.image %}
                                <img src="{{ profile.image.url }}" alt="{{ profile.user.get_full_name }}" class="profile-image mb-3">
                            {% else %}
                                <img src="https://via.placeholder.com/150" alt="Default Profile" class="profile-image mb-3">
                            {% endif %}
                            {% if user == profile.user %}
                                <div class="d-grid">
                                    <a href="{% url 'users:profile_edit' %}" class="btn btn-primary">Edit Profile</a>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-9">
                            <h2>{{ profile.user.get_full_name }}</h2>
                            <p class="text-muted">Applicant</p>

                            <div class="mb-3">
                                <h5>Bio</h5>
                                <p>{{ profile.bio|default:"No bio provided yet." }}</p>
                            </div>

                            {% if user == profile.user or user.is_staff %}
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Case Number</h6>
                                                <p class="card-text">{{ profile.case_number }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Queue Number</h6>
                                                <p class="card-text">{{ profile.queue_number }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Status</h6>
                                                <p class="card-text">{{ profile.get_status_display }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% if profile.assignation %}
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="card-title">Assignation</h6>
                                                <p class="card-text">{{ profile.assignation }}</p>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}
                            {% endif %}

                            {% if view_as_hiring_partner %}
                                <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                                    <a href="{% url 'contact:create_request' recipient_id=profile.user.id %}" class="btn btn-success">Contact Request</a>
                                    <button class="btn btn-outline-primary">Save to Favorites</button>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Application Information (if any) -->
        {% if profile.applicationform_set.exists %}
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h3>Application Information</h3>
                    </div>
                    <div class="card-body">
                        {% with refined_info=profile.applicationform_set.first.refinedinfo %}
                            {% if refined_info %}
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Professional Information</h5>
                                        <dl class="row">
                                            <dt class="col-sm-4">Industry</dt>
                                            <dd class="col-sm-8">{{ refined_info.industry }}</dd>

                                            <dt class="col-sm-4">Branch</dt>
                                            <dd class="col-sm-8">{{ refined_info.branch }}</dd>

                                            <dt class="col-sm-4">Role</dt>
                                            <dd class="col-sm-8">{{ refined_info.role }}</dd>

                                            <dt class="col-sm-4">Experience</dt>
                                            <dd class="col-sm-8">{{ refined_info.experience_years }} years</dd>
                                        </dl>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Personal Information</h5>
                                        <dl class="row">
                                            <dt class="col-sm-4">Age</dt>
                                            <dd class="col-sm-8">{{ refined_info.age }}</dd>

                                            <dt class="col-sm-4">Location</dt>
                                            <dd class="col-sm-8">{{ refined_info.location }}</dd>

                                            <dt class="col-sm-4">Work Permit</dt>
                                            <dd class="col-sm-8">
                                                {% if refined_info.work_permit %}
                                                    <span class="badge bg-success">Yes</span>
                                                {% else %}
                                                    <span class="badge bg-danger">No</span>
                                                {% endif %}
                                            </dd>
                                        </dl>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <h5>Skills</h5>
                                        <div>
                                            {% for skill in refined_info.skills %}
                                                <span class="badge bg-primary me-1">{{ skill }}</span>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <h5>Languages</h5>
                                        <div>
                                            {% for language in refined_info.languages %}
                                                <span class="badge bg-secondary me-1">{{ language }}</span>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    Application information is still being processed.
                                </div>
                            {% endif %}
                        {% endwith %}
                    </div>
                </div>
            </div>
        {% elif user == profile.user %}
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h4>Complete Your Application</h4>
                        <p>You haven't submitted your application form yet.</p>
                        <a href="{% url 'application:apply' %}" class="btn btn-primary">Start Application</a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}