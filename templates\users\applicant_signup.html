{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Applicant Registration" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h2 class="mb-0">{% trans "Applicant Registration" %}</h2>
          <p class="text-white mb-0 mt-2"><small>{% trans "Looking for a job? Register as an applicant to browse opportunities" %}</small></p>
        </div>
        <div class="card-body">
          <form method="post" enctype="multipart/form-data">
            {% csrf_token %}

            <div class="alert alert-info mb-4">
              <p class="mb-0"><i class="bi bi-info-circle-fill me-2"></i> {% trans "Already have an account?" %} <a href="{% url 'account_login' %}">{% trans "Log in here" %}</a>.</p>
              <p class="mb-0 mt-2"><i class="bi bi-building-fill me-2"></i> {% trans "Are you a hiring partner?" %} <a href="{% url 'users:service_signup' %}" class="fw-bold">{% trans "Register as a hiring partner" %}</a> {% trans "to post jobs and find applicants." %}</p>
            </div>

            <h3 class="mb-4">{% trans "Account Information" %}</h3>
            {% for field in user_form %}
              <div class="mb-3">
                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                {{ field }}
                {% if field.help_text %}
                  <div class="form-text">{{ field.help_text }}</div>
                {% endif %}
                {% if field.errors %}
                  <div class="alert alert-danger mt-1">
                    {{ field.errors }}
                  </div>
                {% endif %}
              </div>
            {% endfor %}

            <h3 class="mb-4 mt-5">{% trans "Profile Information" %}</h3>
            {% for field in profile_form %}
              <div class="mb-3">
                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                {{ field }}
                {% if field.help_text %}
                  <div class="form-text">{{ field.help_text }}</div>
                {% endif %}
                {% if field.errors %}
                  <div class="alert alert-danger mt-1">
                    {{ field.errors }}
                  </div>
                {% endif %}
              </div>
            {% endfor %}

            <div class="d-grid gap-2 mt-5">
              <button type="submit" class="btn btn-primary btn-lg">{% trans "Register" %}</button>
            </div>

            <div class="mt-4 text-center">
              <div class="card bg-light">
                <div class="card-body">
                  <h5 class="card-title">{% trans "Are you a hiring partner?" %}</h5>
                  <p class="card-text">{% trans "Register as a hiring partner to post jobs and find qualified applicants." %}</p>
                  <a href="{% url 'users:service_signup' %}" class="btn btn-outline-primary">{% trans "Hiring Partner Registration" %}</a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}