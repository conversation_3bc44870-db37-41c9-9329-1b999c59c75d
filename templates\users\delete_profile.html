{% extends 'common/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Delete Account" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">{% trans "Delete Your Account" %}</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">{% trans "Warning!" %}</h5>
                        <p>{% trans "This action cannot be undone. Once you delete your account, all of your data will be permanently removed from our system." %}</p>
                        <hr>
                        <p class="mb-0">{% trans "Please read the following information carefully before proceeding:" %}</p>
                        <ul>
                            <li>{% trans "Your profile information will be deleted" %}</li>
                            <li>{% trans "Your application data will be deleted" %}</li>
                            <li>{% trans "Your messages and contact requests will be deleted" %}</li>
                            <li>{% trans "Your subscription information will be deleted" %}</li>
                        </ul>
                    </div>

                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="password" class="form-label">{% trans "Enter your password to confirm deletion" %}</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">{% trans "For security reasons, we need to verify your identity." %}</div>
                        </div>

                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="confirm" required>
                            <label class="form-check-label" for="confirm">
                                {% trans "I understand that this action cannot be undone and all my data will be permanently deleted." %}
                            </label>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'users:settings' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i> {% trans "Go Back" %}
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash me-1"></i> {% trans "Delete My Account" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
