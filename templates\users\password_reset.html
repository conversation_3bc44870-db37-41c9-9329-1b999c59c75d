{% extends 'common/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Reset Password" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{% trans "Reset Your Password" %}</h4>
                </div>
                <div class="card-body">
                    <p class="mb-4">{% trans "Forgotten your password? Enter your email address below, and we'll send you an email allowing you to reset it." %}</p>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.errors %}
                        <div class="alert alert-danger">
                            {% for field in form %}
                                {% for error in field.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <div class="mb-3">
                            <label for="id_email" class="form-label">{% trans "Email" %}</label>
                            {{ form.email }}
                            {% if form.email.help_text %}
                            <div class="form-text">{{ form.email.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                {% trans "Reset My Password" %}
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer">
                    <div class="text-center">
                        <a href="{% url 'account_login' %}">{% trans "Back to login" %}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
