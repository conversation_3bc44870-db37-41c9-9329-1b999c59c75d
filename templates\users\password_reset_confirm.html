{% extends 'common/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Set New Password" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{% trans "Set a New Password" %}</h4>
                </div>
                <div class="card-body">
                    {% if validlink %}
                        <p class="mb-4">{% trans "Please enter your new password twice so we can verify you typed it correctly." %}</p>
                        
                        <form method="post">
                            {% csrf_token %}
                            
                            {% if form.errors %}
                            <div class="alert alert-danger">
                                {% for field in form %}
                                    {% for error in field.errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                {% endfor %}
                            </div>
                            {% endif %}
                            
                            <div class="mb-3">
                                <label for="id_new_password1" class="form-label">{% trans "New password" %}</label>
                                {{ form.new_password1 }}
                                {% if form.new_password1.help_text %}
                                <div class="form-text">
                                    <ul class="small">
                                        {% for help_text in form.new_password1.help_text|split:'<li>' %}
                                            {% if not forloop.first %}
                                                <li>{{ help_text }}</li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="id_new_password2" class="form-label">{% trans "Confirm new password" %}</label>
                                {{ form.new_password2 }}
                                {% if form.new_password2.help_text %}
                                <div class="form-text">{{ form.new_password2.help_text }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    {% trans "Change My Password" %}
                                </button>
                            </div>
                        </form>
                    {% else %}
                        <div class="alert alert-danger">
                            <h5 class="alert-heading">{% trans "Password reset unsuccessful" %}</h5>
                            <p>{% trans "The password reset link was invalid, possibly because it has already been used. Please request a new password reset." %}</p>
                        </div>
                        <div class="d-grid gap-2">
                            <a href="{% url 'password_reset' %}" class="btn btn-primary">
                                {% trans "Request New Reset Link" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
