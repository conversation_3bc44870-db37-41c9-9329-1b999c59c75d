{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ user.get_full_name }} | Profile | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <!-- Profile Sidebar -->
    <div class="col-lg-4 mb-4">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h5 class="card-title mb-0">{% trans "Profile" %}</h5>
        </div>
        <div class="card-body text-center">
          {% if user.user_type == 'applicant' and user.applicant_profile.image %}
            <img src="{{ user.applicant_profile.image.url }}" alt="{{ user.get_full_name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
          {% elif user.user_type == 'service' and user.hiring_partner_profile.profile_image %}
            <img src="{{ user.hiring_partner_profile.profile_image.url }}" alt="{{ user.get_full_name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
          {% else %}
            <img src="{% static 'images/default-avatar.png' %}" alt="{{ user.get_full_name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
          {% endif %}

          <h4 class="mb-0">{{ user.get_full_name }}</h4>
          <p class="text-muted">{{ user.email }}</p>

          {% if user.user_type == 'applicant' %}
            <div class="badge bg-info text-white mb-3">{% trans "Applicant" %}</div>
          {% elif user.user_type == 'service' %}
            <div class="badge bg-success text-white mb-3">{% trans "Hiring Partner" %}</div>
          {% endif %}

          {% if has_applicant_profile and has_hiring_partner_profile %}
          <div class="mb-3">
            <p class="small text-muted mb-2">{% trans "Switch Profile Type:" %}</p>
            <div class="btn-group w-100" role="group">
              <a href="{% url 'users:profile' %}?profile_type=applicant" class="btn btn-sm {% if user.user_type == 'applicant' %}btn-info{% else %}btn-outline-info{% endif %}">
                {% trans "Applicant" %}
              </a>
              <a href="{% url 'users:profile' %}?profile_type=service" class="btn btn-sm {% if user.user_type == 'service' %}btn-success{% else %}btn-outline-success{% endif %}">
                {% trans "Hiring Partner" %}
              </a>
            </div>
          </div>
          {% endif %}

          <div class="d-grid gap-2">
            <a href="{% url 'users:profile_edit' %}" class="btn btn-primary">
              <i class="bi bi-pencil-square me-1"></i> {% trans "Edit Profile" %}
            </a>
            <a href="{% url 'users:dashboard' %}" class="btn btn-outline-secondary">
              <i class="bi bi-speedometer me-1"></i> {% trans "Dashboard" %}
            </a>
          </div>
        </div>
      </div>

      <!-- Account Info -->
      <div class="card shadow mt-4">
        <div class="card-header bg-light">
          <h5 class="card-title mb-0">{% trans "Account Information" %}</h5>
        </div>
        <div class="card-body">
          <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex justify-content-between align-items-center">
              {% trans "Username" %}
              <span class="text-muted">{{ user.username }}</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              {% trans "Member Since" %}
              <span class="text-muted">{{ user.date_joined|date:"F d, Y" }}</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              {% trans "Last Login" %}
              <span class="text-muted">{{ user.last_login|date:"F d, Y" }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Profile Content -->
    <div class="col-lg-8">
      <!-- Bio Section -->
      <div class="card shadow mb-4">
        <div class="card-header bg-light">
          <h5 class="card-title mb-0">{% trans "About Me" %}</h5>
        </div>
        <div class="card-body">
          {% if user.user_type == 'applicant' and user.applicant_profile.bio %}
            <p>{{ user.applicant_profile.bio|linebreaks }}</p>
          {% elif user.user_type == 'service' and user.hiring_partner_profile.company_needs %}
            <p>{{ user.hiring_partner_profile.company_needs|linebreaks }}</p>
          {% else %}
            <p class="text-muted">{% trans "No bio information available. Add a bio by editing your profile." %}</p>
          {% endif %}
        </div>
      </div>

      <!-- Additional Information -->
      {% if user.user_type == 'applicant' %}
        <div class="card shadow mb-4">
          <div class="card-header bg-light">
            <h5 class="card-title mb-0">{% trans "Professional Information" %}</h5>
          </div>
          <div class="card-body">

            <div class="row">
              {% if app_profile.industry %}
                <div class="col-md-6 mb-3">
                  <h6>{% trans "Industry" %}</h6>
                  <p>
                    {% if app_profile.industry == 'it_software' %}IT & Software Development
                    {% elif app_profile.industry == 'healthcare' %}Healthcare
                    {% elif app_profile.industry == 'education' %}Education
                    {% elif app_profile.industry == 'finance' %}Finance
                    {% elif app_profile.industry == 'arts' %}Arts & Creative
                    {% elif app_profile.industry == 'media_production' %}Media Production
                    {% elif app_profile.industry == 'video_game_production' %}Video Game Production
                    {% else %}{{ app_profile.industry }}
                    {% endif %}
                  </p>
                </div>
              {% endif %}

              {% if app_profile.role %}
                <div class="col-md-6 mb-3">
                  <h6>{% trans "Role" %}</h6>
                  <p>
                    {% if app_profile.role == 'application_development' %}Application Development
                    {% elif app_profile.role == 'software_engineering' %}Software Engineering
                    {% elif app_profile.role == 'web_development' %}Web Development
                    {% elif app_profile.role == 'mobile_development' %}Mobile Development
                    {% elif app_profile.role == 'ux_ui_design' %}UX/UI Design
                    {% elif app_profile.role == 'qa_testing' %}Quality Assurance & Testing
                    {% elif app_profile.role == 'product_management' %}Product Management
                    {% elif app_profile.role == 'data_analysis' %}Data Analysis & Machine Learning
                    {% elif app_profile.role == 'devops_cloud' %}DevOps & Cloud Infrastructure
                    {% elif app_profile.role == 'fullstack_development' %}Fullstack Development
                    {% elif app_profile.role == 'frontend_development' %}Frontend Development
                    {% elif app_profile.role == 'backend_development' %}Backend Development
                    {% else %}{{ app_profile.role }}
                    {% endif %}
                  </p>
                </div>
              {% endif %}

              {% if app_profile.position_experience_years %}
                <div class="col-md-6 mb-3">
                  <h6>{% trans "Experience" %}</h6>
                  <p>{{ app_profile.position_experience_years }} {% trans "years" %}</p>
                </div>
              {% endif %}

              {% if app_profile.languages %}
                <div class="col-md-6 mb-3">
                  <h6>{% trans "Languages" %}</h6>
                  <p>{{ app_profile.languages }}</p>
                </div>
              {% endif %}

              {% if applications %}
                {% with latest_application=applications.0 %}
                  {% if latest_application.raw_data.dream_job %}
                    <div class="col-md-12 mb-3">
                      <h6>{% trans "Dream Job" %}</h6>
                      <p>{{ latest_application.raw_data.dream_job }}</p>
                    </div>
                  {% endif %}

                  {% if latest_application.raw_data.current_skills %}
                    <div class="col-md-12 mb-3">
                      <h6>{% trans "Skills" %}</h6>
                      <p>{{ latest_application.raw_data.current_skills }}</p>
                    </div>
                  {% endif %}

                  {% if latest_application.raw_data.remote_work or latest_application.raw_data.willing_to_relocate %}
                    <div class="col-md-12 mb-3">
                      <h6>{% trans "Work Preferences" %}</h6>
                      <ul class="list-unstyled">
                        {% if latest_application.raw_data.remote_work %}
                          <li>
                            <i class="bi bi-laptop me-2"></i>
                            {% if latest_application.raw_data.remote_work == 'yes' %}
                              {% trans "Open to remote work" %}
                            {% elif latest_application.raw_data.remote_work == 'no' %}
                              {% trans "Prefers on-site work" %}
                            {% elif latest_application.raw_data.remote_work == 'maybe' %}
                              {% trans "May consider remote work" %}
                            {% endif %}
                          </li>
                        {% endif %}

                        {% if latest_application.raw_data.willing_to_relocate %}
                          <li>
                            <i class="bi bi-geo-alt me-2"></i>
                            {% if latest_application.raw_data.willing_to_relocate == 'yes' %}
                              {% trans "Willing to relocate" %}
                            {% elif latest_application.raw_data.willing_to_relocate == 'no' %}
                              {% trans "Not willing to relocate" %}
                            {% elif latest_application.raw_data.willing_to_relocate == 'maybe' %}
                              {% trans "May consider relocating" %}
                            {% endif %}
                          </li>
                        {% endif %}

                        {% if latest_application.raw_data.work_type %}
                          <li>
                            <i class="bi bi-briefcase me-2"></i>
                            {% trans "Interested in" %}:
                            {% if 'full_time' in latest_application.raw_data.work_type %}
                              {% trans "Full Time" %}
                            {% elif 'part_time' in latest_application.raw_data.work_type %}
                              {% trans "Part Time" %}
                            {% elif 'contract' in latest_application.raw_data.work_type %}
                              {% trans "Contract" %}
                            {% elif 'freelance' in latest_application.raw_data.work_type %}
                              {% trans "Freelance" %}
                            {% elif 'internship' in latest_application.raw_data.work_type %}
                              {% trans "Internship" %}
                            {% else %}
                              {{ latest_application.raw_data.work_type|join:", "|title }}
                            {% endif %}
                          </li>
                        {% endif %}
                      </ul>
                    </div>
                  {% endif %}
                {% endwith %}
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Application Status -->
        <div class="card shadow">
          <div class="card-header bg-light">
            <h5 class="card-title mb-0">{% trans "Application Status" %}</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <h6>{% trans "Case Number" %}</h6>
                <p>{{ user.applicant_profile.case_number|default:"Not assigned" }}</p>
              </div>

              <div class="col-md-6 mb-3">
                <h6>{% trans "Queue Position" %}</h6>
                <p>{{ user.applicant_profile.get_display_queue_position }}</p>
              </div>

              <div class="col-12">
                <a href="{% url 'application:status' %}" class="btn btn-outline-primary">
                  <i class="bi bi-file-earmark-text me-1"></i> {% trans "View Full Application Status" %}
                </a>
              </div>
            </div>
          </div>
        </div>
      {% elif user.user_type == 'service' %}
        <!-- Company Information -->
        <div class="card shadow mb-4">
          <div class="card-header bg-light">
            <h5 class="card-title mb-0">{% trans "Company Information" %}</h5>
          </div>
          <div class="card-body">
            <div class="row">
              {% if user.hiring_partner_profile.company_name %}
                <div class="col-md-6 mb-3">
                  <h6>{% trans "Company Name" %}</h6>
                  <p>{{ user.hiring_partner_profile.company_name }}</p>
                </div>
              {% endif %}

              {% if user.hiring_partner_profile.company_size %}
                <div class="col-md-6 mb-3">
                  <h6>{% trans "Company Size" %}</h6>
                  <p>{{ user.hiring_partner_profile.company_size }}</p>
                </div>
              {% endif %}

              {% if user.hiring_partner_profile.primary_location %}
                <div class="col-md-6 mb-3">
                  <h6>{% trans "Location" %}</h6>
                  <p>{{ user.hiring_partner_profile.primary_location }}</p>
                </div>
              {% endif %}

              {% if user.hiring_partner_profile.industry %}
                <div class="col-md-6 mb-3">
                  <h6>{% trans "Industry" %}</h6>
                  <p>{{ user.hiring_partner_profile.industry }}</p>
                </div>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Subscription Information -->
        <div class="card shadow">
          <div class="card-header bg-light">
            <h5 class="card-title mb-0">{% trans "Subscription Information" %}</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <h6>{% trans "Current Plan" %}</h6>
                <p>{% if user.hiring_partner_profile.subscription %}{{ user.hiring_partner_profile.subscription.plan }}{% else %}{% trans "No active subscription" %}{% endif %}</p>
              </div>

              <div class="col-md-6 mb-3">
                <h6>{% trans "Status" %}</h6>
                <p>{% if user.hiring_partner_profile.subscription %}{{ user.hiring_partner_profile.subscription.status }}{% else %}{% trans "Inactive" %}{% endif %}</p>
              </div>

              <div class="col-12">
                <a href="{% url 'services:list' %}" class="btn btn-outline-primary">
                  <i class="bi bi-credit-card me-1"></i> {% trans "Manage Subscription" %}
                </a>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}