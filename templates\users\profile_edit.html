{% extends "common/base.html" %}
{% load i18n %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}Edit Profile | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h1 class="h3 mb-0">Edit Profile</h1>
        </div>
        <div class="card-body">
          <form method="POST" enctype="multipart/form-data">
            {% csrf_token %}

            <div class="row">
              <!-- Profile Photo -->
              <div class="col-md-4 mb-4">
                <div class="text-center">
                  {% if user.user_type == 'applicant' and user.applicant_profile.image %}
                    <img src="{{ user.applicant_profile.image.url }}" alt="{{ user.get_full_name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                  {% elif user.user_type == 'service' and user.hiring_partner_profile.profile_image %}
                    <img src="{{ user.hiring_partner_profile.profile_image.url }}" alt="{{ user.get_full_name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                  {% else %}
                    <img src="{% static 'images/default-avatar.png' %}" alt="{{ user.get_full_name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                  {% endif %}

                  <div class="mb-3">
                    {{ profile_form.image|as_crispy_field }}
                  </div>
                </div>
              </div>

              <!-- Basic Info -->
              <div class="col-md-8 mb-4">
                <h5>Basic Information</h5>
                <hr>

                <div class="row">
                  <div class="col-md-6">
                    {{ user_form.first_name|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ user_form.last_name|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ user_form.username|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ user_form.email|as_crispy_field }}
                  </div>
                </div>
              </div>

              <!-- Additional Profile Info -->
              <div class="col-12 mb-4">
                <h5>Additional Information</h5>
                <hr>

                <div class="row">
                  <div class="col-12">
                    {{ profile_form.bio|as_crispy_field }}
                  </div>
                </div>
              </div>

              {% if user.user_type == 'applicant' %}
              <!-- Professional Information -->
              <div class="col-12 mb-4">
                <h5>Professional Information</h5>
                <hr>

                <div class="row">
                  <div class="col-md-6">
                    {{ profile_form.industry|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.role|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.position_experience_years|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.languages|as_crispy_field }}
                  </div>
                </div>
              </div>
              {% endif %}

              {% if user.user_type == 'applicant' and user.is_staff %}
              <!-- Applicant-specific fields (only visible to staff) -->
              <div class="col-12 mb-4">
                <h5>Application Status (Staff Only)</h5>
                <hr>

                <div class="row">
                  <div class="col-md-6">
                    {{ profile_form.status|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.assignation|as_crispy_field }}
                  </div>
                </div>
              </div>
              {% endif %}

              {% if user.user_type == 'service' %}
              <!-- Hiring Partner Info -->
              <!-- Company Info -->
              <div class="col-12 mb-4">
                <h5>Company Information</h5>
                <hr>

                <div class="row">
                  <div class="col-md-6">
                    {{ profile_form.company_name|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.company_size|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.primary_location|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.branch|as_crispy_field }}
                  </div>
                  <div class="col-12">
                    {{ profile_form.other_locations|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.industry|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.work_location|as_crispy_field }}
                  </div>
                  <div class="col-12">
                    {{ profile_form.company_needs|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.average_wages|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.contract_types|as_crispy_field }}
                  </div>
                  <div class="col-md-6">
                    {{ profile_form.working_hours|as_crispy_field }}
                  </div>
                </div>
              </div>
              {% endif %}
            </div>

            <div class="d-flex justify-content-between mt-4">
              <a href="{% url 'users:profile' %}" class="btn btn-outline-secondary">Cancel</a>
              <button type="submit" class="btn btn-primary">Save Changes</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}