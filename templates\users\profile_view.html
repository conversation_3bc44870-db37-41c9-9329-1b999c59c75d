{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ user.get_full_name }} | Profile{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <!-- Profile Sidebar -->
    <div class="col-lg-4 mb-4">
      <div class="card shadow">
        <div class="card-body text-center">
          {% if profile.avatar %}
            <img src="{{ profile.avatar.url }}" alt="{{ user.get_full_name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
          {% else %}
            <img src="{% static 'images/default-avatar.png' %}" alt="{{ user.get_full_name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
          {% endif %}
          
          <h3 class="h4 mb-1">{{ user.get_full_name }}</h3>
          <p class="text-muted mb-3">@{{ user.username }}</p>
          
          {% if user.is_applicant %}
            <span class="badge bg-primary mb-3">Applicant</span>
          {% elif user.is_service_provider %}
            <span class="badge bg-success mb-3">Service Provider</span>
          {% else %}
            <span class="badge bg-secondary mb-3">Standard User</span>
          {% endif %}
          
          <div class="d-grid gap-2 mt-4">
            <a href="{% url 'users:profile_edit' %}" class="btn btn-outline-primary">Edit Profile</a>
            <a href="{% url 'account_change_password' %}" class="btn btn-outline-secondary">Change Password</a>
          </div>
        </div>
      </div>
      
      <!-- Contact Information -->
      <div class="card shadow mt-4">
        <div class="card-header bg-light">
          <h5 class="card-title mb-0">Contact Information</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <small class="text-muted d-block">Email</small>
            <div>{{ user.email }}</div>
          </div>
          
          {% if profile.phone_number %}
          <div class="mb-3">
            <small class="text-muted d-block">Phone</small>
            <div>{{ profile.phone_number }}</div>
          </div>
          {% endif %}
          
          {% if profile.address %}
          <div class="mb-0">
            <small class="text-muted d-block">Address</small>
            <div>{{ profile.address }}</div>
            <div>{{ profile.city }}, {{ profile.state }} {{ profile.zip_code }}</div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
    
    <!-- Profile Content -->
    <div class="col-lg-8">
      <!-- Profile Details -->
      <div class="card shadow mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">Profile Details</h5>
          <span class="small text-muted">Member since {{ user.date_joined|date:"M Y" }}</span>
        </div>
        <div class="card-body">
          {% if user.is_applicant %}
            <!-- Applicant Profile -->
            <div class="row">
              <div class="col-md-6 mb-3">
                <small class="text-muted d-block">Full Name</small>
                <div>{{ user.get_full_name }}</div>
              </div>
              
              {% if profile.date_of_birth %}
              <div class="col-md-6 mb-3">
                <small class="text-muted d-block">Date of Birth</small>
                <div>{{ profile.date_of_birth }}</div>
              </div>
              {% endif %}
              
              <div class="col-12">
                <hr>
                <h6>Application Status</h6>
                
                <div class="mt-3">
                  <div class="progress mb-2">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <small class="text-muted">Your application is 75% complete</small>
                </div>
                
                <div class="mt-4">
                  <a href="#" class="btn btn-primary">Continue Application</a>
                </div>
              </div>
            </div>
          {% elif user.is_service_provider %}
            <!-- Service Provider Profile -->
            <div class="row">
              <div class="col-md-6 mb-3">
                <small class="text-muted d-block">Full Name</small>
                <div>{{ user.get_full_name }}</div>
              </div>
              
              {% if profile.job_title %}
              <div class="col-md-6 mb-3">
                <small class="text-muted d-block">Job Title</small>
                <div>{{ profile.job_title }}</div>
              </div>
              {% endif %}
              
              {% if profile.company_name %}
              <div class="col-md-6 mb-3">
                <small class="text-muted d-block">Company</small>
                <div>{{ profile.company_name }}</div>
              </div>
              {% endif %}
              
              {% if profile.company_website %}
              <div class="col-md-6 mb-3">
                <small class="text-muted d-block">Website</small>
                <div><a href="{{ profile.company_website }}" target="_blank">{{ profile.company_website }}</a></div>
              </div>
              {% endif %}
              
              {% if profile.industry %}
              <div class="col-md-6 mb-3">
                <small class="text-muted d-block">Industry</small>
                <div>{{ profile.industry }}</div>
              </div>
              {% endif %}
              
              {% if profile.services_offered %}
              <div class="col-12">
                <small class="text-muted d-block">Services Offered</small>
                <div>{{ profile.services_offered }}</div>
              </div>
              {% endif %}
            </div>
          {% else %}
            <!-- Standard User Profile -->
            <div class="row">
              <div class="col-md-6 mb-3">
                <small class="text-muted d-block">Full Name</small>
                <div>{{ user.get_full_name }}</div>
              </div>
              
              <div class="col-12">
                <hr>
                <p>Complete your profile to access more features.</p>
                <div class="mt-3">
                  <a href="{% url 'users:profile_edit' %}" class="btn btn-primary">Complete Profile</a>
                </div>
              </div>
            </div>
          {% endif %}
        </div>
      </div>
      
      <!-- Activity Overview -->
      <div class="card shadow">
        <div class="card-header bg-light">
          <h5 class="card-title mb-0">Recent Activity</h5>
        </div>
        <div class="card-body">
          {% if activities %}
            <ul class="list-group list-group-flush">
              {% for activity in activities %}
                <li class="list-group-item px-0">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h6 class="mb-1">{{ activity.title }}</h6>
                      <p class="mb-1 text-muted small">{{ activity.description }}</p>
                    </div>
                    <small class="text-muted">{{ activity.timestamp|date:"M d, Y" }}</small>
                  </div>
                </li>
              {% endfor %}
            </ul>
          {% else %}
            <div class="text-center py-4">
              <i class="bi bi-calendar3 fs-1 text-muted"></i>
              <p class="mt-2">No recent activity found.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} 