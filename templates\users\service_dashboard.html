{% extends 'common/base.html' %}

{% block title %}Hiring Partner Dashboard | Smarch{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1>Hiring Partner Dashboard</h1>
            <p class="lead">Welcome back, {{ user.get_full_name }}!</p>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Company Profile Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Company Profile</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="p-3 bg-light rounded-circle me-3">
                            <i class="bi bi-building fs-3"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Company</h6>
                            <h4 class="mb-0">{{ user.hiringpartnerprofile.company_name }}</h4>
                        </div>
                    </div>
                    <div class="mb-3">
                        <p class="mb-1"><strong>Location:</strong> {{ user.hiringpartnerprofile.company_location }}</p>
                        <p class="mb-0"><strong>Industry:</strong> {{ user.hiringpartnerprofile.industry }}</p>
                    </div>
                    <div class="d-grid">
                        <a href="{% url 'users:profile' %}" class="btn btn-outline-primary"><i class="bi bi-building me-1"></i> View Full Profile</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Your Subscription</h5>
                </div>
                <div class="card-body">
                    {% if user.hiringpartnerprofile.subscription_set.filter.is_active=True %}
                        {% with subscription=user.hiringpartnerprofile.subscription_set.filter.is_active=True.first %}
                            <div class="d-flex align-items-center mb-3">
                                <div class="p-3 bg-light rounded-circle me-3">
                                    <i class="bi bi-credit-card fs-3"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Current Plan</h6>
                                    <h4 class="mb-0">{{ subscription.service_offer.name }}</h4>
                                </div>
                            </div>
                            <p class="mb-1">
                                <strong>Price:</strong> ${{ subscription.service_offer.price }}/month
                            </p>
                            <p class="mb-0">
                                <strong>Renewal Date:</strong> {{ subscription.end_date|date:"F d, Y" }}
                            </p>
                            <div class="d-grid mt-3">
                                <a href="{% url 'services:account' %}" class="btn btn-outline-success">Manage Subscription</a>
                            </div>
                        {% endwith %}
                    {% else %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="p-3 bg-light rounded-circle me-3">
                                <i class="bi bi-exclamation-triangle fs-3"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Subscription Status</h6>
                                <h4 class="mb-0">No Active Plan</h4>
                            </div>
                        </div>
                        <p>Subscribe to start searching for applicants and sending contact requests.</p>
                        <div class="d-grid">
                            <a href="{% url 'services:index' %}" class="btn btn-success">Subscribe Now</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Contact Requests Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Contact Requests</h5>
                </div>
                <div class="card-body">
                    {% with sent_count=user.hiringpartnerprofile.contact_requests_sent.count pending_count=user.hiringpartnerprofile.contact_requests_sent.filter.status='pending'.count %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="p-3 bg-light rounded-circle me-3">
                                <i class="bi bi-envelope fs-3"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Contact Requests</h6>
                                <h4 class="mb-0">{{ sent_count }} Total</h4>
                            </div>
                        </div>

                        <div class="mb-3">
                            <p class="mb-1"><strong>Pending:</strong> {{ pending_count }}</p>
                            <p class="mb-0"><strong>Responded:</strong> {{ sent_count|sub:pending_count }}</p>
                        </div>

                        <div class="d-grid">
                            <a href="{% url 'contact:requests' %}" class="btn btn-outline-info">View Requests</a>
                        </div>
                    {% endwith %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Browser Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Applicant Browser</h5>
                </div>
                <div class="card-body">
                    {% if user.hiringpartnerprofile.subscription_set.filter.is_active=True %}
                        <p>Use our powerful browser to find the perfect candidates for your needs.</p>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h2 class="display-4">{{ applicant_count }}</h2>
                                        <p class="mb-0">Available Applicants</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h2 class="display-4">{{ saved_results_count }}</h2>
                                        <p class="mb-0">Saved Results</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <a href="{% url 'services:browser' %}" class="btn btn-primary">Open Browser</a>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <p>You need an active subscription to use the applicant browser.</p>
                            <a href="{% url 'services:index' %}" class="btn btn-sm btn-primary">Subscribe Now</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Calendar Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Upcoming Events</h5>
                </div>
                <div class="card-body">
                    {% if user.event_set.exists %}
                        <ul class="list-group list-group-flush">
                            {% for event in user.event_set.all|dictsortreversed:"start_time"|slice:":5" %}
                                <li class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">{{ event.title }}</h6>
                                            <small class="text-muted">{{ event.start_time|date:"F d, Y - g:i A" }}</small>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">{{ event.get_event_type_display }}</span>
                                    </div>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="mb-0">No upcoming events.</p>
                        </div>
                    {% endif %}
                    <div class="d-grid mt-3">
                        <a href="{% url 'events:calendar' %}" class="btn btn-outline-primary">View Calendar</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if user.hiringpartnerprofile.subscription_set.filter.service_offer__is_premium=True.filter.is_active=True %}
        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="card-title mb-0">Premium Insights</h5>
                    </div>
                    <div class="card-body">
                        <p>Access detailed analytics and insights about your recruitment efforts.</p>
                        <div class="d-grid">
                            <a href="{% url 'insights:index' %}" class="btn btn-dark">View Insights</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}