{% extends 'common/base.html' %}

{% block title %}{{ profile.company_name }} - Hiring Partner Profile{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-12 mb-4">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'core:home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Hiring Partner Profile</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Profile Header -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            {% if profile.profile_image %}
                                <img src="{{ profile.profile_image.url }}" alt="{{ profile.company_name }}" class="profile-image mb-3">
                            {% else %}
                                <img src="https://via.placeholder.com/150" alt="Default Profile" class="profile-image mb-3">
                            {% endif %}
                            {% if user == profile.user %}
                                <div class="d-grid">
                                    <a href="{% url 'users:profile_edit' %}" class="btn btn-primary">Edit Profile</a>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-9">
                            <h2>{{ profile.company_name }}</h2>
                            <p class="text-muted">Hiring Partner</p>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h5>Company Information</h5>
                                    <dl class="row">
                                        <dt class="col-sm-4">Size</dt>
                                        <dd class="col-sm-8">{{ profile.get_company_size_display }}</dd>

                                        <dt class="col-sm-4">Location</dt>
                                        <dd class="col-sm-8">{{ profile.primary_location }}</dd>

                                        <dt class="col-sm-4">Branch</dt>
                                        <dd class="col-sm-8">{{ profile.branch }}</dd>

                                        <dt class="col-sm-4">Industry</dt>
                                        <dd class="col-sm-8">{{ profile.industry }}</dd>
                                    </dl>
                                </div>
                                <div class="col-md-6">
                                    <h5>Work Information</h5>
                                    <dl class="row">
                                        <dt class="col-sm-4">Working Hours</dt>
                                        <dd class="col-sm-8">{{ profile.working_hours|default:"Not specified" }}</dd>

                                        <dt class="col-sm-4">Contract Types</dt>
                                        <dd class="col-sm-8">{{ profile.contract_types|default:"Not specified" }}</dd>

                                        <dt class="col-sm-4">Average Wages</dt>
                                        <dd class="col-sm-8">{{ profile.average_wages|default:"Not specified" }}</dd>

                                        <dt class="col-sm-4">Work Location</dt>
                                        <dd class="col-sm-8">{{ profile.get_work_location_display }}</dd>
                                    </dl>
                                </div>
                            </div>

                            {% if profile.other_locations %}
                                <div class="mb-3">
                                    <h5>Other Locations</h5>
                                    <p>{{ profile.other_locations }}</p>
                                </div>
                            {% endif %}

                            <div class="mb-3">
                                <h5>Company Needs</h5>
                                <p>{{ profile.company_needs }}</p>
                            </div>

                            {% if view_as_applicant %}
                                <div>
                                    <a href="{% url 'contact:view_requests' service_id=profile.id %}" class="btn btn-outline-primary">View Contact History</a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Information (for the service provider) -->
        {% if user == profile.user %}
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h3>Your Subscription</h3>
                    </div>
                    <div class="card-body">
                        {% if profile.subscription_set.filter(is_active=True).exists %}
                            {% with subscription=profile.subscription_set.filter(is_active=True).first %}
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Subscription Details</h5>
                                        <dl class="row">
                                            <dt class="col-sm-4">Plan</dt>
                                            <dd class="col-sm-8">{{ subscription.service_offer.name }}</dd>

                                            <dt class="col-sm-4">Price</dt>
                                            <dd class="col-sm-8">${{ subscription.service_offer.price }}/month</dd>

                                            <dt class="col-sm-4">Start Date</dt>
                                            <dd class="col-sm-8">{{ subscription.start_date }}</dd>

                                            <dt class="col-sm-4">End Date</dt>
                                            <dd class="col-sm-8">{{ subscription.end_date }}</dd>
                                        </dl>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Features</h5>
                                        <ul>
                                            {% for feature in subscription.service_offer.features %}
                                                <li>{{ feature }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="{% url 'services:account' %}" class="btn btn-outline-primary">Manage Subscription</a>
                                </div>
                            {% endwith %}
                        {% else %}
                            <div class="alert alert-warning">
                                <p>You don't have an active subscription.</p>
                                <a href="{% url 'services:index' %}" class="btn btn-primary">View Available Plans</a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}