{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Hiring Partner Registration" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h2 class="mb-0">{% trans "Hiring Partner Registration" %}</h2>
          <p class="text-white mb-0 mt-2"><small>{% trans "After registration, you'll be directed to choose a subscription plan" %}</small></p>
        </div>
        <div class="card-body">
          <form method="post" enctype="multipart/form-data">
            {% csrf_token %}

            <div class="alert alert-info mb-4">
              <p class="mb-0"><i class="bi bi-info-circle-fill me-2"></i> {% trans "Already have an account?" %} <a href="{% url 'account_login' %}">{% trans "Log in here" %}</a>.</p>
              <p class="mb-0 mt-2"><i class="bi bi-lightbulb-fill me-2"></i> {% trans "After signing up, you'll be directed to choose a subscription plan to access our hiring tools." %}</p>
            </div>

            <h3 class="mb-4">{% trans "Account Information" %}</h3>
            {% for field in user_form %}
              <div class="mb-3">
                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                {{ field }}
                {% if field.help_text %}
                  <div class="form-text">{{ field.help_text }}</div>
                {% endif %}
                {% if field.errors %}
                  <div class="alert alert-danger mt-1">
                    {{ field.errors }}
                  </div>
                {% endif %}
              </div>
            {% endfor %}

            <h3 class="mb-4 mt-5">{% trans "Company Information" %}</h3>
            {% for field in profile_form %}
              <div class="mb-3">
                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                {{ field }}
                {% if field.help_text %}
                  <div class="form-text">{{ field.help_text }}</div>
                {% endif %}
                {% if field.errors %}
                  <div class="alert alert-danger mt-1">
                    {{ field.errors }}
                  </div>
                {% endif %}
              </div>
            {% endfor %}

            <div class="d-grid gap-2 mt-5">
              <button type="submit" class="btn btn-primary btn-lg">{% trans "Register & Continue to Subscription" %}</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}