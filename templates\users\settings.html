{% extends 'common/base.html' %}
{% load static %}

{% block title %}Account Settings{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-md-12 mb-4">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'core:home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Account Settings</li>
                </ol>
            </nav>
            <h1 class="mb-4">Account Settings</h1>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Profile Completion</h5>
                    <div class="progress mb-3">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ completion_percentage }}%;"
                            aria-valuenow="{{ completion_percentage }}" aria-valuemin="0" aria-valuemax="100">
                            {{ completion_percentage }}%
                        </div>
                    </div>
                    <p class="card-text">
                        {% if completion_percentage < 50 %}
                            <span class="text-danger">Your profile is incomplete. Please complete your profile to get the most out of Smarch.</span>
                        {% elif completion_percentage < 80 %}
                            <span class="text-warning">Your profile is partially complete. Add more information to improve your visibility.</span>
                        {% else %}
                            <span class="text-success">Your profile is well-completed! This helps you get better matches.</span>
                        {% endif %}
                    </p>
                    <a href="{% url 'users:profile_edit' %}" class="btn btn-primary">Complete Your Profile</a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-3">
            <div class="list-group mb-4">
                <a href="#privacy" class="list-group-item list-group-item-action active" data-bs-toggle="list">Privacy</a>
                <a href="#notifications" class="list-group-item list-group-item-action" data-bs-toggle="list">Notifications</a>
                <a href="#password" class="list-group-item list-group-item-action" data-bs-toggle="list">Password</a>
                <a href="#account" class="list-group-item list-group-item-action" data-bs-toggle="list">Account Status</a>
            </div>
        </div>

        <div class="col-md-9">
            <div class="tab-content">
                <div class="tab-pane fade show active" id="privacy">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Privacy Settings</h5>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Profile Visibility</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="profileVisibility" id="public" checked>
                                        <label class="form-check-label" for="public">
                                            Public - Your profile is visible to all users
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="profileVisibility" id="limited">
                                        <label class="form-check-label" for="limited">
                                            Limited - Your profile is only visible to service providers
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="profileVisibility" id="private">
                                        <label class="form-check-label" for="private">
                                            Private - Your profile is only visible to you and staff
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Data Usage</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="allowDataAnalytics" checked>
                                        <label class="form-check-label" for="allowDataAnalytics">
                                            Allow Smarch to use my data for improving services
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="allowMarketingEmails" checked>
                                        <label class="form-check-label" for="allowMarketingEmails">
                                            Receive marketing communications
                                        </label>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">Save Privacy Settings</button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="notifications">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Notification Settings</h5>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Email Notifications</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notifyApplicationUpdates" checked>
                                        <label class="form-check-label" for="notifyApplicationUpdates">
                                            Application status updates
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notifyNewMessages" checked>
                                        <label class="form-check-label" for="notifyNewMessages">
                                            New messages
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notifyServiceMatches" checked>
                                        <label class="form-check-label" for="notifyServiceMatches">
                                            New service matches
                                        </label>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">Save Notification Settings</button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="password">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Change Password</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <p>To change your password, please use our secure password reset system.</p>
                                <a href="{% url 'users:password_reset' %}" class="btn btn-primary mt-2">
                                    <i class="bi bi-key me-1"></i> Reset Password
                                </a>
                            </div>
                            <div class="mt-3">
                                <h6>Password Requirements:</h6>
                                <ul class="small">
                                    <li>Your password can't be too similar to your other personal information.</li>
                                    <li>Your password must contain at least 8 characters.</li>
                                    <li>Your password can't be a commonly used password.</li>
                                    <li>Your password can't be entirely numeric.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="account">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Account Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h5 class="alert-heading">Account Information</h5>
                                <p>Your account was created on <strong>{{ user.date_joined|date:"F d, Y" }}</strong></p>
                                <p>Account type: <strong>{{ user.get_user_type_display|default:"Standard" }}</strong></p>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#deactivateModal">
                                    Deactivate Account
                                </button>
                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                    Delete Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Deactivate Account Modal -->
<div class="modal fade" id="deactivateModal" tabindex="-1" aria-labelledby="deactivateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deactivateModalLabel">Deactivate Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to deactivate your account? Your profile will be hidden from other users but your data will be retained.</p>
                <form>
                    <div class="mb-3">
                        <label for="deactivateReason" class="form-label">Why are you leaving?</label>
                        <select class="form-select" id="deactivateReason">
                            <option selected>Please select a reason</option>
                            <option value="found_job">I found a job</option>
                            <option value="not_helpful">The service wasn't helpful</option>
                            <option value="too_many_emails">Too many emails/notifications</option>
                            <option value="privacy_concerns">Privacy concerns</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="additionalFeedback" class="form-label">Additional feedback (optional)</label>
                        <textarea class="form-control" id="additionalFeedback" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning">Deactivate Account</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Delete Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h5 class="alert-heading">Warning!</h5>
                    <p>Deleting your account is permanent and cannot be undone. All your data will be permanently removed.</p>
                </div>
                <p>To proceed with account deletion, you will need to confirm your decision on the next page.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="{% url 'users:delete_profile' %}" class="btn btn-danger">Continue to Delete Account</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enable delete button only when confirmation is correctly typed
    const deleteConfirmInput = document.getElementById('deleteConfirmation');
    const deleteButton = document.querySelector('#deleteModal .btn-danger');

    if (deleteConfirmInput && deleteButton) {
        deleteConfirmInput.addEventListener('input', function() {
            deleteButton.disabled = this.value !== 'DELETE';
        });
    }

    // Bootstrap tab initialization
    document.addEventListener('DOMContentLoaded', function() {
        const triggerTabList = [].slice.call(document.querySelectorAll('a[data-bs-toggle="list"]'));
        triggerTabList.forEach(function(triggerEl) {
            const tabTrigger = new bootstrap.Tab(triggerEl);
            triggerEl.addEventListener('click', function(event) {
                event.preventDefault();
                tabTrigger.show();
            });
        });
    });
</script>
{% endblock %}