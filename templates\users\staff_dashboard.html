{% extends 'common/base.html' %}

{% block title %}Staff Dashboard | Smarch{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1>Staff Dashboard</h1>
            <p class="lead">Welcome back, {{ user.get_full_name }}!</p>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Applicants Overview Card -->
        <div class="col-md-6 mb-4">
            <div class="card staff-dashboard-card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Applicants Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ applicant_count }}</h2>
                                    <p class="mb-0">Total</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ pending_count }}</h2>
                                    <p class="mb-0">Pending</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ in_progress_count }}</h2>
                                    <p class="mb-0">In Progress</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ completed_count }}</h2>
                                    <p class="mb-0">Completed</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h5>Recent Applications</h5>
                    {% if recent_applications %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Case #</th>
                                        <th>Queue #</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for profile in recent_applications %}
                                        <tr>
                                            <td>{{ profile.user.get_full_name }}</td>
                                            <td>{{ profile.case_number }}</td>
                                            <td>{{ profile.queue_number }}</td>
                                            <td>
                                                <span class="badge
                                                    {% if profile.status == 'pending' %}bg-warning
                                                    {% elif profile.status == 'in_progress' %}bg-info
                                                    {% elif profile.status == 'completed' %}bg-success
                                                    {% endif %}">
                                                    {{ profile.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{% url 'users:applicant_profile' pk=profile.pk %}" class="btn btn-sm btn-primary">View</a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">No recent applications.</div>
                    {% endif %}

                    <div class="d-grid">
                        <a href="{% url 'staff:applicants' %}" class="btn btn-primary">Manage Applicants</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Users Card -->
        <div class="col-md-6 mb-4">
            <div class="card staff-dashboard-card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Hiring Partners Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ service_user_count }}</h2>
                                    <p class="mb-0">Total</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ active_subscribers }}</h2>
                                    <p class="mb-0">Active</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ premium_subscribers }}</h2>
                                    <p class="mb-0">Premium</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h5>Recent Subscriptions</h5>
                    {% if recent_subscriptions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Company</th>
                                        <th>Plan</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subscription in recent_subscriptions %}
                                        <tr>
                                            <td>{{ subscription.hiring_partner.company_name }}</td>
                                            <td>{{ subscription.plan }}</td>
                                            <td>{{ subscription.start_date|date:"M d, Y" }}</td>
                                            <td>{{ subscription.end_date|date:"M d, Y" }}</td>
                                            <td>
                                                <a href="{% url 'users:service_profile' pk=subscription.hiring_partner.pk %}" class="btn btn-sm btn-primary">View</a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">No recent subscriptions.</div>
                    {% endif %}

                    <div class="d-grid">
                        <a href="{% url 'staff:service_user_list' %}" class="btn btn-primary">Manage Hiring Partners</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Smarch Panel Card -->
        <div class="col-md-12 mb-4">
            <div class="card staff-dashboard-card">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">Smarch Panel</h5>
                </div>
                <div class="card-body">
                    <p>Access the Smarch admin panel to manage and process applicants.</p>

                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ assigned_to_you }}</h2>
                                    <p class="mb-0">Assigned to You</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ completed_by_you }}</h2>
                                    <p class="mb-0">Completed by You</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ unassigned }}</h2>
                                    <p class="mb-0">Unassigned</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h2 class="display-4">{{ smarched }}</h2>
                                    <p class="mb-0">Smarched</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid">
                        <a href="{% url 'staff:smarch_panel' %}" class="btn btn-dark">Access Smarch Panel</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}