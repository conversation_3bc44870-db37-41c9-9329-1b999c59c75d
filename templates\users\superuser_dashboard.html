{% extends "common/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Admin Dashboard" %} | Smarch{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-4 mb-4">{% trans "Admin Dashboard" %}</h1>
            <p class="lead">{% trans "As a superuser, you can access all dashboards." %}</p>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="bi bi-person-badge fs-1 text-primary"></i>
                    </div>
                    <h3 class="card-title">{% trans "Applicant Dashboard" %}</h3>
                    <p class="card-text">{% trans "Access the applicant dashboard to view and manage your application." %}</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'users:dashboard' %}?dashboard_type=applicant" class="btn btn-primary">{% trans "Go to Applicant Dashboard" %}</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="bi bi-building fs-1 text-success"></i>
                    </div>
                    <h3 class="card-title">{% trans "Hiring Partner Dashboard" %}</h3>
                    <p class="card-text">{% trans "Access the hiring partner dashboard to manage your company profile and browse applicants." %}</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'users:dashboard' %}?dashboard_type=service" class="btn btn-success">{% trans "Go to Hiring Partner Dashboard" %}</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="bi bi-gear fs-1 text-danger"></i>
                    </div>
                    <h3 class="card-title">{% trans "Staff Dashboard" %}</h3>
                    <p class="card-text">{% trans "Access the staff dashboard to manage the platform and view administrative data." %}</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'users:dashboard' %}?dashboard_type=staff" class="btn btn-danger">{% trans "Go to Staff Dashboard" %}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% trans "Quick Links" %}</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="{% url 'admin:index' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            {% trans "Django Admin" %}
                            <i class="bi bi-box-arrow-up-right"></i>
                        </a>
                        <a href="{% url 'users:profile' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            {% trans "Your Profile" %}
                            <i class="bi bi-box-arrow-up-right"></i>
                        </a>
                        <a href="{% url 'application:index' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            {% trans "Application Portal" %}
                            <i class="bi bi-box-arrow-up-right"></i>
                        </a>
                        <a href="{% url 'services:index' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            {% trans "Services Portal" %}
                            <i class="bi bi-box-arrow-up-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">{% trans "System Status" %}</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "User Accounts" %}
                            <span class="badge bg-primary rounded-pill">{{ user_count|default:"N/A" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Applicants" %}
                            <span class="badge bg-success rounded-pill">{{ applicant_count|default:"N/A" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Hiring Partners" %}
                            <span class="badge bg-info rounded-pill">{{ hiring_partner_count|default:"N/A" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "Applications Submitted" %}
                            <span class="badge bg-warning rounded-pill">{{ application_count|default:"N/A" }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
