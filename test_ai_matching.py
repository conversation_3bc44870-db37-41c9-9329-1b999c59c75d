#!/usr/bin/env python
"""
Test script for AI-powered candidate matching system.
Run this script to verify the matching functionality works correctly.
"""

import os
import django
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smarch.settings')
django.setup()

from django.contrib.auth import get_user_model
from users.models import ApplicantProfile, HiringPartnerProfile
from services.models import Offer, JobRequirement
from services.ai_matching_service import CandidateMatchingService

User = get_user_model()

def create_test_data():
    """Create test data for AI matching."""
    print("Creating test data...")
    
    # Create test hiring partner
    hiring_partner_user, created = User.objects.get_or_create(
        username='test_recruiter',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Recruiter',
            'user_type': 'service'
        }
    )
    
    hiring_partner, created = HiringPartnerProfile.objects.get_or_create(
        user=hiring_partner_user,
        defaults={
            'company_name': 'Test Tech Company',
            'company_size': '50-100',
            'primary_location': 'San Francisco',
            'branch': 'Technology',
            'industry': 'Software Development',
            'company_description': 'A test technology company',
            'work_location': 'hybrid'
        }
    )
    
    # Create test job offer
    offer, created = Offer.objects.get_or_create(
        title='Senior Python Developer',
        hiring_partner=hiring_partner,
        defaults={
            'description': '''We are looking for a Senior Python Developer with experience in Django, 
            React, and AWS. The ideal candidate should have 3-5 years of experience in web development, 
            strong problem-solving skills, and experience with PostgreSQL. Remote work is allowed.
            Required skills: Python, Django, JavaScript, React, SQL.
            Preferred skills: AWS, Docker, Redis, Git.''',
            'price': 120000,
            'is_premium': False,
            'is_active': True
        }
    )
    
    # Create test candidates
    candidates_data = [
        {
            'username': 'candidate1',
            'email': '<EMAIL>',
            'first_name': 'Alice',
            'last_name': 'Johnson',
            'profile_data': {
                'role': 'Python Developer',
                'industry': 'Software Development',
                'location': 'San Francisco',
                'position_experience_years': 4,
                'bio': 'Experienced Python developer with Django and React skills',
                'languages': 'English, Spanish'
            }
        },
        {
            'username': 'candidate2',
            'email': '<EMAIL>',
            'first_name': 'Bob',
            'last_name': 'Smith',
            'profile_data': {
                'role': 'Full Stack Developer',
                'industry': 'Technology',
                'location': 'New York',
                'position_experience_years': 2,
                'bio': 'Junior developer with Python and JavaScript experience',
                'languages': 'English'
            }
        },
        {
            'username': 'candidate3',
            'email': '<EMAIL>',
            'first_name': 'Carol',
            'last_name': 'Davis',
            'profile_data': {
                'role': 'Senior Software Engineer',
                'industry': 'Software Development',
                'location': 'Remote',
                'position_experience_years': 7,
                'bio': 'Senior developer with Python, Django, AWS, and Docker experience',
                'languages': 'English, French, German'
            }
        }
    ]
    
    for candidate_data in candidates_data:
        user, created = User.objects.get_or_create(
            username=candidate_data['username'],
            defaults={
                'email': candidate_data['email'],
                'first_name': candidate_data['first_name'],
                'last_name': candidate_data['last_name'],
                'user_type': 'applicant'
            }
        )
        
        profile, created = ApplicantProfile.objects.get_or_create(
            user=user,
            defaults=candidate_data['profile_data']
        )
    
    print(f"✅ Created hiring partner: {hiring_partner.company_name}")
    print(f"✅ Created job offer: {offer.title}")
    print(f"✅ Created {len(candidates_data)} test candidates")
    
    return hiring_partner, offer

def test_ai_matching():
    """Test the AI matching functionality."""
    print("\n" + "="*50)
    print("TESTING AI CANDIDATE MATCHING SYSTEM")
    print("="*50)
    
    # Create test data
    hiring_partner, offer = create_test_data()
    
    # Initialize matching service
    print(f"\nInitializing AI matching service for {hiring_partner.company_name}...")
    matching_service = CandidateMatchingService(hiring_partner)
    
    # Test job requirements extraction
    print(f"\nTesting job requirements extraction for '{offer.title}'...")
    requirements = matching_service._get_or_create_job_requirements(offer)
    print(f"✅ Extracted {len(requirements.required_skills)} required skills: {requirements.required_skills}")
    print(f"✅ Extracted {len(requirements.preferred_skills)} preferred skills: {requirements.preferred_skills}")
    print(f"✅ Min experience: {requirements.min_experience_years} years")
    print(f"✅ Remote work allowed: {requirements.remote_work_allowed}")
    
    # Test candidate matching
    print(f"\nFinding matching candidates...")
    matched_candidates = matching_service.find_matching_candidates(offer, limit=10)
    
    print(f"\n🎯 Found {len(matched_candidates)} matching candidates:")
    print("-" * 60)
    
    for i, candidate_data in enumerate(matched_candidates, 1):
        candidate = candidate_data['candidate']
        print(f"\n{i}. {candidate.user.get_full_name()}")
        print(f"   Role: {candidate.role}")
        print(f"   Location: {candidate.location}")
        print(f"   Experience: {candidate.position_experience_years} years")
        print(f"   Overall Score: {candidate_data['overall_score']}% ({candidate_data['quality_rating']})")
        print(f"   Skills Score: {candidate_data['skills_score']}%")
        print(f"   Experience Score: {candidate_data['experience_score']}%")
        print(f"   Location Score: {candidate_data['location_score']}%")
        print(f"   Language Score: {candidate_data['language_score']}%")
        print(f"   Matched Skills: {candidate_data['matched_skills']}")
        print(f"   Missing Skills: {candidate_data['missing_skills']}")
        
        # Save score to database
        score = matching_service.save_candidate_score(candidate_data)
        print(f"   ✅ Score saved to database (ID: {score.id})")
    
    # Test matching criteria
    print(f"\n📊 Testing matching criteria...")
    criteria = matching_service.matching_criteria
    print(f"   Default Skills Weight: {criteria.default_skills_weight}%")
    print(f"   Default Experience Weight: {criteria.default_experience_weight}%")
    print(f"   Default Location Weight: {criteria.default_location_weight}%")
    print(f"   Default Language Weight: {criteria.default_language_weight}%")
    print(f"   Strict Skill Matching: {criteria.strict_skill_matching}")
    print(f"   Allow Junior Candidates: {criteria.allow_junior_candidates}")
    print(f"   Prioritize Local Candidates: {criteria.prioritize_local_candidates}")
    
    print(f"\n✅ AI Matching System Test Completed Successfully!")
    print(f"📈 System is ready for production use")
    
    return matched_candidates

def test_skills_extraction():
    """Test the skills extraction functionality."""
    print(f"\n🔍 Testing skills extraction...")
    
    test_descriptions = [
        "Looking for Python developer with Django and React experience",
        "Need Java developer with Spring Boot, MySQL, and AWS knowledge",
        "Seeking data scientist with machine learning, Python, and SQL skills",
        "Frontend developer needed with JavaScript, Vue.js, and CSS expertise"
    ]
    
    hiring_partner = HiringPartnerProfile.objects.first()
    matching_service = CandidateMatchingService(hiring_partner)
    
    for desc in test_descriptions:
        skills = matching_service._extract_skills_from_text(desc.lower())
        print(f"   '{desc}' → {skills}")
    
    print(f"✅ Skills extraction working correctly")

if __name__ == '__main__':
    try:
        test_ai_matching()
        test_skills_extraction()
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The AI-powered candidate matching system is working correctly.")
        print(f"\nNext steps:")
        print(f"1. Access the admin panel to view created data")
        print(f"2. Log in as a hiring partner to test the UI")
        print(f"3. Navigate to Offers → AI Matching to see the interface")
        print(f"4. Configure job requirements and test different scenarios")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
