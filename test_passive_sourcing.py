#!/usr/bin/env python
"""
Test script for passive candidate sourcing system.
Run this script to verify the passive sourcing functionality works correctly.
"""

import os
import django
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smarch.settings')
django.setup()

from django.contrib.auth import get_user_model
from users.models import HiringPartnerProfile
from services.models import PassiveCandidate, OutreachSequence, OutreachAttempt, Offer
from services.passive_sourcing_service import PassiveCandidateSourcingService

User = get_user_model()

def create_test_data():
    """Create test data for passive sourcing."""
    print("🎬 Creating Test Data for Passive Candidate Sourcing...")
    
    # Get or create hiring partner
    hiring_partner_user, created = User.objects.get_or_create(
        username='passive_recruiter',
        defaults={
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'user_type': 'service'
        }
    )
    
    if created:
        hiring_partner_user.set_password('demo123')
        hiring_partner_user.save()
    
    hiring_partner, created = HiringPartnerProfile.objects.get_or_create(
        user=hiring_partner_user,
        defaults={
            'company_name': 'InnovateTech Solutions',
            'company_size': '50-100',
            'primary_location': 'Austin, TX',
            'branch': 'Technology',
            'industry': 'Software Development',
            'company_description': 'Innovative technology company focused on AI and machine learning',
            'work_location': 'hybrid'
        }
    )
    
    # Create test job offer
    offer, created = Offer.objects.get_or_create(
        title='Senior AI Engineer',
        hiring_partner=hiring_partner,
        defaults={
            'description': '''We are seeking a Senior AI Engineer to join our cutting-edge team.
            
Required Skills:
- Python (5+ years)
- Machine Learning frameworks (TensorFlow, PyTorch)
- Deep Learning and Neural Networks
- Data Science and Analytics
- Cloud platforms (AWS, GCP)

Preferred Skills:
- Computer Vision
- Natural Language Processing
- MLOps and model deployment
- Docker and Kubernetes
- Research background

Experience: 5+ years in AI/ML
Location: Austin, TX (Hybrid)
Salary: $150,000 - $200,000''',
            'price': 175000,
            'is_premium': False,
            'is_active': True
        }
    )
    
    print(f"✅ Created hiring partner: {hiring_partner.company_name}")
    print(f"✅ Created job offer: {offer.title}")
    
    return hiring_partner, offer

def test_passive_candidate_discovery():
    """Test passive candidate discovery functionality."""
    print(f"\n🔍 Testing Passive Candidate Discovery...")
    
    hiring_partner, offer = create_test_data()
    sourcing_service = PassiveCandidateSourcingService(hiring_partner)
    
    # Test candidates data
    candidates_data = [
        {
            'first_name': 'Alex',
            'last_name': 'Chen',
            'email': '<EMAIL>',
            'phone': '+****************',
            'current_company': 'Google',
            'current_position': 'Senior ML Engineer',
            'industry': 'Technology',
            'location': 'San Francisco, CA',
            'experience_years': 6,
            'skills': ['Python', 'TensorFlow', 'PyTorch', 'Computer Vision', 'AWS'],
            'bio': 'Experienced ML engineer with expertise in computer vision and deep learning. Led multiple AI projects at scale.',
            'linkedin_url': 'https://linkedin.com/in/alexchen',
            'github_url': 'https://github.com/alexchen',
            'discovery_source': 'linkedin',
            'discovery_notes': 'Found through LinkedIn search for ML engineers in Bay Area'
        },
        {
            'first_name': 'Maria',
            'last_name': 'Rodriguez',
            'email': '<EMAIL>',
            'current_company': 'OpenAI',
            'current_position': 'Research Scientist',
            'industry': 'AI Research',
            'location': 'Remote',
            'experience_years': 8,
            'skills': ['Python', 'PyTorch', 'NLP', 'Transformers', 'Research'],
            'bio': 'AI researcher specializing in natural language processing and large language models.',
            'linkedin_url': 'https://linkedin.com/in/mariarodriguez',
            'discovery_source': 'conference',
            'discovery_notes': 'Met at NeurIPS conference, very impressive presentation on transformer architectures'
        },
        {
            'first_name': 'David',
            'last_name': 'Kim',
            'email': '<EMAIL>',
            'current_company': 'AI Startup Inc',
            'current_position': 'Lead Data Scientist',
            'industry': 'Artificial Intelligence',
            'location': 'Austin, TX',
            'experience_years': 4,
            'skills': ['Python', 'Scikit-learn', 'TensorFlow', 'MLOps', 'Docker'],
            'bio': 'Data scientist with strong MLOps background. Experience deploying ML models at scale.',
            'discovery_source': 'referral',
            'discovery_notes': 'Referred by current employee John Smith'
        },
        {
            'first_name': 'Emily',
            'last_name': 'Watson',
            'email': '<EMAIL>',
            'current_company': 'Stanford University',
            'current_position': 'PhD Candidate',
            'industry': 'Academia',
            'location': 'Palo Alto, CA',
            'experience_years': 2,
            'skills': ['Python', 'Research', 'Deep Learning', 'Computer Vision', 'Publications'],
            'bio': 'PhD candidate in Computer Science with focus on computer vision and autonomous systems.',
            'linkedin_url': 'https://linkedin.com/in/emilywatson',
            'discovery_source': 'github',
            'discovery_notes': 'Found through GitHub, impressive open source contributions'
        }
    ]
    
    # Test individual candidate discovery
    print(f"\n📝 Testing individual candidate discovery...")
    for candidate_data in candidates_data:
        candidate = sourcing_service.discover_candidate(candidate_data)
        print(f"   ✅ Added: {candidate.full_name} ({candidate.current_company})")
    
    # Test bulk import
    print(f"\n📦 Testing bulk candidate import...")
    bulk_candidates = [
        {
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'current_company': 'Microsoft',
            'current_position': 'Principal Engineer',
            'experience_years': 10,
            'skills': ['Python', 'Azure', 'Machine Learning'],
            'discovery_source': 'linkedin'
        },
        {
            'first_name': 'Jane',
            'last_name': 'Smith',
            'email': '<EMAIL>',
            'current_company': 'Meta',
            'current_position': 'AI Researcher',
            'experience_years': 7,
            'skills': ['PyTorch', 'NLP', 'Research'],
            'discovery_source': 'conference'
        }
    ]
    
    results = sourcing_service.bulk_import_candidates(bulk_candidates)
    print(f"   📊 Bulk import results: {results['created']} created, {results['updated']} updated")
    
    return hiring_partner, offer

def test_candidate_matching():
    """Test candidate matching for job offers."""
    print(f"\n🎯 Testing Candidate Matching...")
    
    hiring_partner, offer = create_test_data()
    sourcing_service = PassiveCandidateSourcingService(hiring_partner)
    
    # Find matching candidates
    matching_candidates = sourcing_service.find_candidates_for_offer(offer, limit=10)
    
    print(f"   🔍 Found {len(matching_candidates)} matching candidates for '{offer.title}':")
    
    for candidate in matching_candidates:
        print(f"      • {candidate.full_name} ({candidate.current_company}) - Score: {candidate.match_score}%")
        print(f"        Skills: {', '.join(candidate.skills[:3])}...")
        print(f"        Experience: {candidate.experience_years} years")
        print(f"        Status: {candidate.get_status_display()}")
        print()
    
    return matching_candidates

def test_outreach_sequences():
    """Test outreach sequence functionality."""
    print(f"\n📧 Testing Outreach Sequences...")
    
    hiring_partner, offer = create_test_data()
    sourcing_service = PassiveCandidateSourcingService(hiring_partner)
    
    # Create outreach sequence
    sequence_data = {
        'name': 'AI Engineer Outreach',
        'sequence_type': 'initial_contact',
        'is_active': True,
        'delay_days': 3,
        'max_attempts': 3,
        'subject_template': 'Exciting AI Engineer Opportunity at {company_name}',
        'message_template': '''Hi {first_name},

I hope this message finds you well. I came across your profile and was impressed by your experience in {current_position} at {current_company}.

We have an exciting opportunity for a Senior AI Engineer at {company_name} that I think would be a great fit for your background. The role involves working on cutting-edge AI projects with a talented team.

Would you be interested in learning more about this opportunity?

Best regards,
{recruiter_name}''',
        'target_roles': ['AI Engineer', 'ML Engineer', 'Data Scientist'],
        'target_industries': ['Technology', 'AI Research'],
        'min_experience_years': 3
    }
    
    sequence = sourcing_service.create_outreach_sequence(sequence_data)
    print(f"   ✅ Created outreach sequence: {sequence.name}")
    
    # Test starting outreach for candidates
    candidates = PassiveCandidate.objects.filter(discovered_by=hiring_partner)[:2]
    
    for candidate in candidates:
        attempt = sourcing_service.start_outreach_sequence(candidate, sequence)
        print(f"   📤 Started outreach for {candidate.full_name}")
        print(f"      Subject: {attempt.subject}")
        print(f"      Scheduled for: {attempt.scheduled_for}")
        print()
    
    return sequence

def test_analytics():
    """Test sourcing analytics."""
    print(f"\n📊 Testing Sourcing Analytics...")
    
    hiring_partner, offer = create_test_data()
    sourcing_service = PassiveCandidateSourcingService(hiring_partner)
    
    analytics = sourcing_service.get_sourcing_analytics()
    
    print(f"   📈 Sourcing Analytics for {hiring_partner.company_name}:")
    print(f"      Total Candidates: {analytics['total_candidates']}")
    print(f"      By Status: {analytics['by_status']}")
    print(f"      By Source: {analytics['by_source']}")
    print(f"      By Interest: {analytics['by_interest']}")
    print(f"      Average Experience: {analytics['avg_experience']:.1f} years")
    print(f"      Recent Discoveries (30 days): {analytics['recent_discoveries']}")
    print(f"      Conversion Rate: {analytics['conversion_rate']:.1f}%")
    
    return analytics

def run_all_tests():
    """Run all passive sourcing tests."""
    print("="*60)
    print("🚀 TESTING PASSIVE CANDIDATE SOURCING SYSTEM")
    print("="*60)
    
    try:
        # Test 1: Candidate Discovery
        test_passive_candidate_discovery()
        
        # Test 2: Candidate Matching
        matching_candidates = test_candidate_matching()
        
        # Test 3: Outreach Sequences
        sequence = test_outreach_sequences()
        
        # Test 4: Analytics
        analytics = test_analytics()
        
        print(f"\n✅ ALL TESTS PASSED!")
        print(f"🎉 Passive Candidate Sourcing System is working correctly!")
        
        print(f"\n📋 Summary:")
        print(f"   • Passive candidates can be discovered and stored")
        print(f"   • Bulk import functionality works")
        print(f"   • Candidate matching algorithm is functional")
        print(f"   • Outreach sequences can be created and started")
        print(f"   • Analytics provide useful insights")
        
        print(f"\n🔗 Next Steps:")
        print(f"   1. Login as 'passive_recruiter' (password: demo123)")
        print(f"   2. Navigate to 'Passive Candidates' in the dashboard")
        print(f"   3. View the discovered candidates")
        print(f"   4. Test adding new candidates manually")
        print(f"   5. Explore the admin interface for advanced management")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    run_all_tests()
