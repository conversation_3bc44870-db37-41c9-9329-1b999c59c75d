from django.test import TestCase, LiveServerTestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os

User = get_user_model()

class AuthenticationTest(TestCase):
    """Integration tests for authentication using Django TestCase."""
    
    def setUp(self):
        """Set up test data."""
        self.register_url = reverse('account_signup')
        self.login_url = reverse('account_login')
        self.home_url = reverse('core:home')
        
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123'
        )
    
    def test_user_can_register(self):
        """Test that a user can register."""
        # Get the registration page
        response = self.client.get(self.register_url)
        self.assertEqual(response.status_code, 200)
        
        # Submit the registration form
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password1': 'newuserpassword123',
            'password2': 'newuserpassword123',
        }
        response = self.client.post(self.register_url, data, follow=True)
        
        # Check that the user was created
        self.assertTrue(User.objects.filter(username='newuser').exists())
        
        # Check that we were redirected to the home page
        self.assertRedirects(response, self.home_url)
    
    def test_user_can_login(self):
        """Test that a user can log in."""
        # Get the login page
        response = self.client.get(self.login_url)
        self.assertEqual(response.status_code, 200)
        
        # Submit the login form
        data = {
            'login': 'testuser',
            'password': 'testpassword123',
        }
        response = self.client.post(self.login_url, data, follow=True)
        
        # Check that we were redirected to the home page
        self.assertRedirects(response, self.home_url)
        
        # Check that the user is logged in
        self.assertTrue(response.context['user'].is_authenticated)


class SeleniumAuthTest(LiveServerTestCase):
    """End-to-end tests for authentication using Selenium."""
    
    @classmethod
    def setUpClass(cls):
        """Set up the test class."""
        super().setUpClass()
        
        # Set up headless Chrome
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Initialize the WebDriver
        try:
            cls.selenium = webdriver.Chrome(options=chrome_options)
        except Exception as e:
            print(f"Could not initialize Chrome WebDriver: {e}")
            cls.selenium = None
    
    @classmethod
    def tearDownClass(cls):
        """Tear down the test class."""
        if cls.selenium:
            cls.selenium.quit()
        super().tearDownClass()
    
    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = User.objects.create_user(
            username='seleniumuser',
            email='<EMAIL>',
            password='seleniumpassword123'
        )
    
    def test_login_with_selenium(self):
        """Test login flow with Selenium."""
        if not self.selenium:
            self.skipTest("Selenium WebDriver not available")
            
        # Open the login page
        self.selenium.get(f'{self.live_server_url}{reverse("account_login")}')
        
        # Fill in the login form
        username_input = self.selenium.find_element(By.NAME, 'login')
        username_input.send_keys('seleniumuser')
        
        password_input = self.selenium.find_element(By.NAME, 'password')
        password_input.send_keys('seleniumpassword123')
        
        # Submit the form
        self.selenium.find_element(By.XPATH, '//button[@type="submit"]').click()
        
        # Wait for the page to load
        WebDriverWait(self.selenium, 10).until(
            EC.url_to_be(f'{self.live_server_url}{reverse("core:home")}')
        )
        
        # Check that we're on the home page
        self.assertEqual(self.selenium.current_url, f'{self.live_server_url}{reverse("core:home")}')
