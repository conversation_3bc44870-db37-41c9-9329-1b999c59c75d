from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core import mail

from contact_management.models import ContactRequest, ContactMessage
from users.models import HiringPartnerProfile

User = get_user_model()


class ContactRequestFlowTest(TestCase):
    """Integration tests for the contact request flow."""
    
    def setUp(self):
        """Set up test data."""
        # Create a hiring partner user
        self.hiring_partner_user = User.objects.create_user(
            username='hiringpartner',
            email='<EMAIL>',
            password='partnerpassword123',
            first_name='Partner',
            last_name='User'
        )
        
        # Create a hiring partner profile
        self.hiring_partner = HiringPartnerProfile.objects.create(
            user=self.hiring_partner_user,
            company_name='Partner Company',
            industry='Technology',
            company_size='10-50',
            primary_location='New York',
            company_website='https://partner.com',
            services_offered='Software Development'
        )
        
        # Create a regular user
        self.user = User.objects.create_user(
            username='regularuser',
            email='<EMAIL>',
            password='userpassword123',
            first_name='Regular',
            last_name='User'
        )
        
        # URLs
        self.login_url = reverse('account_login')
        self.contact_request_url = reverse('contact_management:contact_request', args=[self.hiring_partner.id])
        self.contact_list_url = reverse('contact_management:contact_list')
        
        # Log in the regular user
        self.client.login(username='regularuser', password='userpassword123')
    
    def test_contact_request_submission(self):
        """Test submitting a contact request."""
        # Submit a contact request
        data = {
            'subject': 'Test Contact Request',
            'message': 'This is a test contact request message.',
        }
        response = self.client.post(self.contact_request_url, data, follow=True)
        
        # Check that the request was successful
        self.assertEqual(response.status_code, 200)
        
        # Check that the contact request was created
        self.assertTrue(ContactRequest.objects.filter(
            sender=self.user,
            recipient=self.hiring_partner_user,
            subject='Test Contact Request'
        ).exists())
        
        # Check that an email was sent
        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(mail.outbox[0].subject, 'New Contact Request: Test Contact Request')
        self.assertIn(self.user.email, mail.outbox[0].body)
    
    def test_contact_request_response(self):
        """Test responding to a contact request."""
        # Create a contact request
        contact_request = ContactRequest.objects.create(
            sender=self.user,
            recipient=self.hiring_partner_user,
            subject='Test Contact Request',
            message='This is a test contact request message.'
        )
        
        # Log in as the hiring partner
        self.client.logout()
        self.client.login(username='hiringpartner', password='partnerpassword123')
        
        # Respond to the contact request
        response_url = reverse('contact_management:contact_respond', args=[contact_request.id])
        data = {
            'message': 'This is a test response message.',
        }
        response = self.client.post(response_url, data, follow=True)
        
        # Check that the response was successful
        self.assertEqual(response.status_code, 200)
        
        # Check that the contact message was created
        self.assertTrue(ContactMessage.objects.filter(
            sender=self.hiring_partner_user,
            recipient=self.user,
            message='This is a test response message.'
        ).exists())
        
        # Check that an email was sent
        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(mail.outbox[0].subject, 'New Message from Partner User')
        self.assertIn(self.hiring_partner_user.email, mail.outbox[0].body)
