from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model

from users.models import HiringPartnerProfile
from services.models import Offer

User = get_user_model()


class SearchFlowTest(TestCase):
    """Integration tests for the search functionality."""
    
    def setUp(self):
        """Set up test data."""
        # Create hiring partner users
        self.partner1_user = User.objects.create_user(
            username='partner1',
            email='<EMAIL>',
            password='password123'
        )
        
        self.partner2_user = User.objects.create_user(
            username='partner2',
            email='<EMAIL>',
            password='password123'
        )
        
        # Create hiring partner profiles
        self.partner1 = HiringPartnerProfile.objects.create(
            user=self.partner1_user,
            company_name='Tech Solutions',
            industry='Technology',
            company_size='10-50',
            primary_location='New York',
            company_website='https://techsolutions.com',
            services_offered='Software Development'
        )
        
        self.partner2 = HiringPartnerProfile.objects.create(
            user=self.partner2_user,
            company_name='Design Studio',
            industry='Design',
            company_size='1-10',
            primary_location='San Francisco',
            company_website='https://designstudio.com',
            services_offered='UI/UX Design'
        )
        
        # Create service offers
        self.offer1 = Offer.objects.create(
            hiring_partner=self.partner1,
            title='Software Development Services',
            description='We offer custom software development services.',
            price=5000,
            duration='3 months',
            is_active=True
        )
        
        self.offer2 = Offer.objects.create(
            hiring_partner=self.partner2,
            title='UI/UX Design Services',
            description='Professional UI/UX design for your products.',
            price=3000,
            duration='1 month',
            is_active=True
        )
        
        # URLs
        self.search_url = reverse('services:search')
    
    def test_search_by_keyword(self):
        """Test searching services by keyword."""
        # Search for 'software'
        response = self.client.get(f'{self.search_url}?q=software')
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that the correct offer is in the results
        self.assertContains(response, 'Software Development Services')
        self.assertNotContains(response, 'UI/UX Design Services')
    
    def test_search_by_location(self):
        """Test searching services by location."""
        # Search for 'San Francisco'
        response = self.client.get(f'{self.search_url}?location=San+Francisco')
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that the correct offer is in the results
        self.assertContains(response, 'UI/UX Design Services')
        self.assertNotContains(response, 'Software Development Services')
    
    def test_search_by_industry(self):
        """Test searching services by industry."""
        # Search for 'Design' industry
        response = self.client.get(f'{self.search_url}?industry=Design')
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that the correct offer is in the results
        self.assertContains(response, 'UI/UX Design Services')
        self.assertNotContains(response, 'Software Development Services')
    
    def test_combined_search(self):
        """Test searching with multiple criteria."""
        # Search for 'development' in 'New York'
        response = self.client.get(f'{self.search_url}?q=development&location=New+York')
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that the correct offer is in the results
        self.assertContains(response, 'Software Development Services')
        self.assertNotContains(response, 'UI/UX Design Services')
