from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock

from users.models import HiringPartnerProfile
from services.models import Subscription, Payment
from services.stripe_service import StripeService

User = get_user_model()


class SubscriptionFlowTest(TestCase):
    """Integration tests for the subscription flow."""
    
    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123',
            first_name='Test',
            last_name='User'
        )
        
        # Create a hiring partner profile
        self.hiring_partner = HiringPartnerProfile.objects.create(
            user=self.user,
            company_name='Test Company',
            industry='Technology',
            company_size='10-50',
            primary_location='New York',
            company_website='https://example.com',
            services_offered='Software Development'
        )
        
        # URLs
        self.login_url = reverse('account_login')
        self.payment_url = reverse('services:payment')
        self.subscription_url = reverse('services:subscription')
        self.cancel_subscription_url = lambda id: reverse('services:cancel_subscription', args=[id])
        
        # Log in the user
        self.client.login(username='testuser', password='testpassword123')
    
    @patch('stripe.Customer.create')
    @patch('stripe.PaymentMethod.attach')
    @patch('stripe.Customer.modify')
    @patch('stripe.Subscription.create')
    def test_subscription_creation(self, mock_sub_create, mock_customer_modify, 
                                  mock_pm_attach, mock_customer_create):
        """Test the subscription creation flow."""
        # Set up the mocks
        mock_customer = MagicMock()
        mock_customer.id = 'cus_test123'
        mock_customer_create.return_value = mock_customer
        
        mock_subscription = MagicMock()
        mock_subscription.id = 'sub_test123'
        mock_subscription.latest_invoice = MagicMock()
        mock_subscription.latest_invoice.payment_intent = MagicMock()
        mock_subscription.latest_invoice.payment_intent.id = 'pi_test123'
        mock_sub_create.return_value = mock_subscription
        
        # Submit the payment form
        data = {
            'plan': 'basic',
            'stripeToken': 'tok_test123',
            'auto_renew': 'on'
        }
        response = self.client.post(self.payment_url, data, follow=True)
        
        # Check that we were redirected to the subscription page
        self.assertRedirects(response, self.subscription_url)
        
        # Check that the subscription was created
        self.assertTrue(Subscription.objects.filter(
            hiring_partner=self.hiring_partner,
            plan='basic',
            stripe_subscription_id='sub_test123'
        ).exists())
    
    @patch('stripe.Subscription.modify')
    def test_subscription_cancellation(self, mock_modify):
        """Test the subscription cancellation flow."""
        # Set up the mock
        mock_subscription = MagicMock()
        mock_subscription.id = 'sub_test123'
        mock_modify.return_value = mock_subscription
        
        # Create a subscription
        subscription = Subscription.objects.create(
            hiring_partner=self.hiring_partner,
            plan='basic',
            status='active',
            stripe_subscription_id='sub_test123'
        )
        
        # Cancel the subscription
        response = self.client.get(self.cancel_subscription_url(subscription.id), follow=True)
        
        # Check that we were redirected to the subscription page
        self.assertRedirects(response, self.subscription_url)
        
        # Check that the subscription was cancelled
        subscription.refresh_from_db()
        self.assertEqual(subscription.status, 'cancelled')
        
        # Check that the Stripe API was called
        mock_modify.assert_called_once_with(
            'sub_test123',
            cancel_at_period_end=True
        )
