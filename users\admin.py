from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, ApplicantProfile, HiringPartnerProfile

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ('email', 'username', 'first_name', 'last_name', 'user_type', 'is_staff')
    list_filter = ('user_type', 'is_staff', 'is_active')
    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        ('Personal Info', {'fields': ('username', 'first_name', 'last_name', 'user_type')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'password1', 'password2', 'user_type'),
        }),
    )
    search_fields = ('email', 'username', 'first_name', 'last_name')
    ordering = ('email',)


@admin.register(ApplicantProfile)
class ApplicantProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'case_number', 'queue_number', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('user__email', 'user__username', 'case_number')
    readonly_fields = ('case_number', 'queue_number', 'created_at', 'updated_at')


@admin.register(HiringPartnerProfile)
class HiringPartnerProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'company_name', 'company_size', 'primary_location', 'industry')
    list_filter = ('company_size', 'work_location', 'created_at')
    search_fields = ('user__email', 'company_name', 'primary_location', 'industry')
    readonly_fields = ('created_at', 'updated_at')
