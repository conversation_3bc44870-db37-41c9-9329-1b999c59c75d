from django import forms
from django.contrib.auth.forms import UserCreationForm, PasswordResetForm, SetPasswordForm
from .models import CustomUser, ApplicantProfile, HiringPartnerProfile


class CustomUserCreationForm(UserCreationForm):
    """
    Form for creating a new user with custom fields
    """
    email = forms.EmailField(required=True)
    username = forms.CharField(
        required=True,
        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
    )

    class Meta:
        model = CustomUser
        fields = ('email', 'username', 'first_name', 'last_name')

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.username = self.cleaned_data['username']
        if commit:
            user.save()
        return user


class UserUpdateForm(forms.ModelForm):
    """
    Form for updating user information
    """
    email = forms.EmailField()

    class Meta:
        model = CustomUser
        fields = ('email', 'username', 'first_name', 'last_name')


class ApplicantProfileForm(forms.ModelForm):
    """
    Form for updating applicant profile
    """
    class Meta:
        model = ApplicantProfile
        fields = (
            'image', 'bio', 'branch', 'age', 'date_of_birth', 'location',
            'industry', 'role', 'position_experience_years', 'gender',
            'work_permit', 'languages', 'phone_number', 'address', 'city',
            'state', 'zip_code'
        )
        widgets = {
            'date_of_birth': forms.DateInput(attrs={'type': 'date'}),
            'bio': forms.Textarea(attrs={'rows': 4}),
            'languages': forms.TextInput(attrs={'placeholder': 'e.g., English, Spanish, French'}),
            'industry': forms.TextInput(attrs={'placeholder': 'e.g., Technology, Healthcare, Finance'}),
            'role': forms.TextInput(attrs={'placeholder': 'e.g., Software Developer, Project Manager'}),
        }


class StaffApplicantProfileForm(forms.ModelForm):
    """
    Form for staff to update applicant profiles including status and assignation
    """
    class Meta:
        model = ApplicantProfile
        fields = (
            'image', 'bio', 'branch', 'age', 'date_of_birth', 'location',
            'industry', 'role', 'position_experience_years', 'gender',
            'work_permit', 'languages', 'phone_number', 'address', 'city',
            'state', 'zip_code', 'status', 'assignation', 'case_number', 'queue_number'
        )
        widgets = {
            'date_of_birth': forms.DateInput(attrs={'type': 'date'}),
            'bio': forms.Textarea(attrs={'rows': 4}),
            'languages': forms.TextInput(attrs={'placeholder': 'e.g., English, Spanish, French'}),
            'industry': forms.TextInput(attrs={'placeholder': 'e.g., Technology, Healthcare, Finance'}),
            'role': forms.TextInput(attrs={'placeholder': 'e.g., Software Developer, Project Manager'}),
        }


class HiringPartnerProfileForm(forms.ModelForm):
    """
    Form for updating hiring partner profile
    """
    class Meta:
        model = HiringPartnerProfile
        fields = (
            'company_name', 'company_size', 'profile_image', 'primary_location',
            'other_locations', 'branch', 'industry', 'company_description',
            'company_needs', 'average_wages', 'contract_types', 'working_hours',
            'work_location', 'phone_number', 'company_address', 'company_city',
            'company_state', 'company_zip', 'company_website', 'job_title',
            'services_offered'
        )
        widgets = {
            'company_description': forms.Textarea(attrs={'rows': 3}),
            'company_needs': forms.Textarea(attrs={'rows': 4}),
            'services_offered': forms.Textarea(attrs={'rows': 3}),
        }


class CustomPasswordResetForm(PasswordResetForm):
    """
    Custom password reset form with enhanced styling
    """
    email = forms.EmailField(
        label="Email",
        max_length=254,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your email address',
            'autocomplete': 'email'
        })
    )


class CustomSetPasswordForm(SetPasswordForm):
    """
    Custom set password form with enhanced styling
    """
    new_password1 = forms.CharField(
        label="New password",
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter new password',
            'autocomplete': 'new-password'
        }),
        strip=False,
    )
    new_password2 = forms.CharField(
        label="Confirm new password",
        strip=False,
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirm new password',
            'autocomplete': 'new-password'
        }),
    )
