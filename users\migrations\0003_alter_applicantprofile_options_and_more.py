# Generated by Django 4.2.7 on 2025-04-17 10:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_rename_serviceuserprofile_hiringpartnerprofile_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='applicantprofile',
            options={'verbose_name': 'Applicant Profile', 'verbose_name_plural': 'Applicant Profiles'},
        ),
        migrations.AlterModelOptions(
            name='hiringpartnerprofile',
            options={'verbose_name': 'Hiring Partner Profile', 'verbose_name_plural': 'Hiring Partner Profiles'},
        ),
        migrations.RenameField(
            model_name='hiringpartnerprofile',
            old_name='company_location',
            new_name='branch',
        ),
        migrations.RemoveField(
            model_name='hiringpartnerprofile',
            name='company_branch',
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='address',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='age',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='branch',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='date_of_birth',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='gender',
            field=models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other'), ('prefer_not_to_say', 'Prefer not to say')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='industry',
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='languages',
            field=models.CharField(blank=True, help_text='Comma-separated list of languages', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='location',
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='position_experience_years',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='role',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='state',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='work_permit',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='applicantprofile',
            name='zip_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='company_address',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='company_city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='company_description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='company_state',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='company_website',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='company_zip',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='job_title',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='primary_location',
            field=models.CharField(default='To be updated', max_length=150),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='profile_image',
            field=models.ImageField(blank=True, null=True, upload_to='hiring_partners/'),
        ),
        migrations.AddField(
            model_name='hiringpartnerprofile',
            name='services_offered',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='applicantprofile',
            name='bio',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='applicantprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='applicant_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='hiringpartnerprofile',
            name='average_wages',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='hiringpartnerprofile',
            name='company_name',
            field=models.CharField(max_length=200),
        ),
        migrations.AlterField(
            model_name='hiringpartnerprofile',
            name='company_needs',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='hiringpartnerprofile',
            name='company_size',
            field=models.CharField(choices=[('1-10', '1-10 employees'), ('11-50', '11-50 employees'), ('51-200', '51-200 employees'), ('201-500', '201-500 employees'), ('501-1000', '501-1000 employees'), ('1001+', '1001+ employees')], max_length=20),
        ),
        migrations.AlterField(
            model_name='hiringpartnerprofile',
            name='contract_types',
            field=models.CharField(help_text='E.g., full-time, part-time, contract, internship, etc.', max_length=100),
        ),
        migrations.AlterField(
            model_name='hiringpartnerprofile',
            name='industry',
            field=models.CharField(max_length=150),
        ),
        migrations.AlterField(
            model_name='hiringpartnerprofile',
            name='other_locations',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='hiringpartnerprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='hiring_partner_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='hiringpartnerprofile',
            name='work_location',
            field=models.CharField(choices=[('remote', 'Remote'), ('on-site', 'On-site'), ('hybrid', 'Hybrid')], default='on-site', max_length=20),
        ),
        migrations.AlterField(
            model_name='hiringpartnerprofile',
            name='working_hours',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]
