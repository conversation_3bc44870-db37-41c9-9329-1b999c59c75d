from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.urls import reverse

class CustomUser(AbstractUser):
    """
    Custom User model extending Django's AbstractUser
    """
    USER_TYPE_CHOICES = (
        ('applicant', 'Applicant'),
        ('service', 'Hiring Partner'),
        ('staff', 'Staff'),
    )

    email = models.EmailField(_('email address'), unique=True)
    user_type = models.CharField(
        max_length=10,
        choices=USER_TYPE_CHOICES,
        default='applicant'
    )

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    def __str__(self):
        return self.email

    def get_profile(self):
        if self.user_type == 'applicant':
            return self.applicant_profile
        elif self.user_type == 'service':
            return self.hiring_partner_profile
        return None


class ApplicantProfile(models.Model):
    """
    Profile for applicant users
    """
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name="applicant_profile")
    image = models.ImageField(upload_to='profile_images/', blank=True, null=True)
    bio = models.TextField(blank=True, null=True)
    # Fields from application.models.ApplicantProfile
    branch = models.CharField(max_length=100, blank=True, null=True)
    age = models.IntegerField(blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    location = models.CharField(max_length=150, blank=True, null=True)
    industry = models.CharField(max_length=150, blank=True, null=True)
    role = models.CharField(max_length=100, blank=True, null=True)
    position_experience_years = models.IntegerField(blank=True, null=True)
    gender = models.CharField(
        max_length=20,
        choices=[
            ('male', 'Male'),
            ('female', 'Female'),
            ('other', 'Other'),
            ('prefer_not_to_say', 'Prefer not to say')
        ],
        blank=True, null=True
    )
    work_permit = models.BooleanField(default=False)
    languages = models.CharField(max_length=255, blank=True, null=True,
                                help_text="Comma-separated list of languages")
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    zip_code = models.CharField(max_length=20, blank=True, null=True)
    # Original fields
    case_number = models.CharField(max_length=50, unique=True, blank=True, null=True)
    queue_number = models.IntegerField(unique=True, blank=True, null=True)
    status = models.CharField(max_length=15, choices=(
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
    ), default='pending')
    assignation = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Applicant Profile'
        verbose_name_plural = 'Applicant Profiles'

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.case_number}"

    def get_absolute_url(self):
        return reverse('users:applicant_profile', kwargs={'pk': self.pk})

    def save(self, *args, **kwargs):
        # Generate case number if not provided
        if not self.case_number:
            timestamp = timezone.now().strftime('%Y%m%d')
            applicant_count = ApplicantProfile.objects.count() + 1
            self.case_number = f"APP-{timestamp}-{applicant_count:04d}"

        # Generate queue number if not provided
        if not self.queue_number:
            last_queue = ApplicantProfile.objects.order_by('-queue_number').first()
            if last_queue:
                self.queue_number = last_queue.queue_number + 1
            else:
                self.queue_number = 1

        super().save(*args, **kwargs)

    def get_display_queue_position(self):
        """Get the display queue position (1-based ranking)."""
        if self.queue_number is None:
            return "Pending"

        # Find all profiles with a lower (more negative) queue number
        # These are the profiles with higher priority
        higher_priority_count = ApplicantProfile.objects.filter(
            queue_number__lt=self.queue_number
        ).count()

        # Queue position is 1-based, so add 1 to the count
        return higher_priority_count + 1

    def update_queue_position(self, unemployed_since=None, registered_unemployed_date=None, job_search_duration=None):
        """Update queue position based on unemployment duration."""
        from datetime import datetime, date
        from django.utils import timezone

        # If we have a registered unemployed date, use that for priority
        if registered_unemployed_date:
            if isinstance(registered_unemployed_date, str):
                try:
                    registered_unemployed_date = datetime.strptime(registered_unemployed_date, '%Y-%m-%d').date()
                except ValueError:
                    registered_unemployed_date = None

            if registered_unemployed_date:
                # Calculate days unemployed
                today = date.today()
                days_unemployed = (today - registered_unemployed_date).days

                # Negative queue number - lower is higher priority
                # So someone unemployed for 100 days has higher priority than someone unemployed for 10 days
                self.queue_number = -days_unemployed
                self.save(update_fields=['queue_number'])
                return

        # If no unemployment data, use a default low priority
        if self.queue_number is None:
            # Get the lowest priority (highest number) currently in use
            lowest_priority = ApplicantProfile.objects.exclude(queue_number=None).order_by('queue_number').first()
            self.queue_number = 1000 if not lowest_priority else lowest_priority.queue_number + 100
            self.save(update_fields=['queue_number'])


class HiringPartnerProfile(models.Model):
    """
    Profile for hiring partners (companies)
    """
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name="hiring_partner_profile")
    company_name = models.CharField(max_length=200)
    company_size = models.CharField(
        max_length=20,
        choices=[
            ('1-10', '1-10 employees'),
            ('11-50', '11-50 employees'),
            ('51-200', '51-200 employees'),
            ('201-500', '201-500 employees'),
            ('501-1000', '501-1000 employees'),
            ('1001+', '1001+ employees')
        ]
    )
    profile_image = models.ImageField(upload_to='hiring_partners/', blank=True, null=True)
    primary_location = models.CharField(max_length=150)
    other_locations = models.JSONField(blank=True, null=True)
    branch = models.CharField(max_length=100)
    industry = models.CharField(max_length=150)
    company_description = models.TextField(blank=True, null=True)
    company_needs = models.TextField()
    average_wages = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    contract_types = models.CharField(
        max_length=100,
        help_text="E.g., full-time, part-time, contract, internship, etc."
    )
    working_hours = models.CharField(max_length=100, blank=True, null=True)
    work_location = models.CharField(
        max_length=20,
        choices=[
            ('remote', 'Remote'),
            ('on-site', 'On-site'),
            ('hybrid', 'Hybrid')
        ],
        default='on-site'
    )
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    company_address = models.CharField(max_length=255, blank=True, null=True)
    company_city = models.CharField(max_length=100, blank=True, null=True)
    company_state = models.CharField(max_length=100, blank=True, null=True)
    company_zip = models.CharField(max_length=20, blank=True, null=True)
    company_website = models.URLField(blank=True, null=True)
    job_title = models.CharField(max_length=100, blank=True, null=True)
    services_offered = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Hiring Partner Profile'
        verbose_name_plural = 'Hiring Partner Profiles'

    def __str__(self):
        return self.company_name

    def get_absolute_url(self):
        return reverse('users:hiring_partner_profile', kwargs={'pk': self.pk})
