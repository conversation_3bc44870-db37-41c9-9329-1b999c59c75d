from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import CustomUser, ApplicantProfile, HiringPartnerProfile


@receiver(post_save, sender=CustomUser)
def create_user_profile(sender, instance, created, **kwargs):
    """
    Create user profile when a new user is created based on their user_type
    """
    if created:
        if instance.user_type == 'applicant' and not hasattr(instance, 'applicant_profile'):
            ApplicantProfile.objects.create(user=instance)
        elif instance.user_type == 'service' and not hasattr(instance, 'hiring_partner_profile'):
            HiringPartnerProfile.objects.create(
                user=instance,
                company_name=f"{instance.get_full_name()}'s Company",
                company_size='1-10',
                primary_location='To be updated',
                branch='To be updated',
                industry='To be updated',
                company_needs='To be updated',
                work_location='on-site'
            )


@receiver(post_save, sender=CustomUser)
def save_user_profile(sender, instance, **kwargs):
    """
    Save the user profile whenever the user is saved
    """
    if instance.user_type == 'applicant':
        if hasattr(instance, 'applicant_profile'):
            instance.applicant_profile.save()
    elif instance.user_type == 'service':
        if hasattr(instance, 'hiring_partner_profile'):
            instance.hiring_partner_profile.save()
