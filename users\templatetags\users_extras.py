from django import template

register = template.Library()

@register.filter(name='sub')
def sub(value, arg):
    """Subtract the arg from the value."""
    try:
        return int(value) - int(arg)
    except (ValueError, TypeError):
        return value

@register.filter(name='abs')
def abs_filter(value):
    """Return the absolute value."""
    try:
        return abs(int(value))
    except (ValueError, TypeError):
        return value

@register.filter(name='mul')
def mul(value, arg):
    """Multiply the value by the arg."""
    try:
        return int(value) * int(arg)
    except (ValueError, TypeError):
        return value

@register.filter(name='div')
def div(value, arg):
    """Divide the value by the arg."""
    try:
        return int(value) / int(arg)
    except (ValueError, TypeError, ZeroDivisionError):
        return value

@register.filter(name='lt')
def lt(value, arg):
    """Check if value is less than arg."""
    try:
        return int(value) < int(arg)
    except (ValueError, TypeError):
        return False

@register.filter(name='gt')
def gt(value, arg):
    """Check if value is greater than arg."""
    try:
        return int(value) > int(arg)
    except (ValueError, TypeError):
        return False

@register.filter(name='lte')
def lte(value, arg):
    """Check if value is less than or equal to arg."""
    try:
        return int(value) <= int(arg)
    except (ValueError, TypeError):
        return False

@register.filter(name='gte')
def gte(value, arg):
    """Check if value is greater than or equal to arg."""
    try:
        return int(value) >= int(arg)
    except (ValueError, TypeError):
        return False