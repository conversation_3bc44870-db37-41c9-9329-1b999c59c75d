from django.urls import path
from django.contrib.auth import views as auth_views
from . import views
from .forms import CustomPasswordResetForm, CustomSetPasswordForm

app_name = 'users'

urlpatterns = [
    path('profile/', views.profile, name='profile'),
    path(
        'applicant-profile/<int:pk>/',
        views.applicant_profile_detail,
        name='applicant_profile'
    ),
    path(
        'hiring-partner-profile/<int:pk>/',
        views.service_profile_detail,
        name='hiring_partner_profile'
    ),
    path('profile/edit/', views.profile_edit, name='profile_edit'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('settings/', views.settings, name='settings'),
    path('delete-profile/', views.delete_profile, name='delete_profile'),
    path('service-signup/', views.service_signup, name='service_signup'),
    path('applicant-signup/', views.applicant_signup, name='applicant_signup'),

    # Password reset URLs
    path('password-reset/',
        auth_views.PasswordResetView.as_view(
            template_name='users/password_reset.html',
            form_class=CustomPasswordResetForm,
            email_template_name='users/password_reset_email.html',
            subject_template_name='users/password_reset_subject.txt',
            success_url='/users/password-reset/done/'
        ),
        name='password_reset'
    ),
    path('password-reset/done/',
        auth_views.PasswordResetDoneView.as_view(
            template_name='users/password_reset_done.html'
        ),
        name='password_reset_done'
    ),
    path('password-reset-confirm/<uidb64>/<token>/',
        auth_views.PasswordResetConfirmView.as_view(
            template_name='users/password_reset_confirm.html',
            form_class=CustomSetPasswordForm,
            success_url='/users/password-reset-complete/'
        ),
        name='password_reset_confirm'
    ),
    path('password-reset-complete/',
        auth_views.PasswordResetCompleteView.as_view(
            template_name='users/password_reset_complete.html'
        ),
        name='password_reset_complete'
    ),
]
