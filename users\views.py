from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.auth import login
from django.views.generic import RedirectView
from django.urls import reverse
from .models import ApplicantProfile, HiringPartnerProfile
from .forms import (
    UserUpdateForm, ApplicantProfileForm, HiringPartnerProfileForm,
    CustomUserCreationForm, StaffApplicantProfileForm
)


def calculate_applicant_profile_completion(profile):
    """
    Calculate the completion percentage of an applicant profile.

    Args:
        profile: The ApplicantProfile instance

    Returns:
        int: The completion percentage (0-100)
    """
    # Define the fields to check for completion
    fields_to_check = [
        'image', 'bio', 'branch', 'age', 'date_of_birth', 'location',
        'industry', 'role', 'position_experience_years', 'gender',
        'work_permit', 'languages', 'phone_number', 'address', 'city',
        'state', 'zip_code'
    ]

    # Count the number of completed fields
    completed_fields = 0
    for field in fields_to_check:
        value = getattr(profile, field, None)
        if value not in (None, '', [], {}):
            completed_fields += 1

    # Calculate the percentage
    total_fields = len(fields_to_check)
    completion_percentage = int((completed_fields / total_fields) * 100)

    return completion_percentage


def calculate_service_profile_completion(profile):
    """
    Calculate the completion percentage of a hiring partner profile.

    Args:
        profile: The HiringPartnerProfile instance

    Returns:
        int: The completion percentage (0-100)
    """
    # Define the fields to check for completion
    fields_to_check = [
        'company_name', 'company_size', 'profile_image', 'primary_location',
        'other_locations', 'branch', 'industry', 'company_description',
        'company_needs', 'average_wages', 'contract_types', 'working_hours',
        'work_location', 'phone_number', 'company_address', 'company_city',
        'company_state', 'company_zip', 'company_website', 'job_title',
        'services_offered'
    ]

    # Count the number of completed fields
    completed_fields = 0
    for field in fields_to_check:
        value = getattr(profile, field, None)
        if value not in (None, '', [], {}):
            completed_fields += 1

    # Calculate the percentage
    total_fields = len(fields_to_check)
    completion_percentage = int((completed_fields / total_fields) * 100)

    return completion_percentage


@login_required
def profile(request):
    """
    View for the user's own profile
    """
    user = request.user
    profile_type = request.GET.get('profile_type')

    # For superusers, automatically create profiles if they don't exist
    if user.is_superuser:
        # Create applicant profile if it doesn't exist
        try:
            _ = user.applicant_profile
        except ApplicantProfile.DoesNotExist:
            ApplicantProfile.objects.create(user=user)
            messages.success(request, 'An applicant profile has been automatically created for you as a superuser.')

        # Temporarily set user_type for routing if not set
        if not user.user_type:
            user.user_type = 'applicant'
            user.save(update_fields=['user_type'])

    # Check if the user has both profile types
    has_applicant_profile = hasattr(user, 'applicant_profile')
    has_hiring_partner_profile = hasattr(user, 'hiring_partner_profile')

    # If user has both profiles, use the profile_type parameter to determine which to show
    if has_applicant_profile and has_hiring_partner_profile:
        if profile_type == 'service':
            user.user_type = 'service'
            user.save(update_fields=['user_type'])
        elif profile_type == 'applicant':
            user.user_type = 'applicant'
            user.save(update_fields=['user_type'])
        # Otherwise, use the current user_type

    # Handle applicant profile view
    if user.user_type == 'applicant':
        if has_applicant_profile:
            # Try to get or create the application profile
            try:
                from application.models import ApplicantProfile as ApplicationProfile, ApplicationFormResponse
                app_profile, created = ApplicationProfile.objects.get_or_create(user=user)

                # Get the user's applications
                applications = ApplicationFormResponse.objects.filter(
                    applicant=app_profile
                ).order_by('-submitted_at')
            except ImportError:
                app_profile = None
                applications = []

            return render(request, 'users/profile.html', {
                'user': user,
                'app_profile': app_profile,
                'applications': applications,
                'has_applicant_profile': has_applicant_profile,
                'has_hiring_partner_profile': has_hiring_partner_profile
            })
        else:
            # If user has a hiring partner profile but no applicant profile
            if has_hiring_partner_profile:
                # Switch to hiring partner view
                user.user_type = 'service'
                user.save(update_fields=['user_type'])
                return redirect('users:profile')
            else:
                return redirect('users:applicant_signup')

    # Handle hiring partner profile view
    elif user.user_type == 'service':
        if has_hiring_partner_profile:
            # Check if the profile exists
            return render(request, 'users/profile.html', {
                'user': user,
                'app_profile': None,
                'applications': [],
                'has_applicant_profile': has_applicant_profile,
                'has_hiring_partner_profile': has_hiring_partner_profile
            })
        else:
            # If user has an applicant profile but no hiring partner profile
            if has_applicant_profile:
                # Switch to applicant view
                user.user_type = 'applicant'
                user.save(update_fields=['user_type'])
                return redirect('users:profile')
            else:
                return redirect('users:service_signup')

    # Default view for other user types
    return render(request, 'users/profile.html', {
        'user': user,
        'app_profile': None,
        'applications': [],
        'has_applicant_profile': has_applicant_profile,
        'has_hiring_partner_profile': has_hiring_partner_profile
    })


@login_required
def applicant_profile_detail(request, pk):
    """
    View for applicant profile details
    """
    # If the user is trying to view their own profile, redirect to dashboard
    if request.user.user_type == 'applicant' and hasattr(request.user, 'applicant_profile'):
        if request.user.applicant_profile.pk == pk:
            return redirect('users:dashboard')

    # Get the profile or return 404
    profile = get_object_or_404(ApplicantProfile, pk=pk)

    # For hiring partners viewing an applicant's profile
    view_as_hiring_partner = False
    if request.user.user_type == 'service' and request.user != profile.user:
        view_as_hiring_partner = True

    context = {
        'profile': profile,
        'view_as_hiring_partner': view_as_hiring_partner,
    }

    return render(request, 'users/applicant_profile_detail.html', context)


@login_required
def service_profile_detail(request, pk):
    """
    View for hiring partner profile details
    """
    # If the user is trying to view their own profile, redirect to dashboard
    if request.user.user_type == 'service' and hasattr(request.user, 'hiring_partner_profile'):
        if request.user.hiring_partner_profile.pk == pk:
            return redirect('users:dashboard')

    # Get the profile or return 404
    profile = get_object_or_404(HiringPartnerProfile, pk=pk)

    # For applicants viewing a hiring partner's profile
    view_as_applicant = False
    if request.user.user_type == 'applicant' and request.user != profile.user:
        view_as_applicant = True

    context = {
        'profile': profile,
        'view_as_applicant': view_as_applicant,
    }

    return render(request, 'users/service_profile_detail.html', context)


@login_required
def profile_edit(request):
    """
    View for editing user profile
    """
    user = request.user

    if request.method == 'POST':
        user_form = UserUpdateForm(request.POST, instance=user)

        if user.user_type == 'applicant':
            if user.is_staff:
                profile_form = StaffApplicantProfileForm(
                    request.POST,
                    request.FILES,
                    instance=user.applicant_profile
                )
            else:
                profile_form = ApplicantProfileForm(
                    request.POST,
                    request.FILES,
                    instance=user.applicant_profile
                )
        elif user.user_type == 'service':
            profile_form = HiringPartnerProfileForm(
                request.POST,
                request.FILES,
                instance=user.hiring_partner_profile
            )
        else:
            profile_form = None

        if user_form.is_valid() and (profile_form is None or profile_form.is_valid()):
            user_form.save()
            if profile_form:
                profile = profile_form.save()

                # Calculate profile completion percentage
                if user.user_type == 'applicant':
                    completion_percentage = calculate_applicant_profile_completion(profile)
                    messages.success(
                        request,
                        f'Your profile has been updated. Profile completion: {completion_percentage}%'
                    )
                elif user.user_type == 'service':
                    completion_percentage = calculate_service_profile_completion(profile)
                    messages.success(
                        request,
                        f'Your profile has been updated. Profile completion: {completion_percentage}%'
                    )
                else:
                    messages.success(request, 'Your profile has been updated.')
            else:
                messages.success(request, 'Your profile has been updated.')

            return redirect('users:profile')
        else:
            # Show form errors
            if user_form.errors:
                for field, error in user_form.errors.items():
                    messages.error(request, f"{field}: {error}")
            if profile_form and profile_form.errors:
                for field, error in profile_form.errors.items():
                    messages.error(request, f"{field}: {error}")
    else:
        user_form = UserUpdateForm(instance=user)

        if user.user_type == 'applicant':
            # Get the application profile data
            try:
                from application.models import ApplicantProfile as ApplicationProfile
                app_profile = ApplicationProfile.objects.get(user=user)
                initial_data = {
                    'industry': app_profile.industry,
                    'role': app_profile.role,
                    'position_experience_years': app_profile.position_experience_years,
                    'languages': app_profile.languages
                }
            except (ApplicationProfile.DoesNotExist, ImportError):
                initial_data = {}

            if user.is_staff:
                profile_form = StaffApplicantProfileForm(
                    instance=user.applicant_profile,
                    initial=initial_data
                )
            else:
                profile_form = ApplicantProfileForm(
                    instance=user.applicant_profile,
                    initial=initial_data
                )
        elif user.user_type == 'service':
            profile_form = HiringPartnerProfileForm(instance=user.hiring_partner_profile)
        else:
            profile_form = None

    # Get the appropriate profile based on user type
    if user.user_type == 'applicant':
        try:
            profile = user.applicant_profile
            completion_percentage = calculate_applicant_profile_completion(profile)
        except ApplicantProfile.DoesNotExist:
            profile = None
            completion_percentage = 0
    elif user.user_type == 'service':
        try:
            profile = user.hiring_partner_profile
            completion_percentage = calculate_service_profile_completion(profile)
        except HiringPartnerProfile.DoesNotExist:
            profile = None
            completion_percentage = 0
    else:
        profile = None
        completion_percentage = 0

    context = {
        'user_form': user_form,
        'profile_form': profile_form,
        'profile': profile,
        'completion_percentage': completion_percentage,
    }

    return render(request, 'users/profile_edit.html', context)


@login_required
def dashboard(request):
    """
    User dashboard view - redirects to appropriate dashboard based on user type
    """
    user = request.user
    dashboard_type = request.GET.get('dashboard_type')

    # For superusers, allow switching between dashboards
    if user.is_superuser:
        # Create profiles if they don't exist
        if not hasattr(user, 'applicant_profile'):
            from .models import ApplicantProfile
            ApplicantProfile.objects.create(user=user)

        if not hasattr(user, 'hiring_partner_profile'):
            from .models import HiringPartnerProfile
            HiringPartnerProfile.objects.create(
                user=user,
                company_name='Admin Company',
                company_size='1-10',
                primary_location='Admin Location',
                branch='Administration',
                industry='Technology',
                company_needs='Testing purposes'
            )

        # Use dashboard_type parameter to determine which dashboard to show
        if dashboard_type == 'service':
            context = {
                'is_superuser': True,
                'has_applicant_profile': True,
                'has_hiring_partner_profile': True
            }
            return render(request, 'users/service_dashboard.html', context)
        elif dashboard_type == 'applicant':
            context = {
                'is_superuser': True,
                'has_applicant_profile': True,
                'has_hiring_partner_profile': True
            }
            return render(request, 'users/applicant_dashboard.html', context)
        elif dashboard_type == 'staff':
            context = {
                'is_superuser': True,
                'has_applicant_profile': True,
                'has_hiring_partner_profile': True
            }
            return render(request, 'users/staff_dashboard.html', context)
        else:
            # Show a dashboard selector for superusers
            return render(request, 'users/superuser_dashboard.html', {
                'is_superuser': True,
                'has_applicant_profile': True,
                'has_hiring_partner_profile': True
            })

    # Regular users follow the normal flow
    if user.user_type == 'applicant':
        return render(request, 'users/applicant_dashboard.html')

    elif user.user_type == 'service':
        return render(request, 'users/service_dashboard.html')

    elif user.is_staff:
        return render(request, 'users/staff_dashboard.html')

    return redirect('users:profile')


@login_required
def settings(request):
    """
    View for user settings.
    """
    user = request.user

    # Get profile completion percentage
    if user.user_type == 'applicant' and hasattr(user, 'applicant_profile'):
        completion_percentage = calculate_applicant_profile_completion(user.applicant_profile)
    elif user.user_type == 'service' and hasattr(user, 'hiring_partner_profile'):
        completion_percentage = calculate_service_profile_completion(user.hiring_partner_profile)
    else:
        completion_percentage = 0

    context = {
        'completion_percentage': completion_percentage,
    }

    return render(request, 'users/settings.html', context)


@login_required
def delete_profile(request):
    """
    View for deleting user profile.
    """
    user = request.user

    if request.method == 'POST':
        # Verify password for security
        password = request.POST.get('password')
        if user.check_password(password):
            # Delete the user account
            user.delete()
            messages.success(request, "Your account has been deleted successfully.")
            return redirect('core:home')
        else:
            messages.error(request, "Incorrect password. Account deletion failed.")
            return redirect('users:settings')

    return render(request, 'users/delete_profile.html')


def service_signup(request):
    """
    View for hiring partner registration with integrated subscription process
    """
    # If user is already logged in, redirect to the services register page
    if request.user.is_authenticated:
        messages.info(request, "You're already logged in. Please complete your hiring partner profile.")
        return redirect('services:register')

    if request.method == 'POST':
        user_form = CustomUserCreationForm(request.POST)
        profile_form = HiringPartnerProfileForm(request.POST, request.FILES)

        if user_form.is_valid() and profile_form.is_valid():
            user = user_form.save(commit=False)
            user.user_type = 'service'
            user.save()

            profile = profile_form.save(commit=False)
            profile.user = user
            profile.save()

            # Log in the user
            login(request, user)

            messages.success(
                request,
                'Your hiring partner account has been created successfully.'
            )
            # Redirect to payment page instead of profile
            return redirect('services:payment')
    else:
        user_form = CustomUserCreationForm()
        profile_form = HiringPartnerProfileForm()

    context = {
        'user_form': user_form,
        'profile_form': profile_form,
    }

    return render(request, 'users/service_signup.html', context)


def applicant_signup(request):
    """
    View for applicant registration
    """
    # If user is already logged in, redirect to the profile page
    if request.user.is_authenticated:
        # Check if user already has an applicant profile
        if hasattr(request.user, 'applicant_profile'):
            messages.info(request, "You already have an applicant profile.")
        else:
            # Create an applicant profile for the user
            profile = ApplicantProfileForm().save(commit=False)
            profile.user = request.user
            profile.save()

            # Update user type
            request.user.user_type = 'applicant'
            request.user.save(update_fields=['user_type'])

            messages.success(request, "An applicant profile has been created for your account.")

        return redirect('users:profile')

    if request.method == 'POST':
        user_form = CustomUserCreationForm(request.POST)
        profile_form = ApplicantProfileForm(request.POST, request.FILES)

        if user_form.is_valid() and profile_form.is_valid():
            user = user_form.save(commit=False)
            user.user_type = 'applicant'
            user.save()

            profile = profile_form.save(commit=False)
            profile.user = user
            profile.save()

            # Log in the user
            login(request, user)

            messages.success(
                request,
                'Your applicant account has been created successfully.'
            )
            return redirect('users:profile')
    else:
        user_form = CustomUserCreationForm()
        profile_form = ApplicantProfileForm()

    context = {
        'user_form': user_form,
        'profile_form': profile_form,
    }

    return render(request, 'users/applicant_signup.html', context)


class SignupRedirectView(RedirectView):
    """
    Redirects the default allauth signup page to the appropriate signup page
    based on the 'user_type' query parameter. Defaults to applicant signup.
    """
    permanent = False

    def get_redirect_url(self, *args, **kwargs):
        user_type = self.request.GET.get('user_type', 'applicant')
        if user_type == 'service':
            return reverse('users:service_signup')
        return reverse('users:applicant_signup')
